// Copyright (C) 2021 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//       http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "device_generic_car_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["device_generic_car_license"],
}

// Emulator VehicleHAL implementation
cc_library_static {
    name: "android.hardware.automotive.vehicle@2.0-emulator-impl-lib",
    visibility: ["//hardware/interfaces/automotive/vehicle/2.0:__subpackages__"],
    vendor: true,
    defaults: ["vhal_v2_0_target_defaults"],
    cflags: ["-DENABLE_VENDOR_CLUSTER_PROPERTY_FOR_TESTING"],
    srcs: [
        "EmulatedVehicleConnector.cpp",
        "EmulatedVehicleHal.cpp",
        "EmulatedVehicleHalServer.cpp",
        "VehicleEmulator.cpp",
    ],
    header_libs: ["vhal_v2_0_common_headers"],
    whole_static_libs: [
        "android.hardware.automotive.vehicle@2.0-default-impl-lib",
    ],
    shared_libs: [
        "libbase",
        "libjsoncpp",
        "libprotobuf-cpp-lite",
        "device.generic.car.emulator-aidl-V1-ndk",
    ],
    export_include_dirs: ["."],
    static_libs: [
        "android.hardware.automotive.vehicle@2.0-libproto-native",
        "EmulatorCommConn",
        "EmulatorPipeComm",
        "EmulatorSocketComm",
    ],
}

cc_binary {
    name: "android.hardware.automotive.vehicle@2.0-emulator-service",
    defaults: ["vhal_v2_0_target_defaults"],
    vintf_fragments: [
        "<EMAIL>",
    ],
    init_rc: ["<EMAIL>"],
    vendor: true,
    relative_install_path: "hw",
    srcs: ["VehicleService.cpp"],
    shared_libs: [
        "libbase",
        "libjsoncpp",
        "libprotobuf-cpp-lite",
        "device.generic.car.emulator-aidl-V1-ndk",
    ],
    static_libs: [
        "android.hardware.automotive.vehicle@2.0-manager-lib",
        "android.hardware.automotive.vehicle@2.0-libproto-native",
        "android.hardware.automotive.vehicle@2.0-emulator-impl-lib",
        "EmulatorCommConn",
        "EmulatorPipeComm",
        "EmulatorSocketComm",
    ],
}

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "device_amlogic_yukawa_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    //   SPDX-license-identifier-GPL-2.0
    default_applicable_licenses: ["device_amlogic_yukawa_license"],
}

cc_library_shared {
    name: "hwcomposer.drm_meson",
    defaults: ["hwcomposer.drm_defaults"],
    srcs: [
        ":drm_hwcomposer_common",
        ":drm_hwcomposer_platformmeson",
    ],
}

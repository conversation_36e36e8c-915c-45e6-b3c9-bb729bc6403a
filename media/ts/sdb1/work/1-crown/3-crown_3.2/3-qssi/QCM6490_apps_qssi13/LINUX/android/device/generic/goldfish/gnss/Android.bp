/*
 * Copyright (C) 2020 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "device_generic_goldfish_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["device_generic_goldfish_license"],
}

cc_binary {
    name: "<EMAIL>",
    vendor: true,
    relative_install_path: "hw",
    init_rc: ["<EMAIL>"],
    vintf_fragments: ["<EMAIL>"],
    defaults: ["hidl_defaults"],
    srcs: [
        "agnss.cpp",
        "gnss_configuration.cpp",
        "gnss_measurement.cpp",
        "gnss_hw_conn.cpp",
        "gnss_hw_listener.cpp",
        "data_sink.cpp",
        "gnss.cpp",
        "main.cpp",
        "util.cpp",
    ],
    shared_libs: [
        "libbase",
        "libhidlbase",
        "liblog",
        "libutils",
        "android.hardware.gnss@2.0",
        "android.hardware.gnss@1.1",
        "android.hardware.gnss@1.0",
        "android.hardware.gnss.measurement_corrections@1.0",
        "android.hardware.gnss.visibility_control@1.0",
    ],
    static_libs: [
        "libqemud.ranchu",
    ],
    cflags: [
        "-DLOG_TAG=\"<EMAIL>\"",
        "-DANDROID_BASE_UNIQUE_FD_DISABLE_IMPLICIT_CONVERSION",
    ],
}

<?xml version="1.0" encoding="utf-8"?>
<!--
 * Copyright (c) 2021, The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
*/
-->
<resources>
    <dimen name="display_cutout_margin_consumption">0px</dimen>

    <!-- Height of the status bar header bar when on Keyguard (match status_bar_portrait) -->
    <dimen name="status_bar_header_height_keyguard">49dp</dimen>

    <!-- Margin end of the system icons super container when the avatar is missing. -->
    <dimen name="system_icons_super_container_avatarless_margin_end">10dp</dimen>

    <!-- end margin for multi user switch in collapsed quick settings -->
    <dimen name="multi_user_switch_keyguard_margin">7dp</dimen>

    <!-- Location on the screen of the center of the physical power button. -->
    <dimen name="physical_power_button_center_screen_location_y">780px</dimen>

    <!-- Location on the screen of the center of the physical volume up/down buttons. -->
    <dimen name="physical_volume_up_button_center_screen_location_y">1180px</dimen>
    <dimen name="physical_volume_down_button_center_screen_location_y">1380px</dimen>
</resources>

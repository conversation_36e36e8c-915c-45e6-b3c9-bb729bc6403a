// Copyright (C) 2022 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//       http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    default_applicable_licenses: ["device_generic_car_license"],
}

cc_library {
    name: "EmulatorSocketComm",
    vendor: true,
    srcs: [
        "*.cpp",
    ],
    shared_libs: [
        "liblog",
        "libprotobuf-cpp-lite",
    ],
    export_include_dirs: ["include"],
    static_libs: [
        "android.hardware.automotive.vehicle@2.0-libproto-native",
        "EmulatorCommConn",
    ],
}

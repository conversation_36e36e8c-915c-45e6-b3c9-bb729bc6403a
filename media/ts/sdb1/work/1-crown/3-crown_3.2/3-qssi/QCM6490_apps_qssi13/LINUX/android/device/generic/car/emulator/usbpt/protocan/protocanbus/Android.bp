//
// Copyright (C) 2021 Google Inc. All Rights Reserved.
//

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "device_generic_car_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["device_generic_car_license"],
}

cc_defaults {
    name: "android.device.generic.car.emulator@1.0-protocanbus-defaults",
    cpp_std: "experimental",
    cflags: [
        "-Wall",
        "-Wextra",
        "-Werror",
    ],
    shared_libs: [
        "libbase",
        "libutils",
    ],
}

cc_binary {
    name: "android.device.generic.car.emulator@1.0-protocanbus-service",
    init_rc: ["<EMAIL>"],
    defaults: ["android.device.generic.car.emulator@1.0-protocanbus-defaults"],
    vendor: true,
    relative_install_path: "hw",
    srcs: [
        "service.cpp",
        "CanClient.cpp",
        "CloseHandleWrapper.cpp",
        "ExtraCanClient.cpp",
        "VehicleBus.cpp",
    ],
    header_libs: [
        "android.hardware.automotive.can@hidl-utils-lib",
        "VehicleHalUtilHeaders"
    ],
    include_dirs: ["frameworks/native/include"],
    shared_libs: [
        "android.hardware.automotive.can@1.0",
        "device.generic.car.emulator-aidl-V1-ndk",
        "libbinder_ndk",
        "libhidlbase",
        "libcutils",
    ],
    whole_static_libs: [
        "libprotocan",
    ],
    static_libs: [
        "libmath",
    ],

    vintf_fragments: ["<EMAIL>"]
}

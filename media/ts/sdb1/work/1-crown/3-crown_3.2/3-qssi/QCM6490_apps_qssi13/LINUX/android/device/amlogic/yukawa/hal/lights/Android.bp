package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "device_amlogic_yukawa_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["device_amlogic_yukawa_license"],
}

cc_binary {
    name: "lights-yukawa",
    relative_install_path: "hw",
    init_rc: ["lights.rc"],
    vintf_fragments: ["lights.xml"],
    vendor: true,
    shared_libs: [
        "libbase",
        "libbinder_ndk",
        "android.hardware.light-V1-ndk",
    ],

    srcs: ["lights.cpp"],
}

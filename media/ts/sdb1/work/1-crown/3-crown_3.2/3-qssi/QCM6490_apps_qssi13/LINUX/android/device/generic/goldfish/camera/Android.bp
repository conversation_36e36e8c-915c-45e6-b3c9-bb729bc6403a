// Copyright (C) 2011 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "device_generic_goldfish_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["device_generic_goldfish_license"],
}

cc_library_shared {
    name: "camera.ranchu",
    vendor: true,
    relative_install_path: "hw",
    srcs: [
        "EmulatedCameraHal.cpp",
        "EmulatedCameraFactory.cpp",
        "EmulatedCameraHotplugThread.cpp",
        "EmulatedBaseCamera.cpp",
        "EmulatedCamera.cpp",
        "EmulatedCameraDevice.cpp",
        "EmulatedQemuCamera.cpp",
        "EmulatedQemuCameraDevice.cpp",
        "EmulatedFakeCamera.cpp",
        "EmulatedFakeCameraDevice.cpp",
        "EmulatedFakeRotatingCameraDevice.cpp",
        "Converters.cpp",
        "PreviewWindow.cpp",
        "CallbackNotifier.cpp",
        "QemuClient.cpp",
        "JpegCompressor.cpp",
        "EmulatedCamera2.cpp",
        "EmulatedFakeCamera2.cpp",
        "EmulatedQemuCamera2.cpp",
        "fake-pipeline2/Scene.cpp",
        "fake-pipeline2/Sensor.cpp",
        "fake-pipeline2/JpegCompressor.cpp",
        "EmulatedCamera3.cpp",
        "EmulatedFakeCamera3.cpp",
        "CameraRotator.cpp",
        "EmulatedFakeRotatingCamera3.cpp",
        "EmulatedQemuCamera3.cpp",
        "qemu-pipeline3/QemuSensor.cpp",
        "Exif.cpp",
        "Thumbnail.cpp",
        "WorkerThread.cpp",
    ],
    shared_libs: [
        "libbinder",
        "libexif",
        "liblog",
        "libutils",
        "libcutils",
        "libEGL",
        "libGLESv1_CM",
        "libGLESv2",
        "libui",
        "libdl",
        "libjpeg",
        "libcamera_metadata",
	"libqemupipe.ranchu",
    ],
    static_libs: [
        "libqemud.ranchu",
        "android.hardware.camera.common@1.0-helper",
        "libyuv_static",
    ],
    header_libs: [
        "libhardware_headers",
        "media_plugin_headers",
        "libgralloc_cb.ranchu",
    ],
    cflags: [
        "-Wno-unused-parameter",
        "-Wno-c++11-narrowing",
    ]
}

cc_binary {
    name: "emulatorcameratest",
    vendor: true,
    relative_install_path: "hw",
    srcs: ["EmulatorCameraTest.cpp"],
    shared_libs: [
        "camera.ranchu",
        "libcamera_metadata",
        "libui",
        "libutils",
    ],
    header_libs: [
        "libhardware_headers",
        "libgralloc_cb.ranchu",
    ],
    cflags: [
        "-Wno-unused-parameter",
    ]
}

<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2021 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<resources>
    <!--
    A collection of apps that are critical for the device and hence will never be disabled by
    device policies or APIs.
    -->
    <string-array translatable="false" name="vendor_policy_exempt_apps">
        <!-- This is just an example; on real products this list would
             contain apps like:
             - Rear-view camera
             - Driver assistance
             - Vehicle warnings
        -->
        <item>com.google.android.car.netdbug</item>
    </string-array>
</resources>

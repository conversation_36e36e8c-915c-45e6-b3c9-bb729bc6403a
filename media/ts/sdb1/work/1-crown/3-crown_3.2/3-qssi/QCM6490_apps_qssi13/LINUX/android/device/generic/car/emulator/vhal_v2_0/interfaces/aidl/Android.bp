// This is the expected build file, but it may not be right in all cases

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "device_generic_car_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["device_generic_car_license"],
}

aidl_interface {
    name: "device.generic.car.emulator-aidl",
    vendor_available: true,
    srcs: ["device/generic/car/emulator/*.aidl"],
    imports: [
        "android.hardware.automotive.vehicle",
    ],
    stability: "vintf",
    backend: {
        cpp: {
            enabled: false,
        },
        java: {
            enabled: false,
        },
        ndk: {
            enabled: true,
        },
    },
    versions_with_info: [
        {
            version: "1",
            imports: ["android.hardware.automotive.vehicle-V1"],
        },
    ],

}

#!/bin/sh -eu
#
# Copyright 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

tmp="$(mktemp -d)"
trap "rm -rf $tmp" EXIT
mkfifo $tmp/port

"$(dirname $0)/run_model_only" \
  -C bp.terminal_0.start_telnet=1 \
  -C bp.terminal_0.terminal_command="echo %port > $tmp/port" \
  "$@" &

read port < $tmp/port
telnet localhost $port
kill -INT %%
wait

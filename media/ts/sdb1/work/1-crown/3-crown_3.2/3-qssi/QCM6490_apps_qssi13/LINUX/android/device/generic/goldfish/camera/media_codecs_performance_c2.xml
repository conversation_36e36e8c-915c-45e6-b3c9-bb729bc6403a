<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright 2020 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<MediaCodecs>
    <Decoders>

        <MediaCodec name="c2.android.avc.decoder" type="video/avc" update="true">
            <Limit name="measured-frame-rate-320x240" range="486-504" /> <!-- N=50 v96%=1.3 -->
            <Limit name="measured-frame-rate-640x360" range="267-275" /> <!-- v90%=1.1 -->
            <Limit name="measured-frame-rate-720x480" range="248-248" /> <!-- v90%=1.1 -->
            <Limit name="measured-frame-rate-1280x720" range="107-108" /> <!-- v90%=1.0 -->
            <Limit name="measured-frame-rate-1920x1080" range="50-50" /> <!-- v90%=1.0 -->
        </MediaCodec>
        <MediaCodec name="c2.android.hevc.decoder" type="video/hevc" update="true">
            <Limit name="measured-frame-rate-352x288" range="469-485" /> <!-- v90%=1.1 -->
            <Limit name="measured-frame-rate-640x360" range="267-275" /> <!-- v90%=1.1 -->
            <Limit name="measured-frame-rate-720x480" range="248-248" /> <!-- v90%=1.1 -->
            <Limit name="measured-frame-rate-1280x720" range="103-105" /> <!-- v90%=1.0 -->
            <Limit name="measured-frame-rate-1920x1080" range="57-58" /> <!-- v90%=1.0 -->
        </MediaCodec>
        <MediaCodec name="c2.android.vp8.decoder" type="video/x-vnd.on2.vp8" update="true">
            <!-- measured 90%:799-924 med:815 N=12 -->
            <Limit name="measured-frame-rate-320x180" range="814-859" /> <!-- v90%=1.1 -->
            <!-- measured 90%:338-379 med:345 N=12 -->
            <Limit name="measured-frame-rate-640x360" range="344-358" /> <!-- v90%=1.1 -->
            <Limit name="measured-frame-rate-1280x720" range="88-92" /> <!-- N=50 v90%=1.1 -->
            <!-- measured 90%:35-40 med:36 N=12 -->
            <Limit name="measured-frame-rate-1920x1080" range="35-37" /> <!-- v90%=1.1 -->
        </MediaCodec>
        <MediaCodec name="c2.android.vp9.decoder" type="video/x-vnd.on2.vp9" update="true">
            <!-- measured 90%:621-650 med:634 N=12 -->
            <Limit name="measured-frame-rate-320x180" range="633-635" /> <!-- v90%=1.0 -->
            <!-- measured 90%:225-231 med:228 N=12 -->
            <Limit name="measured-frame-rate-640x360" range="228-228" /> <!-- v90%=1.0 -->
            <!-- measured 90%:91-94 med:93 N=12 -->
            <Limit name="measured-frame-rate-1280x720" range="92-93" /> <!-- v90%=1.0 -->
            <!-- measured 90%:56-58 med:57 N=12 -->
            <Limit name="measured-frame-rate-1920x1080" range="57-57" /> <!-- v90%=1.0 -->
        </MediaCodec>
        <MediaCodec name="c2.android.h263.decoder" type="video/3gpp" update="true">
            <!-- measured 90%:1219-1704 med:1479 N=12 -->
            <Limit name="measured-frame-rate-176x144" range="470-520" /> <!-- v90%=1.2 -->
            <!-- measured 96%:889-1227 med:922 SLOW -->
            <Limit name="measured-frame-rate-352x288" range="470-520" /> <!-- N=50 v96%=1.2 -->
        </MediaCodec>
        <MediaCodec name="c2.android.mpeg4.decoder" type="video/mp4v-es" update="true">
            <Limit name="measured-frame-rate-176x144" range="540-590" /> <!-- v90%=1.1 -->
            <Limit name="measured-frame-rate-480x360" range="430-615" /> <!-- v90%=1.0 -->
            <Limit name="measured-frame-rate-1280x720" range="171-230" /> <!-- v90%=1.0 -->
        </MediaCodec>
    </Decoders>

    <Encoders>

        <MediaCodec name="c2.android.h263.encoder" type="video/3gpp" update="true">
            <Limit name="measured-frame-rate-176x144" range="1400-1560" /> <!-- SHOULDN'T HAVE TWEAKED N=220 v90%=4.0 -->
        </MediaCodec>
        <MediaCodec name="c2.android.avc.encoder" type="video/avc" update="true">
            <Limit name="measured-frame-rate-320x240" range="500-600" /> <!-- Manual N=20 -->
            <Limit name="measured-frame-rate-720x480" range="200-250" /> <!-- Manual N=20 -->
            <Limit name="measured-frame-rate-1280x720" range="76-80" /> <!-- Manual N=20 -->
            <Limit name="measured-frame-rate-1920x1080" range="44-49" /> <!-- Manual N=20 -->
        </MediaCodec>
        <MediaCodec name="c2.android.hevc.encoder" type="video/hevc" update="true">
            <Limit name="measured-frame-rate-320x240" range="47-60" /> <!-- Manual N=20 -->
        </MediaCodec>
        <MediaCodec name="c2.android.mpeg4.encoder" type="video/mp4v-es" update="true">
            <Limit name="measured-frame-rate-176x144" range="1400-1560" /> <!-- SHOULDN'T HAVE TWEAKED N=220 v90%=4.0 -->
        </MediaCodec>
        <MediaCodec name="c2.android.vp8.encoder" type="video/x-vnd.on2.vp8" update="true">
            <Limit name="measured-frame-rate-320x180" range="300-400" /> <!-- Manual N=20 -->
            <Limit name="measured-frame-rate-640x360" range="200-300" /> <!-- Manual N=20 -->
            <Limit name="measured-frame-rate-1280x720" range="35-37" /> <!-- Manual N=20 -->
            <Limit name="measured-frame-rate-1920x1080" range="24-31" /> <!-- Manual N=20 -->
        </MediaCodec>
        <MediaCodec name="c2.android.vp9.encoder" type="video/x-vnd.on2.vp9" update="true">
            <Limit name="measured-frame-rate-320x180" range="109-109" /> <!-- v93%=1.3 -->
            <Limit name="measured-frame-rate-640x360" range="61-61" /> <!-- v95%=1.1 -->
            <Limit name="measured-frame-rate-1280x720" range="20-20" /> <!-- v95%=1.3 -->
        </MediaCodec>
    </Encoders>
</MediaCodecs>

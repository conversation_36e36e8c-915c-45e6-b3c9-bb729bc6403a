// Copyright (C) 2023 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
//

package {
    default_applicable_licenses: ["device_generic_car_license"],
}

runtime_resource_overlay {
    name: "CarServiceOverlayEmulatorMedia",
    resource_dirs: ["res"],
    manifest: "AndroidManifest.xml",
    sdk_version: "current",
    product_specific: true
}

override_runtime_resource_overlay {
    name: "CarServiceOverlayEmulatorMediaGoogle",
    base: "CarServiceOverlayEmulatorMedia",
    package_name: "com.google.android.car.resources.emulator.media",
    target_package_name: "com.google.android.car.updatable",
}

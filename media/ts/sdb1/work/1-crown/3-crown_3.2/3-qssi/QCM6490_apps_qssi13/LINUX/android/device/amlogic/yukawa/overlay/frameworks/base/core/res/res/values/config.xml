<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Copyright 2011, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<!-- These resources are around just to allow their values to be customized
     for different hardware and product builds. -->
<resources>

    <!-- This device is not "voice capable"; it's data-only. -->
    <bool name="config_voice_capable">false</bool>

    <!-- This device does not allow sms service. -->
    <bool name="config_sms_capable">false</bool>

    <!-- Separate software navigation bar required on this device. -->
    <bool name="config_showNavigationBar">true</bool>

    <!-- XXXXX NOTE THE FOLLOWING RESOURCES USE THE WRONG NAMING CONVENTION.
         Please don't copy them, copy anything else. -->

    <!-- the 5th element "resore-time" indicates the number of milliseconds to delay
        before automatically restore the default connection.  Set -1 if the connection
         does not require auto-restore. -->
    <!-- the 6th element indicates boot-time dependency-met value. -->
    <string-array translatable="false" name="networkAttributes">
        <item>"wifi,1,1,1,-1,true"</item>
        <item>"ethernet,9,9,2,-1,true"</item>
    </string-array>

    <!-- This string array should be overridden by the device to present a list of radio
         attributes.  This is used by the connectivity manager to decide which networks can coexist
         based on the hardware -->
    <!-- An Array of "[ConnectivityManager connectionType],
                      [# simultaneous connection types]"  -->
    <string-array translatable="false" name="radioAttributes">
        <item>"1,1"</item>
        <item>"9,1"</item>
    </string-array>

    <!-- List of regexpressions describing the interface (if any) that represent tetherable
         Wifi interfaces.  If the device doesn't want to support tethering over Wifi this
         should be empty.  An example would be "softap.*" -->
    <string-array translatable="false" name="config_tether_wifi_regexs">
        <item>"wlan0"</item>
        <item>"softap.*"</item>
    </string-array>

    <!-- List of regexpressions describing the interface (if any) that represent tetherable
         USB interfaces.  If the device doesn't want to support tethering over USB this should
         be empty.  An example would be "usb.*" -->
    <string-array translatable="false" name="config_tether_usb_regexs">
        <item>"usb\\d"</item>
        <item>"rndis\\d"</item>
    </string-array>

   <!-- List of regexpressions describing the interface (if any) that represent tetherable
         bluetooth interfaces.  If the device doesn't want to support tethering over bluetooth this
         should be empty. -->
    <string-array translatable="false" name="config_tether_bluetooth_regexs">
        <item>"bt-pan"</item>
    </string-array>

    <!-- Array of allowable ConnectivityManager network types for tethering -->
    <!-- Common options are [1, 4] for TYPE_WIFI and TYPE_MOBILE_DUN or
         [0,1,5,7] for TYPE_MOBILE, TYPE_WIFI, TYPE_MOBILE_HIPRI and TYPE_BLUETOOTH -->
    <integer-array translatable="false" name="config_tether_upstream_types">
        <item>1</item>
        <item>7</item>
    </integer-array>

</resources>

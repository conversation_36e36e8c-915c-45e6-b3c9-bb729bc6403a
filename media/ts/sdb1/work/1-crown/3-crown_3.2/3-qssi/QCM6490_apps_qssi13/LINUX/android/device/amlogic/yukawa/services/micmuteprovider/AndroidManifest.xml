<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2021 The Android Open Source Project

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          package="com.google.android.tv.hotwordmic"
          android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>

    <application android:label="YukawaHotwordMicToggleProvider">

        <provider
            android:name=".HotwordMicToggleProvider"
            android:authorities="atv.hotwordmic"
            android:enabled="true"
            android:exported="true" />

        <service android:exported="false" android:name=".HotwordMicToggleService"/>

        <receiver android:name=".BootReceiver"
                  android:exported="true"
                  android:enabled="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
            </intent-filter>
        </receiver>

    </application>
</manifest>
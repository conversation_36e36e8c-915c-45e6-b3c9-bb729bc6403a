<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Copyright 2017, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- Make this 'true' to allow the Emulator to control
         the state of the headphone/microphone jack -->
    <bool name="config_useDevInputEventForAudioJack">true</bool>

    <!--  Maximum number of supported users -->
    <integer name="config_multiuserMaximumUsers">4</integer>
    <!--  Whether Multiuser UI should be shown -->
    <bool name="config_enableMultiUserUI">true</bool>

    <!-- Set to true to add links to Cell Broadcast app from Settings and MMS app. -->
    <bool name="config_cellBroadcastAppLinks">true</bool>

    <!-- MMS user agent string -->
    <string name="config_mms_user_agent" translatable="false">GoldfishNexus</string>

    <!-- MMS user agent prolfile url -->
    <string name="config_mms_user_agent_profile_url" translatable="false">http://gsm.lge.com/html/gsm/Nexus5-M3.xml</string>

    <!-- Indicate whether closing the lid causes the device to enter the folded state which means
         to get a smaller screen and opening the lid causes the device to enter the unfolded state
         which means to get a larger screen. -->
    <bool name="config_lidControlsDisplayFold">true</bool>

    <!-- Allow testing SoftAP using the simulated interfaces on the emulator. -->
    <string-array name="config_tether_wifi_regexs">
      <item>"wlan\\d"</item>
    </string-array>

    <!-- List of biometric sensors on the device, in decreasing strength. Consumed by AuthService
     when registering authenticators with BiometricService. Format must be ID:Modality:Strength,
     where: IDs are unique per device, Modality as defined in BiometricAuthenticator.java,
     and Strength as defined in Authenticators.java -->
    <string-array name="config_biometric_sensors" translatable="false" >
        <item>0:2:15</item> <!-- ID0:Fingerprint:Strong -->
    </string-array>

    <!-- Map of System DeviceState supplied by DeviceStateManager to WM Jetpack posture. -->
    <string-array name="config_device_state_postures" translatable="false">
        <item>1:1</item> <!-- CLOSED -->
        <item>2:2</item> <!-- HALF_OPENED -->
        <item>3:3</item> <!-- OPENED -->
        <item>4:4</item> <!-- FLIPPED -->
        <item>5:5</item> <!-- TENT -->
    </string-array>

    <!-- The device states (supplied by DeviceStateManager) that should be treated as folded by the
         display fold controller. -->
    <integer-array name="config_foldedDeviceStates" translatable="false">
      <item>1</item> <!-- CLOSED -->
    </integer-array>

    <!-- This device is able to support the microphone and camera global toggles. -->
    <bool name="config_supportsMicToggle">true</bool>
    <bool name="config_supportsCamToggle">true</bool>
    <!-- Specifies priority of automatic time sources. Suggestions from higher entries in the list
         take precedence over lower ones.
         See com.android.server.timedetector.TimeDetectorStrategy for available sources. -->
    <string-array name="config_autoTimeSourcesPriority">
        <item>telephony</item>
        <item>network</item>
    </string-array>

    <bool name="config_supportMicNearUltrasound">false</bool>
    <bool name="config_supportSpeakerNearUltrasound">false</bool>
    <string name="config_systemContacts">com.google.android.contacts</string>
</resources>

/*
 * Copyright (C) 2020 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "device_factory.h"
#include "talsa.h"

using android::hardware::audio::CPP_VERSION::IDevicesFactory;
using android::hardware::audio::CPP_VERSION::implementation::DevicesFactory;
namespace talsa = android::hardware::audio::CPP_VERSION::implementation::talsa;

extern "C" IDevicesFactory* HIDL_FETCH_IDevicesFactory(const char* name) {
    (void)name;
    talsa::init();
    return new DevicesFactory();
}

# The default Bluetooth Class of Device
# Service Field: 0x26 -> 38
#     - Bit 17: Networking
#     - Bit 18: Rendering
#     - Bit 21: Audio
# Major Class: 0x04 -> 4 (Audio / Video)
# Minor Class: 0x08 -> 8 (Car Audio)
bluetooth.device.class_of_device=38,4,8
bluetooth.device.default_name=gCar Emulator

# The Bluetooth profiles that cars expect to have enabled. All other profiles
# are disabled by default.
bluetooth.profile.a2dp.sink.enabled=true
bluetooth.profile.avrcp.controller.enabled=true
bluetooth.profile.gatt.enabled=true
bluetooth.profile.hfp.hf.enabled=true
bluetooth.profile.map.client.enabled=true
bluetooth.profile.pan.nap.enabled=true
bluetooth.profile.pan.panu.enabled=true
bluetooth.profile.pbap.client.enabled=true

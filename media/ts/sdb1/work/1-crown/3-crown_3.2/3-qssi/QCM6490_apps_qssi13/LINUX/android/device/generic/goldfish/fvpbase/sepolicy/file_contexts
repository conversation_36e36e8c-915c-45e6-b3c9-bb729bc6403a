/data/vendor/var/run(/.*)?                       u:object_r:varrun_file:s0
/dev/block/mmcblk0                               u:object_r:userdata_block_device:s0
/dev/block/vda                                   u:object_r:userdata_block_device:s0
/dev/dri/card0                                   u:object_r:gpu_device:s0
/dev/dri/renderD128                              u:object_r:gpu_device:s0
/vendor/bin/hw/android\.hardware\.drm@[0-9]+\.[0-9]+-service\.clearkey u:object_r:hal_drm_clearkey_exec:s0
/vendor/bin/hw/android\.hardware\.gatekeeper@1\.0-service.software     u:object_r:hal_gatekeeper_default_exec:s0
/vendor/bin/hw/android\.hardware\.neuralnetworks@1\.3-service-sample-.*   u:object_r:hal_neuralnetworks_sample_exec:s0
/vendor/lib(64)?/hw/gralloc\.minigbm\.so         u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libdrm\.so                      u:object_r:same_process_hal_file:s0
/system/bin/mini_network.sh                      u:object_r:mini_network_exec:s0
/vendor/lib(64)?/hw/vulkan.pastel.so             u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libEGL_angle\.so                u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libGLESv1_CM_angle\.so          u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libGLESv2_angle\.so             u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libminigbm_gralloc.so           u:object_r:same_process_hal_file:s0

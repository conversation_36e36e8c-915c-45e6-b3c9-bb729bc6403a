<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright (c) 2015, The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
-->

<resources>
    <bool name="def_lockscreen_disabled">true</bool>
    <bool name="def_wifi_on">true</bool>
    <!-- Allow scanning even if WiFi is disabled -->
    <integer name="def_wifi_scan_always_available">1</integer>

    <!-- maximize the timeout to INT_MAX about 500+ hours -->
    <integer name="def_screen_off_timeout">**********</integer>

    <!-- Allow users to use both the on-screen keyboard, as well as a real
         keyboard -->
    <bool name="def_show_ime_with_hard_keyboard">true</bool>
</resources>

<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2019 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- sharedUserId with system is required to allow putting activities
     on virtual/secondary displays -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          package="com.android.emulator.multidisplay"
          android:sharedUserId="android.uid.system" >

    <uses-sdk android:minSdkVersion="19" />
    <application android:label="@string/app_name"
                 android:persistent="true">
        <receiver android:name=".MultiDisplayServiceReceiver"
                  android:exported="true">
            <intent-filter>
                <action android:name="com.android.emulator.multidisplay.START" />
            </intent-filter>
        </receiver>

        <service android:name=".MultiDisplayService"
                 android:label="@string/app_name"
                 android:exported="true">
        </service>

    </application>
</manifest>

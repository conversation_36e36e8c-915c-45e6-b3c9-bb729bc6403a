<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2021 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<resources>
    <string-array translatable="false" name="vendor_required_apps_managed_user">

        <item>com.android.vending</item>
        <item>com.google.android.apps.automotive.inputmethod</item>
        <item>com.google.android.apps.automotive.sensing.sensorreplay.recordingapp</item>
        <item>com.google.android.apps.geo.autograph.vms.client.console.car</item>
        <item>com.google.android.apps.geo.autograph.vms.client.devapp</item>
        <item>com.google.android.apps.maps</item>
        <item>com.google.android.automotive.benchmarks</item>
        <item>com.google.android.car.adaslocation</item>
        <item>com.google.android.carassistant</item>
        <item>com.google.android.car.garagemode.testapp</item>
        <item>com.google.android.car.kitchensink</item>
        <item>com.google.android.car.netdbug</item>
        <item>com.google.android.car.networking.preferenceupdater</item>
	<item>com.google.android.car.networking.railway</item>
        <item>com.google.android.car.setupwizard</item>
        <item>com.google.android.car.uxr.oas</item>
        <item>com.google.android.car.uxr.sample</item>
        <item>com.google.android.embedded.projection</item>

    </string-array>
</resources>

<manifest version="1.0" type="device">
    <hal format="hidl">
        <name>android.hardware.automotive.can</name>
        <transport>hwbinder</transport>
        <impl level="generic"></impl>
        <version>1.0</version>
        <interface>
            <name>ICanController</name>
            <instance>socketcan</instance>
        </interface>
        <interface>
            <name>ICanBus</name>
            <instance>BCAN</instance>
            <instance>CCAN</instance>
            <instance>aae</instance>
            <instance>test</instance>
        </interface>
    </hal>
</manifest>

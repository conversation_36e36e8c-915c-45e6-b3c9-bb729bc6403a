# vehiclebus subsystem
hal_attribute(vehiclebus);

type hal_vehiclebus_default, domain;
type hal_vehiclebus_default_exec, exec_type, vendor_file_type, file_type;
init_daemon_domain(hal_vehiclebus_default);

hal_server_domain(hal_vehiclebus_default, hal_vehiclebus);
hal_client_domain(hal_vehiclebus_default, hal_can_bus);
hal_client_domain(hal_vehicle_default, hal_vehiclebus);

binder_use(hal_vehicle_default)
binder_use(hal_vehiclebus_default)

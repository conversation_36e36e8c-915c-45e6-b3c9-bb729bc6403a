<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Copyright 2020, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- List of biometric sensors on the device, in decreasing strength. Consumed by AuthService
     when registering authenticators with BiometricService. Format must be ID:Modality:Strength,
     where: IDs are unique per device, Modality as defined in BiometricAuthenticator.java,
     and Strength as defined in Authenticators.java -->
    <!-- AAOS doesn't support fingerprint and face -->
    <string-array name="config_biometric_sensors" translatable="false" >
    </string-array>

    <integer-array name="config_availableColorModes">
        <item>0</item> <!-- COLOR_MODE_NATURAL -->
    </integer-array>
    <!-- Device does not support near ultrasound audio-->
    <bool name="config_supportMicNearUltrasound">false</bool>
    <bool name="config_supportSpeakerNearUltrasound">false</bool>

    <!-- Controls if local secondary displays should be private or not. Value specified in the array
         represents physical port address of each display and display in this list will be marked
         as private. {@see android.view.Display#FLAG_PRIVATE} -->
    <integer-array translatable="false" name="config_localPrivateDisplayPorts">
        <item>1</item> <!-- ClusterDisplay -->
    </integer-array>
</resources>

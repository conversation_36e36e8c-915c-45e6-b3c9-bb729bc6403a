# define SELinux domain
type hal_drm_widevine, domain;
hal_server_domain(hal_drm_widevine, hal_drm)

type hal_drm_widevine_exec, exec_type, vendor_file_type, file_type;
init_daemon_domain(hal_drm_widevine)

allow hal_drm mediacodec:fd use;

vndbinder_use(hal_drm_widevine);
hal_client_domain(hal_drm_widevine, hal_graphics_composer);
allow hal_drm_widevine hal_allocator_server:fd use;
allow hal_drm_widevine mediadrm_vendor_data_file:dir create_dir_perms;
allow hal_drm_widevine mediadrm_vendor_data_file:file create_file_perms;

# devices
/dev/ttyAML0           u:object_r:console_device:s0
/dev/mali0             u:object_r:gpu_device:s0
/dev/dri/card0         u:object_r:gpu_device:s0
/dev/block/zram0       u:object_r:swap_block_device:s0
/dev/cec0              u:object_r:cec_device:s0
/dev/i2c-0             u:object_r:led_device:s0
/dev/nanohub           u:object_r:sensors_device:s0
/dev/nanohub_comms     u:object_r:sensors_device:s0
/dev/dma_heap/linux,cma u:object_r:dmabuf_cma_heap_device:s0

/sys/devices/platform/soc/ffe03000.sd/mmc_host(/.*)?             u:object_r:sysfs_suspend_stats:s0
/sys/devices/platform/soc/ff800000.bus/ff8000a8.rtc/wakeup(/.*)? u:object_r:sysfs_suspend_stats:s0
/sys/devices/platform/soc/ff800000.bus/ff8000a8.rtc/rtc/rtc0/alarmtimer.0.auto(/.*)? u:object_r:sysfs_suspend_stats:s0
/sys/devices/platform/soc/ff800000.bus/ff8000a8.rtc/rtc/rtc0/wakeup1(/.*)? u:object_r:sysfs_suspend_stats:s0
/sys/devices/platform/soc/ff800000.bus/ff8000a8.rtc/rtc/rtc0/hctosys       u:object_r:sysfs_rtc:s0
/sys/devices/platform/soc/ffd00000.bus/ffd24000.serial(/.*)?     u:object_r:sysfs_suspend_stats:s0
/sys/devices/platform/soc/ffe09000.usb/ff500000.usb(/.*)?        u:object_r:sysfs_suspend_stats:s0

# files in /vendor
/(vendor|system/vendor)/lib(64)?/hw/gralloc\.yukawa\.so u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/hw/android\.hardware\.health@2\.0-impl-2\.1-cuttlefish\.so u:object_r:same_process_hal_file:s0
/vendor/bin/hw/lights-yukawa u:object_r:hal_light_default_exec:s0
/(vendor|system/vendor)/firmware(/.*)?        u:object_r:vendor_firmware_file:s0

#Block devices
/dev/block/platform/soc/ffe07000\.mmc/by-name/userdata u:object_r:userdata_block_device:s0
/dev/block/platform/soc/ffe07000.mmc/by-name/cache u:object_r:cache_block_device:s0
/dev/block/platform/soc/ffe07000.mmc/by-name/recovery   u:object_r:recovery_block_device:s0
/dev/block/platform/soc/ffe07000.mmc/by-name/misc       u:object_r:misc_block_device:s0

/vendor/bin/hw/android\.hardware\.gatekeeper@1\.0-service\.software	u:object_r:hal_gatekeeper_default_exec:s0

/vendor/bin/hw/android\.hardware\.drm(@[0-9]+\.[0-9]+)?-service\.widevine    u:object_r:hal_drm_widevine_exec:s0
/vendor/bin/hw/android\.hardware\.drm@[0-9]+\.[0-9]+-service\.clearkey     u:object_r:hal_drm_clearkey_exec:s0
/data/vendor/mediadrm(/.*)?    u:object_r:mediadrm_vendor_data_file:s0

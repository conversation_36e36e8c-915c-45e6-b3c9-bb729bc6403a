<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2009 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- These are the hardware components that all handheld devices
     must include. Devices with optional hardware must also include extra
     hardware files, per the comments below.

     Handheld devices include phones, mobile Internet devices (MIDs),
     Personal Media Players (PMPs), small tablets (7" or less), and similar
     devices.

     This file is similar to frameworks/native/data/etc/handheld_core_hardware.xml.
     The differences are:
         Extra features available in goldfish:
             android.hardware.location.gps
             android.hardware.sensor.barometer
             android.hardware.sensor.light
             android.hardware.sensor.proximity
             android.hardware.sensor.relative_humidity
             android.hardware.sensor.gyroscope
             android.hardware.sensor.hinge_angle
             android.hardware.telephony
             android.hardware.telephony.gsm
             android.software.midi
-->
<permissions>
    <feature name="android.hardware.audio.output" />
    <feature name="android.hardware.bluetooth" />
    <feature name="android.hardware.camera" />
    <feature name="android.hardware.location" />
    <feature name="android.hardware.location.gps" />
    <feature name="android.hardware.location.network" />
    <feature name="android.hardware.sensor.accelerometer" />
    <feature name="android.hardware.sensor.ambient_temperature" />
    <feature name="android.hardware.sensor.compass" />
    <feature name="android.hardware.sensor.barometer" />
    <feature name="android.hardware.sensor.light" />
    <feature name="android.hardware.sensor.proximity" />
    <feature name="android.hardware.sensor.relative_humidity" />
    <feature name="android.hardware.sensor.gyroscope" />
    <feature name="android.hardware.sensor.hinge_angle" />
    <feature name="android.hardware.telephony" />
    <feature name="android.hardware.telephony.data" />
    <feature name="android.hardware.telephony.gsm" />
    <feature name="android.hardware.telephony.ims" />
    <feature name="android.hardware.telephony.radio.access" />
    <feature name="android.hardware.telephony.subscription" />
    <feature name="android.hardware.touchscreen" />
    <feature name="android.hardware.microphone" />
    <feature name="android.hardware.screen.portrait" />
    <feature name="android.hardware.screen.landscape" />

    <!-- basic system services -->
    <feature name="android.software.app_widgets" />
    <feature name="android.software.telecom" />
    <feature name="android.software.voice_recognizers" notLowRam="true" />
    <feature name="android.software.backup" />
    <feature name="android.software.home_screen" />
    <feature name="android.software.input_methods" />
    <feature name="android.software.midi" />
    <feature name="android.software.print" />
    <feature name="android.software.cant_save_state" />
    <feature name="android.software.secure_lock_screen" />
    <feature name="android.software.companion_device_setup" />

    <!-- Feature to specify if the device supports adding device admins. -->
    <feature name="android.software.device_admin" />

    <!-- Feature to specify if the device support managed users. -->
    <feature name="android.software.managed_users" />

    <feature name="android.software.picture_in_picture" />
    <feature name="android.software.activities_on_secondary_displays" />

    <feature name="android.software.cts" />

    <feature name="android.hardware.security.model.compatible" />

    <!-- Feature to specify if the device supports controls. -->
    <feature name="android.software.controls" />

    <!-- devices with GPS must include android.hardware.location.gps.xml -->
    <!-- devices with an autofocus camera and/or flash must include either
         android.hardware.camera.autofocus.xml or
         android.hardware.camera.autofocus-flash.xml -->
    <!-- devices with a front facing camera must include
         android.hardware.camera.front.xml -->
    <!-- devices with WiFi must also include android.hardware.wifi.xml -->
    <!-- devices that support multitouch must include the most appropriate one
         of these files:

         If only partial (non-independent) pointers are supported:
         android.hardware.touchscreen.multitouch.xml

         If up to 4 independently tracked pointers are supported:
         include android.hardware.touchscreen.multitouch.distinct.xml

         If 5 or more independently tracked pointers are supported:
         include android.hardware.touchscreen.multitouch.jazzhand.xml

         ONLY ONE of the above should be included. -->
    <!-- devices with an ambient light sensor must also include
         android.hardware.sensor.light.xml -->
    <!-- devices with a proximity sensor must also include
         android.hardware.sensor.proximity.xml -->
    <!-- GSM phones must also include android.hardware.telephony.gsm.xml -->
    <!-- CDMA phones must also include android.hardware.telephony.cdma.xml -->
    <!-- Devices that have low-latency audio stacks suitable for apps like
         VoIP may include android.hardware.audio.low_latency.xml. ONLY apps
         that meet the requirements specified in the CDD may include this. -->
</permissions>

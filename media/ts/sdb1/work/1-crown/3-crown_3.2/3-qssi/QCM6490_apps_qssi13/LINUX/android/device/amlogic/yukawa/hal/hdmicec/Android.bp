//
// Copyright (C) 2019 The Android Open-Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

// WARNING: Everything listed here will be built on ALL platforms,
// including x86, the emulator, and the SDK.  Modules must be uniquely
// named (liblights.tuna), and must build everywhere, or limit themselves
// to only building on ARM if they include assembly. Individual makefiles
// are responsible for having their own logic, for fine-grained control.

// HAL module implementation, not prelinked and stored in
// hw/<COPYPIX_HARDWARE_MODULE_ID>.<ro.board.platform>.so
package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "device_amlogic_yukawa_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["device_amlogic_yukawa_license"],
}

cc_library_shared {
    name: "hdmi_cec.yukawa",
    proprietary: true,
    srcs: ["hdmi_cec.c"],
    cflags: ["-Werror"],

    relative_install_path: "hw",

    shared_libs: [
        "liblog",
        "libcutils",
        "libhardware",
    ],

}

//#######################################################
cc_binary {
    name: "hdmicec_test",
    defaults: ["hidl_defaults"],
    vendor: true,
    srcs: ["hdmi_cec_test_hal.c"] + ["hdmi_cec.c"],
    cflags: [
        "-Wno-error",
        "-Wno-unused-parameter",
    ],
    header_libs: ["libhardware_headers"],
    shared_libs: [
        "liblog",
        "libcutils",
    ],

}

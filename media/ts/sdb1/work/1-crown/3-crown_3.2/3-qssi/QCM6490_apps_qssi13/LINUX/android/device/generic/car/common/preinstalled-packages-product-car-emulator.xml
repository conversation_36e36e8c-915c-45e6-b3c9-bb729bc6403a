<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2019 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->
<!-- System packages to preinstall, per user type.
     Documentation at frameworks/base/data/etc/preinstalled-packages-platform.xml
-->
<config>
<!--
  Apps that need to run on SYSTEM.
  Here the apps will have FULL and SYSTEM.
-->
    <install-in-user-type package="com.android.car.developeroptions">
        <install-in user-type="FULL" />
        <install-in user-type="SYSTEM" />
    </install-in-user-type>
    <install-in-user-type package="com.android.car.hvac">
        <install-in user-type="FULL" />
        <install-in user-type="SYSTEM" />
    </install-in-user-type>
    <install-in-user-type package="com.android.phone">
        <install-in user-type="FULL" />
        <install-in user-type="SYSTEM" />
    </install-in-user-type>
    <install-in-user-type package="com.android.systemui">
        <install-in user-type="FULL" />
        <install-in user-type="SYSTEM" />
    </install-in-user-type>
    <install-in-user-type package="com.android.traceur">
        <install-in user-type="FULL" />
        <install-in user-type="SYSTEM" />
    </install-in-user-type>
    <install-in-user-type package="com.android.localtransport">
        <install-in user-type="FULL" />
        <install-in user-type="SYSTEM" />
    </install-in-user-type>

    <!-- Android remote display which need to work for all users-->
    <install-in-user-type package="com.android.car.acast.source">
        <install-in user-type="FULL" />
        <install-in user-type="SYSTEM" />
    </install-in-user-type>

    <!-- This application is needed in ModuleInfoProvider -->
    <install-in-user-type package="com.android.modulemetadata">
        <install-in user-type="FULL" />
        <install-in user-type="SYSTEM" />
    </install-in-user-type>

    <!-- Required for SUW to assume app updates -->
    <install-in-user-type package="com.android.car.systemupdater">
        <install-in user-type="FULL" />
        <install-in user-type="SYSTEM" />
    </install-in-user-type>

    <!-- Required for Wifi -->
    <install-in-user-type package="com.android.networkstack">
        <install-in user-type="FULL" />
        <install-in user-type="SYSTEM" />
    </install-in-user-type>
    <!-- Required for Tethering -->
    <install-in-user-type package="com.android.networkstack.tethering">
        <install-in user-type="FULL" />
        <install-in user-type="SYSTEM" />
    </install-in-user-type>
    <install-in-user-type package="com.android.connectivity.resources">
        <install-in user-type="FULL" />
        <install-in user-type="SYSTEM" />
    </install-in-user-type>

    <!-- Required when sysui queries for system user apps to handle the home intent -->
    <install-in-user-type package="com.android.car.carlauncher">
        <install-in user-type="FULL" />
        <install-in user-type="SYSTEM" />
    </install-in-user-type>

    <!-- Required for RearViewCamera -->
    <install-in-user-type package="com.google.android.car.rvc">
        <install-in user-type="FULL" />
        <install-in user-type="SYSTEM" />
    </install-in-user-type>
<!--
  Apps that do need to run on SYSTEM and evaluated by package owner.
  Here the apps will have FULL only.
-->
    <install-in-user-type package="com.android.car.datacenter">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.car.goldilocks">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.car.settings">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.car.speedbump">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.contacts">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.dynsystem">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.mms.service">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.mtp">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.nfc">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.car.media">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.car.radio">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.car.media.localmediaplayer">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.gallery3d">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.car.themeplayground">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.car.linkviewer">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.car.retaildemo">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.car.rotary">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.car.rotaryime">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.car.rotaryplayground">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.car.voicecontrol">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.documentsui">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.musicfx">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.car.datacenter">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.timezone.updater">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.sdksetup">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.cellbroadcastservice">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.service.ims">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.safetyregulatoryinfo">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.emulator.multidisplay">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.providers.partnerbookmarks">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.service.ims.presence">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.apps.tag">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.angle">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.android.emulator.radio.config">
        <install-in user-type="FULL" />
    </install-in-user-type>

<!--
  Apps installed on multi-display emulator
  TODO: move to a MD-only XML file under vendor/auto/embedded/products
-->
    <!-- defaultToDeviceProtectedStorage -->
    <install-in-user-type package="com.android.cellbroadcastreceiver.module">
        <install-in user-type="FULL" />
        <install-in user-type="SYSTEM" />
    </install-in-user-type>
    <install-in-user-type package="com.android.car.multidisplay">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.google.android.car.multidisplaytest">
        <install-in user-type="FULL" />
    </install-in-user-type>
    <install-in-user-type package="com.example.android.multiclientinputmethod">
        <install-in user-type="FULL" />
    </install-in-user-type>
</config>

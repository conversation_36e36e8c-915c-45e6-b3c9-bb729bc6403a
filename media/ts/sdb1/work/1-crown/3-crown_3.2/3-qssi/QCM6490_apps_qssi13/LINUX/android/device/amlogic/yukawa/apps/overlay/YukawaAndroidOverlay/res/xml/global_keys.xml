<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Copyright 2019, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<!-- Mapping of keycodes to components which will be handled globally.
     Modify this file to add global keys.
     The key will NOT go to the foreground application and instead only ever be sent via targeted
     broadcast to the specified component. The action of the intent will be
     android.intent.action.GLOBAL_BUTTON and the KeyEvent will be included in the intent as
     android.intent.extra.KEY_EVENT.
-->

<global_keys version="1">
    <!-- Example format: id = keycode to handle globally. component = component which will handle this key. -->
    <key keyCode="KEYCODE_PAIRING" component="com.google.android.yukawaservice/.RemoteSyncReceiver" />
</global_keys>

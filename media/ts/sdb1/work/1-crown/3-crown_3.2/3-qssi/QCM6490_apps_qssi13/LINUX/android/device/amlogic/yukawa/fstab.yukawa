/dev/block/platform/soc/ffe07000.mmc/by-name/userdata   /data           f2fs    noatime,nosuid,nodev                                            latemount,wait,check,quota,fileencryption=software,quota
/dev/block/platform/soc/ffe07000.mmc/by-name/userdata   /data           ext4    noatime,nosuid,nodev,nodelalloc,nomblk_io_submit,errors=panic   latemount,wait,check,quota,formattable,reservedsize=32M
/dev/block/platform/soc/ffe07000.mmc/by-name/cache      /cache          ext4    noatime,nosuid,nodev,nodelalloc,nomblk_io_submit,errors=panic   latemount,wait,check,formattable
/dev/block/platform/soc/ffe07000.mmc/by-name/misc       /misc           emmc    defaults                                                        defaults
system                                                  /system         ext4    ro,barrier=1                                                    wait,logical,first_stage_mount
vendor                                                  /vendor         ext4    ro,barrier=1                                                    wait,logical,first_stage_mount
*/block/mmcblk0						auto            auto    defaults                                                        voldmanaged=sdcard1:auto,encryptable=userdata
*/block/sd*                                             auto            auto    defaults                                                        voldmanaged=usb:auto,noemulatedsd
/dev/block/zram0                                        none            swap    defaults                                                        zramsize=268435456

<manifest version="1.0" type="device" target-level="4">
    <hal format="hidl">
        <name>android.hardware.bluetooth</name>
        <transport>hwbinder</transport>
        <version>1.1</version>
        <interface>
            <name>IBluetoothHci</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.bluetooth.audio</name>
        <transport>hwbinder</transport>
        <version>2.1</version>
        <interface>
            <name>IBluetoothAudioProvidersFactory</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.drm</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>ICryptoFactory</name>
            <instance>default</instance>
        </interface>
        <interface>
            <name>IDrmFactory</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.2::ICryptoFactory/clearkey</fqname>
        <fqname>@1.2::IDrmFactory/clearkey</fqname>
    </hal>
    <hal format="hidl">
        <name>android.hardware.authsecret</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IAuthSecret</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.graphics.allocator</name>
        <transport>hwbinder</transport>
        <version>3.0</version>
        <interface>
            <name>IAllocator</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.graphics.mapper</name>
        <transport arch="32+64">passthrough</transport>
        <version>3.0</version>
        <interface>
            <name>IMapper</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.media.c2</name>
        <transport>hwbinder</transport>
        <version>1.1</version>
        <interface>
            <name>IComponentStore</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.camera.provider</name>
        <transport>hwbinder</transport>
        <fqname>@2.4::ICameraProvider/legacy/0</fqname>
    </hal>
    <kernel target-level="5"/>
</manifest>

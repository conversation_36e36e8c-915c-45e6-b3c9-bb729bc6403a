<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Copyright 2021, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- The bounding path of the cutout region of the main built-in display.
         Must either be empty if there is no cutout region, or a string that is parsable by
         {@link android.util.PathParser}.

         The path is assumed to be specified in display coordinates with pixel units and in
         the display's native orientation, with the origin of the coordinate system at the
         center top of the display.

         To facilitate writing device-independent emulation overlays, the marker `@dp` can be
         appended after the path string to interpret coordinates in dp instead of px units.
         Note that a physical cutout should be configured in pixels for the best results.
         -->
    <string translatable="false" name="config_mainBuiltInDisplayCutoutRectApproximation">
        M -258,0
        v 171
        h 516
        v -171
        q
    </string>

    <string translatable="false" name="config_mainBuiltInDisplayCutout">
        M -338,0
        q 86,-4 80,71.5 v 8 q 4,90 95,91.5
        h 163 v -171 z
        M 338,0
        q -86,-4 -80,71.5 v 8 q -4,90 -95,91.5
        h -163 v -171 z
    </string>

    <!-- Whether the display cutout region of the main built-in display should be forced to
         black in software (to avoid aliasing or emulate a cutout that is not physically existent).
     -->
    <bool name="config_fillMainBuiltInDisplayCutout">false</bool>

    <!-- Height of the status bar -->
    <dimen name="status_bar_height_portrait">171px</dimen>
    <dimen name="status_bar_height_landscape">28dp</dimen>
    <!-- Height of area above QQS where battery/time go (equal to status bar height if > 48dp) -->
    <dimen name="quick_qs_offset_height">171px</dimen>
    <!-- Total height of QQS (quick_qs_offset_height + 128dp) -->
    <dimen name="quick_qs_total_height">177dp</dimen>

    <!-- How much we expand the touchable region of the status bar below the notch to catch touches
        that just start below the notch. -->
    <dimen name="display_cutout_touchable_region_size">60px</dimen>
</resources>

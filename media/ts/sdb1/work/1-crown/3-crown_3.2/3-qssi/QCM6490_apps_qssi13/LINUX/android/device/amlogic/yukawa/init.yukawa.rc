import /vendor/etc/init/hw/init.yukawa.usb.rc

on early-init
    # mount debugfs
    mount debugfs /sys/kernel/debug /sys/kernel/debug mode=755

on init
    write /proc/sys/vm/page-cluster 0

    # boot time fs tuning
    write /sys/block/mmcblk1/queue/iostats 0
    write /sys/block/mmcblk1/queue/scheduler bfq
    write /sys/block/mmcblk1/queue/iosched/slice_idle 0
    write /sys/block/mmcblk1/queue/nr_requests 256

on fs
    mount_all /vendor/etc/fstab.yukawa
    swapon_all /vendor/etc/fstab.yukawa

on post-fs
    setprop ro.hardware.hwcomposer drm_meson

on zygote-start
    mkdir /data/vendor/wifi 0770 wifi wifi
    mkdir /data/vendor/wifi/wpa 0770 wifi wifi
    mkdir /data/vendor/wifi/wpa/sockets 0770 wifi wifi

service wpa_supplicant /system/vendor/bin/hw/wpa_supplicant \
     -g@android:wpa_wlan0
     interface aidl android.hardware.wifi.supplicant.ISupplicant/default
     socket wpa_wlan0 dgram 660 wifi wifi
     class main
     disabled
     oneshot

service bugreport /system/bin/dumpstate -d -p -z
    class main
    disabled
    oneshot

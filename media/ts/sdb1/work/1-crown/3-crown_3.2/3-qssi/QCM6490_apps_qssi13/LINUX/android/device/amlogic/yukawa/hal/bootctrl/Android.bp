// Copyright (C) 2018 Texas Instruments Incorporated - http://www.ti.com/
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "device_amlogic_yukawa_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["device_amlogic_yukawa_license"],
}

cc_library_shared {
    name: "bootctrl.yukawa",
    vendor: true,
    recovery_available: true,
    relative_install_path: "hw",

    srcs: [
        "boot_control.cc",
        "bootloader_message.cpp"
    ],

    cflags: [
        "-DLOG_TAG=\"amlogic_bootcontrol\"",
    ],

    header_libs: ["libhardware_headers"],

    static_libs: ["libfstab",],

    shared_libs: [
        "liblog",
        "libcutils",
        "libbase",
        "libz"
    ],
}

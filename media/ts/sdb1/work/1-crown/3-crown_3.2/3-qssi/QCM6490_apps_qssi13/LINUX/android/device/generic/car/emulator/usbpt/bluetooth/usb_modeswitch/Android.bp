// Copyright (C) 2020 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    default_applicable_licenses: [
        "device_generic_car_emulator_usb_modeswitch_license",
    ],
}

// See: http://go/android-license-faq
license {
    name: "device_generic_car_emulator_usb_modeswitch_license",
    visibility: [":__subpackages__"],
    license_kinds: [
        "SPDX-license-identifier-GPL-2.0+",
    ],
    license_text: [
        "LICENSE",
    ],
}

cc_binary {
    name: "usb_modeswitch",
    srcs: [
        "usb_modeswitch.c",
    ],
    shared_libs: [
        "libusb",
    ],
    cflags: [
        "-Wno-self-assign",
        "-Wno-unused-parameter",
        "-Wno-sometimes-uninitialized",
    ],
    vendor: true
}

#!/bin/bash

# Author: <PERSON><PERSON> <<EMAIL>>
# Date: 2020-02-03
# Copyright (c) 2020, Thundercomm All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are
# met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above
#       copyright notice, this list of conditions and the following
#       disclaimer in the documentation and/or other materials provided
#       with the distribution.
#     * Neither the name of The Linux Foundation nor the names of its
#       contributors may be used to endorse or promote products derived
#       from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
# WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
# MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT
# ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
# BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
# CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
# SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
# BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
# WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
# OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
# IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#

LOGFILE=build_hlos.log

usage() {
cat <<USAGE

Usage:
    bash $0 [OPTIONS] [OPTIONS]

Description:
    Builds apps_proc

OPTIONS:
    -h, --help
        Display this help message

    -l, --log
        Log file to store build logs

    -p, --product <product name>
        set product name.

    -v, --variant <build variant>
        set build variant.

Example:
    userdebug build and save build log:
        $0 -l

USAGE
}

BUILD_OPTIONS=""
OPTIONS=""

PRODUCT="lahaina"
VARIANT="userdebug"

BUILD_SELECT="all"

BUILD_PRODUCT=""

SRC_QSSI_PATH="QCM6490_apps_qssi13/LINUX/android"
SRC_VENDOR_PATH="LINUX/android"

# ==================================================================
assert() {
    if [ ${PIPESTATUS[0]} -ne 0 ]; then
        exit 1;
    fi
}


build_qssi() {
    cd $meta_dir/$SRC_QSSI_PATH
    echo "source build/envsetup.sh" 2>&1 | tee -a $log_file
    source build/envsetup.sh

    echo "lunch qssi-${VARIANT} $BUILD_OPTIONS $BUILD_PRODUCT" 2>&1 | tee -a $log_file
    lunch qssi-${VARIANT} $BUILD_OPTIONS $BUILD_PRODUCT

    # Set HARDWARE_VERSION environment variable for Android build
    export HARDWARE_VERSION="$HardWareVersion"
    echo "Setting HARDWARE_VERSION=$HARDWARE_VERSION for Android build" 2>&1 | tee -a $log_file

    # Update system.prop with dynamic firmware version
    QSSI_SYSTEM_PROP="device/qcom/qssi/thundercomm/system.prop"
    if [ -f "$QSSI_SYSTEM_PROP" ]; then
        echo "Updating firmware version in $QSSI_SYSTEM_PROP to $HardWareVersion" 2>&1 | tee -a $log_file
        # Remove existing firmware version line if present
        sed -i '/^ro\.build\.version\.firmware=/d' "$QSSI_SYSTEM_PROP"
        # Add new firmware version line
        echo "ro.build.version.firmware=$HardWareVersion" >> "$QSSI_SYSTEM_PROP"
    fi

    echo "bash build.sh dist $BUILD_OPTIONS $BUILD_PRODUCT" 2>&1 | tee -a $log_file
    bash build.sh -j${JOBS} dist --qssi_only $BUILD_OPTIONS $BUILD_PRODUCT EXPERIMENTAL_USE_OPENJDK9=1.8  2>&1 | tee -a $log_file
    assert

    unset PLATFORM_VERSION
    unset TEMPORARY_DISABLE_PATH_RESTRICTIONS

}

build_vendor() {

    cd $meta_dir/$SRC_VENDOR_PATH
    echo "source build/envsetup.sh" 2>&1 | tee -a $log_file
    source build/envsetup.sh

    echo "lunch ${PRODUCT}-${VARIANT} $BUILD_OPTIONS $BUILD_PRODUCT" 2>&1 | tee -a $log_file
    lunch ${PRODUCT}-${VARIANT} $BUILD_OPTIONS $BUILD_PRODUCT

    # Set HARDWARE_VERSION environment variable for Android build
    export HARDWARE_VERSION="$HardWareVersion"
    echo "Setting HARDWARE_VERSION=$HARDWARE_VERSION for Android build" 2>&1 | tee -a $log_file

    # Update system.prop with dynamic firmware version
    LAHAINA_SYSTEM_PROP="device/qcom/lahaina/system.prop"
    if [ -f "$LAHAINA_SYSTEM_PROP" ]; then
        echo "Updating firmware version in $LAHAINA_SYSTEM_PROP to $HardWareVersion" 2>&1 | tee -a $log_file
        # Remove existing firmware version line if present
        sed -i '/^ro\.build\.version\.firmware=/d' "$LAHAINA_SYSTEM_PROP"
        # Add new firmware version line
        echo "ro.build.version.firmware=$HardWareVersion" >> "$LAHAINA_SYSTEM_PROP"
    fi

    echo "bash build.sh dist $BUILD_OPTIONS $BUILD_PRODUCT" 2>&1 | tee -a $log_file
    bash build.sh -j${JOBS} dist --target_only $BUILD_OPTIONS $BUILD_PRODUCT EXPERIMENTAL_USE_OPENJDK9=1.8  2>&1 | tee -a $log_file

    assert

}

build_storageaccess() {
    echo "calling build_storageaccess ...... " 2>&1 | tee -a $log_file
    cd $meta_dir/$SRC_VENDOR_PATH

    genDir="$meta_dir/$SRC_VENDOR_PATH/bootable/bootloader/edk2/QcomModulePkg/tsnv/host/StorageAccess/gen"
    if [ ! -d "$genDir" ]; then
        echo "$genDir not found, create it"
        mkdir -p $genDir
    else
        echo "$genDir found"
    fi

    cmd="vendor/thundercomm/tsnv/host/mkfs.tsnv $genDir/tsnv.img vendor/thundercomm/tsnv/host/header.xml vendor/thundercomm/tsnv/host/item_release.xml $genDir/StorageAccess.i"
    echo "python3 $cmd" 2>&1 | tee -a $log_file
    python3 $cmd

    assert
}

make_super() {
   cd $meta_dir/$SRC_VENDOR_PATH
    python vendor/qcom/opensource/core-utils/build/build_image_standalone.py \
    --image super \
    --qssi_build_path $meta_dir/$SRC_QSSI_PATH \
    --target_build_path $meta_dir/$SRC_VENDOR_PATH \
    --merged_build_path $meta_dir/$SRC_VENDOR_PATH \
    --target_lunch $PRODUCT --output_ota
    assert

}


#===========================================================
UPDATAPI="false"
JOBS=`cat /proc/cpuinfo| grep "processor"| wc -l`

while true; do
    case "$1" in
        -l|--log)               BUILD_LOG="true";;
        -h|--help)              usage; exit 0;;
        -c|--clean_build)       OPTIONS="$OPTIONS $1";;
        -f|--factory)           BUILD_OPTIONS="--factory_build";;
        -d|--debug)             OPTIONS="$OPTIONS $1";;
        -i|--image)             OPTIONS="$OPTIONS $1 $2";;
        -j|--jobs)              OPTIONS="$OPTIONS $1 $2"; JOBS=$2;;
        -k|--kernel_defconf)    OPTIONS="$OPTIONS $1 $2";;
        -m|--module)            OPTIONS="$OPTIONS $1 $2";;
        -p|--product)           product_category $1 $2; BUILD_PRODUCT="--$2";;
        -s|--setup_ccache)      OPTIONS="$OPTIONS $1";;
        -u|--update-api)        OPTIONS="$OPTIONS $1"; UPDATAPI="true";;
        -v|--variant)           VARIANT=$2;;
        -bs)                    BUILD_SELECT=$2;;
        --)                     break;;
    esac
    shift

    if [ "$1" = "" ]; then
        break
    fi
done

if [ "$(type -t set_log)" = "function" ] ; then
    set_log $BUILD_LOG $LOGFILE
else
    if [ "$BUILD_LOG" = "true" ]; then
        log_file=$LOGFILE
    else
        log_file=/dev/null
    fi
fi

if [ ! -n "$meta_dir" ]; then
    meta_dir=`pwd`
fi

# build storageaccess first
build_storageaccess

case $BUILD_SELECT in
"qssi")
    build_qssi
    ;;
"vendor")
    build_vendor
    ;;
"combination")
    make_super
    ;;
"qc")
    build_qssi
    make_super
    ;;
"vc")
    build_vendor
    make_super
    ;;
*)
    build_qssi
    build_vendor
    make_super
    ;;
esac

cd $meta_dir

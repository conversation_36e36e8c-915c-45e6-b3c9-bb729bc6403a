#============= hal_graphics_composer_default ==============
hal_client_domain(hal_graphics_composer_default, hal_graphics_allocator);

allow hal_graphics_composer_default vndbinder_device:chr_file { ioctl open read write map };
allow hal_graphics_composer_default graphics_device:chr_file { ioctl open read write map };
allow hal_graphics_composer_default gpu_device:chr_file { ioctl open read write map };

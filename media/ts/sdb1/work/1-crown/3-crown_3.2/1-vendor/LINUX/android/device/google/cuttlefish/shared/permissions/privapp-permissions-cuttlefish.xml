<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2020 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<permissions>
    <privapp-permissions package="com.android.google.gce.gceservice">
        <permission name="android.permission.ACCESS_NETWORK_STATE" />
        <permission name="android.permission.ACCESS_WIFI_STATE" />
        <permission name="android.permission.CHANGE_WIFI_STATE" />
        <permission name="android.permission.FOREGROUND_SERVICE" />
        <permission name="android.permission.INTERNET" />
        <permission name="android.permission.RECEIVE_BOOT_COMPLETED" />
        <permission name="android.permission.WRITE_EXTERNAL_STORAGE" />
        <permission name="android.permission.WRITE_SETTINGS" />
        <permission name="android.permission.BLUETOOTH" />
    </privapp-permissions>
</permissions>

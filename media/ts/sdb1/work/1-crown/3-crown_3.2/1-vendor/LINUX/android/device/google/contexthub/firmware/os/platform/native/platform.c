/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <plat/rtc.h>
#include <stdbool.h>
#include <stdio.h>
#include <string.h>
#include <pthread.h>
#include <unistd.h>
#include <platform.h>
#include <seos.h>
#include <timer.h>
#include <usart.h>
#include <gpio.h>
#include <seos.h>
#include <mpu.h>
#include <cpu.h>


void platUninitialize(void)
{
    //TODO
}

void platSleep(void)
{
    //TODO
}

void platLogPutchar(char ch)
{
     putchar(ch);
}

void platInitialize(void)
{
    /* set up RTC */
    rtcInit();

    //TODO
}

uint64_t platGetTicks(void)
{
    //TODO

    return 0;
}

uint32_t platFreeResources(uint32_t tid)
{
    return 0;
}

void platPeriodic()
{
}

int main(int argc, char** argv)
{
    osMain();

    return 0;
}

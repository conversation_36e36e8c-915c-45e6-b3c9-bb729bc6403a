/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

MEMORY
{
	bl	: ORIGIN = 0x08000000,	LENGTH = 16K	/* one block */
	eedata	: ORIGIN = 0x08004000,	LENGTH = 32K	/* two 16K blocks */
	code	: ORIGIN = 0x0800C000,	LENGTH = 208K	/* 16K block + 64K block + 128K block */
	shared	: ORIGIN = 0x08040000,	LENGTH = 768K	/* 6x 128K blocks */
	ram	: ORIGIN = 0x20000000,	LENGTH = 256K
}

<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright (C) 2012 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!--
<!DOCTYPE MediaCodecs [
<!ELEMENT Include EMPTY>
<!ATTLIST Include href CDATA #REQUIRED>
<!ELEMENT MediaCodecs (Decoders|Encoders|Include)*>
<!ELEMENT Decoders (MediaCodec|Include)*>
<!ELEMENT Encoders (MediaCodec|Include)*>
<!ELEMENT MediaCodec (Type|Quirk|Include)*>
<!ATTLIST MediaCodec name CD<PERSON>A #REQUIRED>
<!ATTLIST MediaCodec type CDATA>
<!ELEMENT Type EMPTY>
<!ATTLIST Type name CDATA #REQUIRED>
<!ELEMENT Quirk EMPTY>
<!ATTLIST Quirk name CDATA #REQUIRED>
]>

There's a simple and a complex syntax to declare the availability of a
media codec:

A codec that properly follows the OpenMax spec and therefore doesn't have any
quirks and that only supports a single content type can be declared like so:

    <MediaCodec name="OMX.foo.bar" type="something/interesting" />

If a codec has quirks OR supports multiple content types, the following syntax
can be used:

    <MediaCodec name="OMX.foo.bar" >
        <Type name="something/interesting" />
        <Type name="something/else" />
        ...
        <Quirk name="requires-allocate-on-input-ports" />
        <Quirk name="requires-allocate-on-output-ports" />
        <Quirk name="output-buffers-are-unreadable" />
    </MediaCodec>

Only the three quirks included above are recognized at this point:

"requires-allocate-on-input-ports"
    must be advertised if the component does not properly support specification
    of input buffers using the OMX_UseBuffer(...) API but instead requires
    OMX_AllocateBuffer to be used.

"requires-allocate-on-output-ports"
    must be advertised if the component does not properly support specification
    of output buffers using the OMX_UseBuffer(...) API but instead requires
    OMX_AllocateBuffer to be used.

"output-buffers-are-unreadable"
    must be advertised if the emitted output buffers of a decoder component
    are not readable, i.e. use a custom format even though abusing one of
    the official OMX colorspace constants.
    Clients of such decoders will not be able to access the decoded data,
    naturally making the component much less useful. The only use for
    a component with this quirk is to render the output to the screen.
    Audio decoders MUST NOT advertise this quirk.
    Video decoders that advertise this quirk must be accompanied by a
    corresponding color space converter for thumbnail extraction,
    matching surfaceflinger support that can render the custom format to
    a texture and possibly other code, so just DON'T USE THIS QUIRK.

-->

<MediaCodecs>
    <Settings>
        <Setting name="max-video-encoder-input-buffers" value="12" />
    </Settings>

    <MediaCodec name="OMX.android.goldfish.h264.decoder" type="video/avc" >
        <Limit name="concurrent-instances" max="4" />
    </MediaCodec>
    <MediaCodec name="OMX.android.goldfish.vp8.decoder" type="video/x-vnd.on2.vp8" >
        <Limit name="concurrent-instances" max="4" />
    </MediaCodec>
    <MediaCodec name="OMX.android.goldfish.vp9.decoder" type="video/x-vnd.on2.vp9" >
        <Limit name="concurrent-instances" max="4" />
    </MediaCodec>

    <Include href="media_codecs_google_audio.xml" />
    <Include href="media_codecs_google_telephony.xml" />
    <Include href="media_codecs_google_video.xml" />
</MediaCodecs>

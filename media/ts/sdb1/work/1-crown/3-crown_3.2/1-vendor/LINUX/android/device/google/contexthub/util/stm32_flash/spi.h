/*
 * Copyright (C) 2015 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef _SPI_H_
#define _SPI_H_

#include "stm32_bl.h"

typedef struct spi_handle
{
    handle_t handle;
    int fd;
} spi_handle_t;

uint8_t spi_write_data(handle_t *handle, uint8_t *buffer, int length);
uint8_t spi_write_cmd(handle_t *handle, uint8_t cmd);
uint8_t spi_read_data(handle_t *handle, uint8_t *data, int length);
uint8_t spi_read_ack(handle_t *handle);
int spi_init(handle_t *handle);

#endif /* _SPI_H_ */

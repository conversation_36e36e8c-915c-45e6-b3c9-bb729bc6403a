/*
 * Copyright (C) 2015 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef _STM32F4_CRC_H_
#define _STM32F4_CRC_H_

#include <stdint.h>

#define STM32F4_CRC_RESIDUE 0xC704DD7B

uint32_t stm32f4_crc32(uint8_t *buffer, int length);

#endif /* _STM32F4_CRC_H_ */

// Generated Code - DO NOT EDIT !!
// generated by 'emugen'
#ifndef __gl2_client_context_t_h
#define __gl2_client_context_t_h

#include "gl2_client_proc.h"

#include "gl2_types.h"


struct gl2_client_context_t {

	glActiveTexture_client_proc_t glActiveTexture;
	glAttachShader_client_proc_t glAttachShader;
	glBindAttribLocation_client_proc_t glBindAttribLocation;
	glBindBuffer_client_proc_t glBindBuffer;
	glBindFramebuffer_client_proc_t glBindFramebuffer;
	glBindRenderbuffer_client_proc_t glBindRenderbuffer;
	glBindTexture_client_proc_t glBindTexture;
	glBlendColor_client_proc_t glBlendColor;
	glBlendEquation_client_proc_t glBlendEquation;
	glBlendEquationSeparate_client_proc_t glBlendEquationSeparate;
	glBlendFunc_client_proc_t glBlendFunc;
	glBlendFuncSeparate_client_proc_t glBlendFuncSeparate;
	glBufferData_client_proc_t glBufferData;
	glBufferSubData_client_proc_t glBufferSubData;
	glCheckFramebufferStatus_client_proc_t glCheckFramebufferStatus;
	glClear_client_proc_t glClear;
	glClearColor_client_proc_t glClearColor;
	glClearDepthf_client_proc_t glClearDepthf;
	glClearStencil_client_proc_t glClearStencil;
	glColorMask_client_proc_t glColorMask;
	glCompileShader_client_proc_t glCompileShader;
	glCompressedTexImage2D_client_proc_t glCompressedTexImage2D;
	glCompressedTexSubImage2D_client_proc_t glCompressedTexSubImage2D;
	glCopyTexImage2D_client_proc_t glCopyTexImage2D;
	glCopyTexSubImage2D_client_proc_t glCopyTexSubImage2D;
	glCreateProgram_client_proc_t glCreateProgram;
	glCreateShader_client_proc_t glCreateShader;
	glCullFace_client_proc_t glCullFace;
	glDeleteBuffers_client_proc_t glDeleteBuffers;
	glDeleteFramebuffers_client_proc_t glDeleteFramebuffers;
	glDeleteProgram_client_proc_t glDeleteProgram;
	glDeleteRenderbuffers_client_proc_t glDeleteRenderbuffers;
	glDeleteShader_client_proc_t glDeleteShader;
	glDeleteTextures_client_proc_t glDeleteTextures;
	glDepthFunc_client_proc_t glDepthFunc;
	glDepthMask_client_proc_t glDepthMask;
	glDepthRangef_client_proc_t glDepthRangef;
	glDetachShader_client_proc_t glDetachShader;
	glDisable_client_proc_t glDisable;
	glDisableVertexAttribArray_client_proc_t glDisableVertexAttribArray;
	glDrawArrays_client_proc_t glDrawArrays;
	glDrawElements_client_proc_t glDrawElements;
	glEnable_client_proc_t glEnable;
	glEnableVertexAttribArray_client_proc_t glEnableVertexAttribArray;
	glFinish_client_proc_t glFinish;
	glFlush_client_proc_t glFlush;
	glFramebufferRenderbuffer_client_proc_t glFramebufferRenderbuffer;
	glFramebufferTexture2D_client_proc_t glFramebufferTexture2D;
	glFrontFace_client_proc_t glFrontFace;
	glGenBuffers_client_proc_t glGenBuffers;
	glGenerateMipmap_client_proc_t glGenerateMipmap;
	glGenFramebuffers_client_proc_t glGenFramebuffers;
	glGenRenderbuffers_client_proc_t glGenRenderbuffers;
	glGenTextures_client_proc_t glGenTextures;
	glGetActiveAttrib_client_proc_t glGetActiveAttrib;
	glGetActiveUniform_client_proc_t glGetActiveUniform;
	glGetAttachedShaders_client_proc_t glGetAttachedShaders;
	glGetAttribLocation_client_proc_t glGetAttribLocation;
	glGetBooleanv_client_proc_t glGetBooleanv;
	glGetBufferParameteriv_client_proc_t glGetBufferParameteriv;
	glGetError_client_proc_t glGetError;
	glGetFloatv_client_proc_t glGetFloatv;
	glGetFramebufferAttachmentParameteriv_client_proc_t glGetFramebufferAttachmentParameteriv;
	glGetIntegerv_client_proc_t glGetIntegerv;
	glGetProgramiv_client_proc_t glGetProgramiv;
	glGetProgramInfoLog_client_proc_t glGetProgramInfoLog;
	glGetRenderbufferParameteriv_client_proc_t glGetRenderbufferParameteriv;
	glGetShaderiv_client_proc_t glGetShaderiv;
	glGetShaderInfoLog_client_proc_t glGetShaderInfoLog;
	glGetShaderPrecisionFormat_client_proc_t glGetShaderPrecisionFormat;
	glGetShaderSource_client_proc_t glGetShaderSource;
	glGetString_client_proc_t glGetString;
	glGetTexParameterfv_client_proc_t glGetTexParameterfv;
	glGetTexParameteriv_client_proc_t glGetTexParameteriv;
	glGetUniformfv_client_proc_t glGetUniformfv;
	glGetUniformiv_client_proc_t glGetUniformiv;
	glGetUniformLocation_client_proc_t glGetUniformLocation;
	glGetVertexAttribfv_client_proc_t glGetVertexAttribfv;
	glGetVertexAttribiv_client_proc_t glGetVertexAttribiv;
	glGetVertexAttribPointerv_client_proc_t glGetVertexAttribPointerv;
	glHint_client_proc_t glHint;
	glIsBuffer_client_proc_t glIsBuffer;
	glIsEnabled_client_proc_t glIsEnabled;
	glIsFramebuffer_client_proc_t glIsFramebuffer;
	glIsProgram_client_proc_t glIsProgram;
	glIsRenderbuffer_client_proc_t glIsRenderbuffer;
	glIsShader_client_proc_t glIsShader;
	glIsTexture_client_proc_t glIsTexture;
	glLineWidth_client_proc_t glLineWidth;
	glLinkProgram_client_proc_t glLinkProgram;
	glPixelStorei_client_proc_t glPixelStorei;
	glPolygonOffset_client_proc_t glPolygonOffset;
	glReadPixels_client_proc_t glReadPixels;
	glReleaseShaderCompiler_client_proc_t glReleaseShaderCompiler;
	glRenderbufferStorage_client_proc_t glRenderbufferStorage;
	glSampleCoverage_client_proc_t glSampleCoverage;
	glScissor_client_proc_t glScissor;
	glShaderBinary_client_proc_t glShaderBinary;
	glShaderSource_client_proc_t glShaderSource;
	glStencilFunc_client_proc_t glStencilFunc;
	glStencilFuncSeparate_client_proc_t glStencilFuncSeparate;
	glStencilMask_client_proc_t glStencilMask;
	glStencilMaskSeparate_client_proc_t glStencilMaskSeparate;
	glStencilOp_client_proc_t glStencilOp;
	glStencilOpSeparate_client_proc_t glStencilOpSeparate;
	glTexImage2D_client_proc_t glTexImage2D;
	glTexParameterf_client_proc_t glTexParameterf;
	glTexParameterfv_client_proc_t glTexParameterfv;
	glTexParameteri_client_proc_t glTexParameteri;
	glTexParameteriv_client_proc_t glTexParameteriv;
	glTexSubImage2D_client_proc_t glTexSubImage2D;
	glUniform1f_client_proc_t glUniform1f;
	glUniform1fv_client_proc_t glUniform1fv;
	glUniform1i_client_proc_t glUniform1i;
	glUniform1iv_client_proc_t glUniform1iv;
	glUniform2f_client_proc_t glUniform2f;
	glUniform2fv_client_proc_t glUniform2fv;
	glUniform2i_client_proc_t glUniform2i;
	glUniform2iv_client_proc_t glUniform2iv;
	glUniform3f_client_proc_t glUniform3f;
	glUniform3fv_client_proc_t glUniform3fv;
	glUniform3i_client_proc_t glUniform3i;
	glUniform3iv_client_proc_t glUniform3iv;
	glUniform4f_client_proc_t glUniform4f;
	glUniform4fv_client_proc_t glUniform4fv;
	glUniform4i_client_proc_t glUniform4i;
	glUniform4iv_client_proc_t glUniform4iv;
	glUniformMatrix2fv_client_proc_t glUniformMatrix2fv;
	glUniformMatrix3fv_client_proc_t glUniformMatrix3fv;
	glUniformMatrix4fv_client_proc_t glUniformMatrix4fv;
	glUseProgram_client_proc_t glUseProgram;
	glValidateProgram_client_proc_t glValidateProgram;
	glVertexAttrib1f_client_proc_t glVertexAttrib1f;
	glVertexAttrib1fv_client_proc_t glVertexAttrib1fv;
	glVertexAttrib2f_client_proc_t glVertexAttrib2f;
	glVertexAttrib2fv_client_proc_t glVertexAttrib2fv;
	glVertexAttrib3f_client_proc_t glVertexAttrib3f;
	glVertexAttrib3fv_client_proc_t glVertexAttrib3fv;
	glVertexAttrib4f_client_proc_t glVertexAttrib4f;
	glVertexAttrib4fv_client_proc_t glVertexAttrib4fv;
	glVertexAttribPointer_client_proc_t glVertexAttribPointer;
	glViewport_client_proc_t glViewport;
	glEGLImageTargetTexture2DOES_client_proc_t glEGLImageTargetTexture2DOES;
	glEGLImageTargetRenderbufferStorageOES_client_proc_t glEGLImageTargetRenderbufferStorageOES;
	glGetProgramBinaryOES_client_proc_t glGetProgramBinaryOES;
	glProgramBinaryOES_client_proc_t glProgramBinaryOES;
	glMapBufferOES_client_proc_t glMapBufferOES;
	glUnmapBufferOES_client_proc_t glUnmapBufferOES;
	glTexImage3DOES_client_proc_t glTexImage3DOES;
	glTexSubImage3DOES_client_proc_t glTexSubImage3DOES;
	glCopyTexSubImage3DOES_client_proc_t glCopyTexSubImage3DOES;
	glCompressedTexImage3DOES_client_proc_t glCompressedTexImage3DOES;
	glCompressedTexSubImage3DOES_client_proc_t glCompressedTexSubImage3DOES;
	glFramebufferTexture3DOES_client_proc_t glFramebufferTexture3DOES;
	glBindVertexArrayOES_client_proc_t glBindVertexArrayOES;
	glDeleteVertexArraysOES_client_proc_t glDeleteVertexArraysOES;
	glGenVertexArraysOES_client_proc_t glGenVertexArraysOES;
	glIsVertexArrayOES_client_proc_t glIsVertexArrayOES;
	glDiscardFramebufferEXT_client_proc_t glDiscardFramebufferEXT;
	glMultiDrawArraysEXT_client_proc_t glMultiDrawArraysEXT;
	glMultiDrawElementsEXT_client_proc_t glMultiDrawElementsEXT;
	glGetPerfMonitorGroupsAMD_client_proc_t glGetPerfMonitorGroupsAMD;
	glGetPerfMonitorCountersAMD_client_proc_t glGetPerfMonitorCountersAMD;
	glGetPerfMonitorGroupStringAMD_client_proc_t glGetPerfMonitorGroupStringAMD;
	glGetPerfMonitorCounterStringAMD_client_proc_t glGetPerfMonitorCounterStringAMD;
	glGetPerfMonitorCounterInfoAMD_client_proc_t glGetPerfMonitorCounterInfoAMD;
	glGenPerfMonitorsAMD_client_proc_t glGenPerfMonitorsAMD;
	glDeletePerfMonitorsAMD_client_proc_t glDeletePerfMonitorsAMD;
	glSelectPerfMonitorCountersAMD_client_proc_t glSelectPerfMonitorCountersAMD;
	glBeginPerfMonitorAMD_client_proc_t glBeginPerfMonitorAMD;
	glEndPerfMonitorAMD_client_proc_t glEndPerfMonitorAMD;
	glGetPerfMonitorCounterDataAMD_client_proc_t glGetPerfMonitorCounterDataAMD;
	glRenderbufferStorageMultisampleIMG_client_proc_t glRenderbufferStorageMultisampleIMG;
	glFramebufferTexture2DMultisampleIMG_client_proc_t glFramebufferTexture2DMultisampleIMG;
	glDeleteFencesNV_client_proc_t glDeleteFencesNV;
	glGenFencesNV_client_proc_t glGenFencesNV;
	glIsFenceNV_client_proc_t glIsFenceNV;
	glTestFenceNV_client_proc_t glTestFenceNV;
	glGetFenceivNV_client_proc_t glGetFenceivNV;
	glFinishFenceNV_client_proc_t glFinishFenceNV;
	glSetFenceNV_client_proc_t glSetFenceNV;
	glCoverageMaskNV_client_proc_t glCoverageMaskNV;
	glCoverageOperationNV_client_proc_t glCoverageOperationNV;
	glGetDriverControlsQCOM_client_proc_t glGetDriverControlsQCOM;
	glGetDriverControlStringQCOM_client_proc_t glGetDriverControlStringQCOM;
	glEnableDriverControlQCOM_client_proc_t glEnableDriverControlQCOM;
	glDisableDriverControlQCOM_client_proc_t glDisableDriverControlQCOM;
	glExtGetTexturesQCOM_client_proc_t glExtGetTexturesQCOM;
	glExtGetBuffersQCOM_client_proc_t glExtGetBuffersQCOM;
	glExtGetRenderbuffersQCOM_client_proc_t glExtGetRenderbuffersQCOM;
	glExtGetFramebuffersQCOM_client_proc_t glExtGetFramebuffersQCOM;
	glExtGetTexLevelParameterivQCOM_client_proc_t glExtGetTexLevelParameterivQCOM;
	glExtTexObjectStateOverrideiQCOM_client_proc_t glExtTexObjectStateOverrideiQCOM;
	glExtGetTexSubImageQCOM_client_proc_t glExtGetTexSubImageQCOM;
	glExtGetBufferPointervQCOM_client_proc_t glExtGetBufferPointervQCOM;
	glExtGetShadersQCOM_client_proc_t glExtGetShadersQCOM;
	glExtGetProgramsQCOM_client_proc_t glExtGetProgramsQCOM;
	glExtIsProgramBinaryQCOM_client_proc_t glExtIsProgramBinaryQCOM;
	glExtGetProgramBinarySourceQCOM_client_proc_t glExtGetProgramBinarySourceQCOM;
	glStartTilingQCOM_client_proc_t glStartTilingQCOM;
	glEndTilingQCOM_client_proc_t glEndTilingQCOM;
	glVertexAttribPointerData_client_proc_t glVertexAttribPointerData;
	glVertexAttribPointerOffset_client_proc_t glVertexAttribPointerOffset;
	glDrawElementsOffset_client_proc_t glDrawElementsOffset;
	glDrawElementsData_client_proc_t glDrawElementsData;
	glGetCompressedTextureFormats_client_proc_t glGetCompressedTextureFormats;
	glShaderString_client_proc_t glShaderString;
	glFinishRoundTrip_client_proc_t glFinishRoundTrip;
	glGenVertexArrays_client_proc_t glGenVertexArrays;
	glBindVertexArray_client_proc_t glBindVertexArray;
	glDeleteVertexArrays_client_proc_t glDeleteVertexArrays;
	glIsVertexArray_client_proc_t glIsVertexArray;
	glMapBufferRange_client_proc_t glMapBufferRange;
	glUnmapBuffer_client_proc_t glUnmapBuffer;
	glFlushMappedBufferRange_client_proc_t glFlushMappedBufferRange;
	glMapBufferRangeAEMU_client_proc_t glMapBufferRangeAEMU;
	glUnmapBufferAEMU_client_proc_t glUnmapBufferAEMU;
	glFlushMappedBufferRangeAEMU_client_proc_t glFlushMappedBufferRangeAEMU;
	glReadPixelsOffsetAEMU_client_proc_t glReadPixelsOffsetAEMU;
	glCompressedTexImage2DOffsetAEMU_client_proc_t glCompressedTexImage2DOffsetAEMU;
	glCompressedTexSubImage2DOffsetAEMU_client_proc_t glCompressedTexSubImage2DOffsetAEMU;
	glTexImage2DOffsetAEMU_client_proc_t glTexImage2DOffsetAEMU;
	glTexSubImage2DOffsetAEMU_client_proc_t glTexSubImage2DOffsetAEMU;
	glBindBufferRange_client_proc_t glBindBufferRange;
	glBindBufferBase_client_proc_t glBindBufferBase;
	glCopyBufferSubData_client_proc_t glCopyBufferSubData;
	glClearBufferiv_client_proc_t glClearBufferiv;
	glClearBufferuiv_client_proc_t glClearBufferuiv;
	glClearBufferfv_client_proc_t glClearBufferfv;
	glClearBufferfi_client_proc_t glClearBufferfi;
	glGetBufferParameteri64v_client_proc_t glGetBufferParameteri64v;
	glGetBufferPointerv_client_proc_t glGetBufferPointerv;
	glUniformBlockBinding_client_proc_t glUniformBlockBinding;
	glGetUniformBlockIndex_client_proc_t glGetUniformBlockIndex;
	glGetUniformIndices_client_proc_t glGetUniformIndices;
	glGetUniformIndicesAEMU_client_proc_t glGetUniformIndicesAEMU;
	glGetActiveUniformBlockiv_client_proc_t glGetActiveUniformBlockiv;
	glGetActiveUniformBlockName_client_proc_t glGetActiveUniformBlockName;
	glUniform1ui_client_proc_t glUniform1ui;
	glUniform2ui_client_proc_t glUniform2ui;
	glUniform3ui_client_proc_t glUniform3ui;
	glUniform4ui_client_proc_t glUniform4ui;
	glUniform1uiv_client_proc_t glUniform1uiv;
	glUniform2uiv_client_proc_t glUniform2uiv;
	glUniform3uiv_client_proc_t glUniform3uiv;
	glUniform4uiv_client_proc_t glUniform4uiv;
	glUniformMatrix2x3fv_client_proc_t glUniformMatrix2x3fv;
	glUniformMatrix3x2fv_client_proc_t glUniformMatrix3x2fv;
	glUniformMatrix2x4fv_client_proc_t glUniformMatrix2x4fv;
	glUniformMatrix4x2fv_client_proc_t glUniformMatrix4x2fv;
	glUniformMatrix3x4fv_client_proc_t glUniformMatrix3x4fv;
	glUniformMatrix4x3fv_client_proc_t glUniformMatrix4x3fv;
	glGetUniformuiv_client_proc_t glGetUniformuiv;
	glGetActiveUniformsiv_client_proc_t glGetActiveUniformsiv;
	glVertexAttribI4i_client_proc_t glVertexAttribI4i;
	glVertexAttribI4ui_client_proc_t glVertexAttribI4ui;
	glVertexAttribI4iv_client_proc_t glVertexAttribI4iv;
	glVertexAttribI4uiv_client_proc_t glVertexAttribI4uiv;
	glVertexAttribIPointer_client_proc_t glVertexAttribIPointer;
	glVertexAttribIPointerOffsetAEMU_client_proc_t glVertexAttribIPointerOffsetAEMU;
	glVertexAttribIPointerDataAEMU_client_proc_t glVertexAttribIPointerDataAEMU;
	glGetVertexAttribIiv_client_proc_t glGetVertexAttribIiv;
	glGetVertexAttribIuiv_client_proc_t glGetVertexAttribIuiv;
	glVertexAttribDivisor_client_proc_t glVertexAttribDivisor;
	glDrawArraysInstanced_client_proc_t glDrawArraysInstanced;
	glDrawElementsInstanced_client_proc_t glDrawElementsInstanced;
	glDrawElementsInstancedDataAEMU_client_proc_t glDrawElementsInstancedDataAEMU;
	glDrawElementsInstancedOffsetAEMU_client_proc_t glDrawElementsInstancedOffsetAEMU;
	glDrawRangeElements_client_proc_t glDrawRangeElements;
	glDrawRangeElementsDataAEMU_client_proc_t glDrawRangeElementsDataAEMU;
	glDrawRangeElementsOffsetAEMU_client_proc_t glDrawRangeElementsOffsetAEMU;
	glFenceSync_client_proc_t glFenceSync;
	glClientWaitSync_client_proc_t glClientWaitSync;
	glWaitSync_client_proc_t glWaitSync;
	glDeleteSync_client_proc_t glDeleteSync;
	glIsSync_client_proc_t glIsSync;
	glGetSynciv_client_proc_t glGetSynciv;
	glFenceSyncAEMU_client_proc_t glFenceSyncAEMU;
	glClientWaitSyncAEMU_client_proc_t glClientWaitSyncAEMU;
	glWaitSyncAEMU_client_proc_t glWaitSyncAEMU;
	glDeleteSyncAEMU_client_proc_t glDeleteSyncAEMU;
	glIsSyncAEMU_client_proc_t glIsSyncAEMU;
	glGetSyncivAEMU_client_proc_t glGetSyncivAEMU;
	glDrawBuffers_client_proc_t glDrawBuffers;
	glReadBuffer_client_proc_t glReadBuffer;
	glBlitFramebuffer_client_proc_t glBlitFramebuffer;
	glInvalidateFramebuffer_client_proc_t glInvalidateFramebuffer;
	glInvalidateSubFramebuffer_client_proc_t glInvalidateSubFramebuffer;
	glFramebufferTextureLayer_client_proc_t glFramebufferTextureLayer;
	glRenderbufferStorageMultisample_client_proc_t glRenderbufferStorageMultisample;
	glTexStorage2D_client_proc_t glTexStorage2D;
	glGetInternalformativ_client_proc_t glGetInternalformativ;
	glBeginTransformFeedback_client_proc_t glBeginTransformFeedback;
	glEndTransformFeedback_client_proc_t glEndTransformFeedback;
	glGenTransformFeedbacks_client_proc_t glGenTransformFeedbacks;
	glDeleteTransformFeedbacks_client_proc_t glDeleteTransformFeedbacks;
	glBindTransformFeedback_client_proc_t glBindTransformFeedback;
	glPauseTransformFeedback_client_proc_t glPauseTransformFeedback;
	glResumeTransformFeedback_client_proc_t glResumeTransformFeedback;
	glIsTransformFeedback_client_proc_t glIsTransformFeedback;
	glTransformFeedbackVaryings_client_proc_t glTransformFeedbackVaryings;
	glTransformFeedbackVaryingsAEMU_client_proc_t glTransformFeedbackVaryingsAEMU;
	glGetTransformFeedbackVarying_client_proc_t glGetTransformFeedbackVarying;
	glGenSamplers_client_proc_t glGenSamplers;
	glDeleteSamplers_client_proc_t glDeleteSamplers;
	glBindSampler_client_proc_t glBindSampler;
	glSamplerParameterf_client_proc_t glSamplerParameterf;
	glSamplerParameteri_client_proc_t glSamplerParameteri;
	glSamplerParameterfv_client_proc_t glSamplerParameterfv;
	glSamplerParameteriv_client_proc_t glSamplerParameteriv;
	glGetSamplerParameterfv_client_proc_t glGetSamplerParameterfv;
	glGetSamplerParameteriv_client_proc_t glGetSamplerParameteriv;
	glIsSampler_client_proc_t glIsSampler;
	glGenQueries_client_proc_t glGenQueries;
	glDeleteQueries_client_proc_t glDeleteQueries;
	glBeginQuery_client_proc_t glBeginQuery;
	glEndQuery_client_proc_t glEndQuery;
	glGetQueryiv_client_proc_t glGetQueryiv;
	glGetQueryObjectuiv_client_proc_t glGetQueryObjectuiv;
	glIsQuery_client_proc_t glIsQuery;
	glProgramParameteri_client_proc_t glProgramParameteri;
	glProgramBinary_client_proc_t glProgramBinary;
	glGetProgramBinary_client_proc_t glGetProgramBinary;
	glGetFragDataLocation_client_proc_t glGetFragDataLocation;
	glGetInteger64v_client_proc_t glGetInteger64v;
	glGetIntegeri_v_client_proc_t glGetIntegeri_v;
	glGetInteger64i_v_client_proc_t glGetInteger64i_v;
	glTexImage3D_client_proc_t glTexImage3D;
	glTexImage3DOffsetAEMU_client_proc_t glTexImage3DOffsetAEMU;
	glTexStorage3D_client_proc_t glTexStorage3D;
	glTexSubImage3D_client_proc_t glTexSubImage3D;
	glTexSubImage3DOffsetAEMU_client_proc_t glTexSubImage3DOffsetAEMU;
	glCompressedTexImage3D_client_proc_t glCompressedTexImage3D;
	glCompressedTexImage3DOffsetAEMU_client_proc_t glCompressedTexImage3DOffsetAEMU;
	glCompressedTexSubImage3D_client_proc_t glCompressedTexSubImage3D;
	glCompressedTexSubImage3DOffsetAEMU_client_proc_t glCompressedTexSubImage3DOffsetAEMU;
	glCopyTexSubImage3D_client_proc_t glCopyTexSubImage3D;
	glGetStringi_client_proc_t glGetStringi;
	glGetBooleani_v_client_proc_t glGetBooleani_v;
	glMemoryBarrier_client_proc_t glMemoryBarrier;
	glMemoryBarrierByRegion_client_proc_t glMemoryBarrierByRegion;
	glGenProgramPipelines_client_proc_t glGenProgramPipelines;
	glDeleteProgramPipelines_client_proc_t glDeleteProgramPipelines;
	glBindProgramPipeline_client_proc_t glBindProgramPipeline;
	glGetProgramPipelineiv_client_proc_t glGetProgramPipelineiv;
	glGetProgramPipelineInfoLog_client_proc_t glGetProgramPipelineInfoLog;
	glValidateProgramPipeline_client_proc_t glValidateProgramPipeline;
	glIsProgramPipeline_client_proc_t glIsProgramPipeline;
	glUseProgramStages_client_proc_t glUseProgramStages;
	glActiveShaderProgram_client_proc_t glActiveShaderProgram;
	glCreateShaderProgramv_client_proc_t glCreateShaderProgramv;
	glCreateShaderProgramvAEMU_client_proc_t glCreateShaderProgramvAEMU;
	glProgramUniform1f_client_proc_t glProgramUniform1f;
	glProgramUniform2f_client_proc_t glProgramUniform2f;
	glProgramUniform3f_client_proc_t glProgramUniform3f;
	glProgramUniform4f_client_proc_t glProgramUniform4f;
	glProgramUniform1i_client_proc_t glProgramUniform1i;
	glProgramUniform2i_client_proc_t glProgramUniform2i;
	glProgramUniform3i_client_proc_t glProgramUniform3i;
	glProgramUniform4i_client_proc_t glProgramUniform4i;
	glProgramUniform1ui_client_proc_t glProgramUniform1ui;
	glProgramUniform2ui_client_proc_t glProgramUniform2ui;
	glProgramUniform3ui_client_proc_t glProgramUniform3ui;
	glProgramUniform4ui_client_proc_t glProgramUniform4ui;
	glProgramUniform1fv_client_proc_t glProgramUniform1fv;
	glProgramUniform2fv_client_proc_t glProgramUniform2fv;
	glProgramUniform3fv_client_proc_t glProgramUniform3fv;
	glProgramUniform4fv_client_proc_t glProgramUniform4fv;
	glProgramUniform1iv_client_proc_t glProgramUniform1iv;
	glProgramUniform2iv_client_proc_t glProgramUniform2iv;
	glProgramUniform3iv_client_proc_t glProgramUniform3iv;
	glProgramUniform4iv_client_proc_t glProgramUniform4iv;
	glProgramUniform1uiv_client_proc_t glProgramUniform1uiv;
	glProgramUniform2uiv_client_proc_t glProgramUniform2uiv;
	glProgramUniform3uiv_client_proc_t glProgramUniform3uiv;
	glProgramUniform4uiv_client_proc_t glProgramUniform4uiv;
	glProgramUniformMatrix2fv_client_proc_t glProgramUniformMatrix2fv;
	glProgramUniformMatrix3fv_client_proc_t glProgramUniformMatrix3fv;
	glProgramUniformMatrix4fv_client_proc_t glProgramUniformMatrix4fv;
	glProgramUniformMatrix2x3fv_client_proc_t glProgramUniformMatrix2x3fv;
	glProgramUniformMatrix3x2fv_client_proc_t glProgramUniformMatrix3x2fv;
	glProgramUniformMatrix2x4fv_client_proc_t glProgramUniformMatrix2x4fv;
	glProgramUniformMatrix4x2fv_client_proc_t glProgramUniformMatrix4x2fv;
	glProgramUniformMatrix3x4fv_client_proc_t glProgramUniformMatrix3x4fv;
	glProgramUniformMatrix4x3fv_client_proc_t glProgramUniformMatrix4x3fv;
	glGetProgramInterfaceiv_client_proc_t glGetProgramInterfaceiv;
	glGetProgramResourceiv_client_proc_t glGetProgramResourceiv;
	glGetProgramResourceIndex_client_proc_t glGetProgramResourceIndex;
	glGetProgramResourceLocation_client_proc_t glGetProgramResourceLocation;
	glGetProgramResourceName_client_proc_t glGetProgramResourceName;
	glBindImageTexture_client_proc_t glBindImageTexture;
	glDispatchCompute_client_proc_t glDispatchCompute;
	glDispatchComputeIndirect_client_proc_t glDispatchComputeIndirect;
	glBindVertexBuffer_client_proc_t glBindVertexBuffer;
	glVertexAttribBinding_client_proc_t glVertexAttribBinding;
	glVertexAttribFormat_client_proc_t glVertexAttribFormat;
	glVertexAttribIFormat_client_proc_t glVertexAttribIFormat;
	glVertexBindingDivisor_client_proc_t glVertexBindingDivisor;
	glDrawArraysIndirect_client_proc_t glDrawArraysIndirect;
	glDrawArraysIndirectDataAEMU_client_proc_t glDrawArraysIndirectDataAEMU;
	glDrawArraysIndirectOffsetAEMU_client_proc_t glDrawArraysIndirectOffsetAEMU;
	glDrawElementsIndirect_client_proc_t glDrawElementsIndirect;
	glDrawElementsIndirectDataAEMU_client_proc_t glDrawElementsIndirectDataAEMU;
	glDrawElementsIndirectOffsetAEMU_client_proc_t glDrawElementsIndirectOffsetAEMU;
	glTexStorage2DMultisample_client_proc_t glTexStorage2DMultisample;
	glSampleMaski_client_proc_t glSampleMaski;
	glGetMultisamplefv_client_proc_t glGetMultisamplefv;
	glFramebufferParameteri_client_proc_t glFramebufferParameteri;
	glGetFramebufferParameteriv_client_proc_t glGetFramebufferParameteriv;
	glGetTexLevelParameterfv_client_proc_t glGetTexLevelParameterfv;
	glGetTexLevelParameteriv_client_proc_t glGetTexLevelParameteriv;
	glMapBufferRangeDMA_client_proc_t glMapBufferRangeDMA;
	glUnmapBufferDMA_client_proc_t glUnmapBufferDMA;
	glMapBufferRangeDirect_client_proc_t glMapBufferRangeDirect;
	glUnmapBufferDirect_client_proc_t glUnmapBufferDirect;
	glFlushMappedBufferRangeDirect_client_proc_t glFlushMappedBufferRangeDirect;
	glGetGraphicsResetStatusEXT_client_proc_t glGetGraphicsResetStatusEXT;
	glReadnPixelsEXT_client_proc_t glReadnPixelsEXT;
	glGetnUniformfvEXT_client_proc_t glGetnUniformfvEXT;
	glGetnUniformivEXT_client_proc_t glGetnUniformivEXT;
	glDrawArraysNullAEMU_client_proc_t glDrawArraysNullAEMU;
	glDrawElementsNullAEMU_client_proc_t glDrawElementsNullAEMU;
	glDrawElementsOffsetNullAEMU_client_proc_t glDrawElementsOffsetNullAEMU;
	glDrawElementsDataNullAEMU_client_proc_t glDrawElementsDataNullAEMU;
	glUnmapBufferAsyncAEMU_client_proc_t glUnmapBufferAsyncAEMU;
	glFlushMappedBufferRangeAEMU2_client_proc_t glFlushMappedBufferRangeAEMU2;
	virtual ~gl2_client_context_t() {}

	typedef gl2_client_context_t *CONTEXT_ACCESSOR_TYPE(void);
	static void setContextAccessor(CONTEXT_ACCESSOR_TYPE *f);
	int initDispatchByName( void *(*getProc)(const char *name, void *userData), void *userData);
	virtual void setError(unsigned int  error){ (void)error; };
	virtual unsigned int getError(){ return 0; };
};

#endif

#pragma once
/*
 * Copyright (C) 2017 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <linux/input.h>
#include <linux/uinput.h>
#include <stdint.h>

#include <functional>
#include <vector>

namespace vsoc_input_service {

class VirtualDeviceBase {
 public:
  VirtualDeviceBase(const char* device_name, uint16_t product_id);
  virtual ~VirtualDeviceBase();

  bool SetUp();
  bool EmitEvent(uint16_t type, uint16_t code, uint32_t value);

 protected:
  virtual const std::vector<const uint32_t>& GetEventTypes() const;
  virtual const std::vector<const uint32_t>& GetKeys() const;
  virtual const std::vector<const uint32_t>& GetProperties() const;
  virtual const std::vector<const uint32_t>& GetAbs() const;

  const char* const device_name_;
  const uint16_t bus_type_;
  const uint16_t vendor_id_;
  const uint16_t product_id_;
  const uint16_t version_;

  struct uinput_user_dev dev_ {};
  int fd_ = -1;
};

}  // namespace vsoc_input_service

type vsock_logcat, domain;
type vsock_logcat_exec, exec_type, vendor_file_type, file_type;
type vsock_logcat_port_prop, property_type;
type vsock_logcat_status_prop, property_type;

init_daemon_domain(vsock_logcat)

get_prop(vsock_logcat, vsock_logcat_port_prop)

set_prop(vsock_logcat, vendor_ser_prop)
set_prop(vsock_logcat, vsock_logcat_status_prop)

allow vsock_logcat device:dir w_dir_perms;
allow vsock_logcat device:fifo_file create_file_perms;
allow vsock_logcat kmsg_device:chr_file rw_file_perms;
allow vsock_logcat self:capability net_admin;
allow vsock_logcat self:{ socket vsock_socket } create_socket_perms_no_ioctl;

// Copyright (C) 2017 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

cc_library_shared {
    name: "libemulator_multidisplay_jni",

    srcs: ["com_android_emulator_multidisplay.cpp"],

    shared_libs: [
        "libandroid_runtime",
        "libnativehelper",
        "libcutils",
        "libutils",
        "liblog",
        "libui",
        "libgui",
    ],

    static_libs: [
        "libqemupipe.ranchu",
    ],

    header_libs: [
        "libgralloc_cb.ranchu",
    ],

    cflags: [
        "-Wall",
        "-Wextra",
        "-Wno-unused-parameter",
    ],

    system_ext_specific: true,
}

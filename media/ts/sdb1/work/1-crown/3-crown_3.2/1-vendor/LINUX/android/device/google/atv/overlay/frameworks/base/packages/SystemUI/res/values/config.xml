<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<resources>

    <!-- Empty stub status/navigation bar -->
    <string name="config_statusBarComponent" translatable="false">com.android.systemui.statusbar.tv.TvStatusBar</string>

    <!-- Disable KeyguardSerivce -->
    <bool name="config_enableKeyguardService">false</bool>

    <!-- Package names to be blacklisted in Recents -->
    <string-array name="recents_tv_blacklist_array">
        <item>com.google.android.tv.remote.service</item>
        <item>com.google.android.apps.mediashell</item>
    </string-array>

    <!-- Svelte specific logic, see RecentsConfiguration.SVELTE_* constants. -->
    <integer name="recents_svelte_level">3</integer>
</resources>

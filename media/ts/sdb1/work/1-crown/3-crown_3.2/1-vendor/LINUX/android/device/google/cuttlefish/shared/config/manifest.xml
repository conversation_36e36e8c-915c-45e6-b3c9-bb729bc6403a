<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Copyright 2017, The Android Open Source Project.
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<manifest version="1.0" type="device" target-level="5">
    <kernel  target-level="5" />
    <hal format="hidl">
        <name>android.hardware.audio</name>
        <transport>hwbinder</transport>
        <version>6.0</version>
        <interface>
            <name>IDevicesFactory</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.audio.effect</name>
        <transport>hwbinder</transport>
        <version>6.0</version>
        <interface>
            <name>IEffectsFactory</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.authsecret</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IAuthSecret</name>
            <instance>default</instance>
        </interface>
    </hal>
    <!-- TODO (b/130076570):
    <hal format="hidl">
        <name>android.hardware.biometrics.face</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IBiometricsFace</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
    <!-- TODO (b/130079316):
    <hal format="hidl">
        <name>android.hardware.biometrics.fingerprint</name>
        <transport>hwbinder</transport>
        <version>2.1</version>
        <interface>
            <name>IBiometricsFingerprint</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
    <hal format="hidl">
        <name>android.hardware.bluetooth.audio</name>
        <transport>hwbinder</transport>
        <version>2.0</version>
        <interface>
            <name>IBluetoothAudioProvidersFactory</name>
            <instance>default</instance>
        </interface>
    </hal>
    <!-- TODO (b/130078386):
    <hal format="hidl">
        <name>android.hardware.confirmationui</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IConfirmationUI</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
    <!-- TODO (b/130077250):
    <hal format="hidl">
        <name>android.hardware.contexthub</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IContexthub</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
    <hal format="hidl">
        <name>android.hardware.graphics.allocator</name>
        <transport>hwbinder</transport>
        <version>4.0</version>
        <interface>
            <name>IAllocator</name>
            <instance>default</instance>
        </interface>
    </hal>
    <!-- TODO (b/130079341): -->
    <hal format="hidl">
        <name>android.hardware.graphics.composer</name>
        <transport>hwbinder</transport>
        <version>2.2</version>
        <interface>
            <name>IComposer</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.graphics.mapper</name>
        <transport arch="32+64">passthrough</transport>
        <version>4.0</version>
        <interface>
            <name>IMapper</name>
            <instance>default</instance>
        </interface>
    </hal>
    <!-- TODO (b/130075874):
    <hal format="hidl">
        <name>android.hardware.ir</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IConsumerIr</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
    <hal format="hidl">
        <name>android.hardware.media.omx</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IOmx</name>
            <instance>default</instance>
        </interface>
        <interface>
            <name>IOmxStore</name>
            <instance>default</instance>
        </interface>
    </hal>
    <!-- TODO (b/130079342):
    <hal format="hidl">
        <name>android.hardware.memtrack</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IMemtrack</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
    <!-- TODO (b/130080415):
    <hal format="hidl">
        <name>android.hardware.nfc</name>
        <transport>hwbinder</transport>
        <version>1.1</version>
        <interface>
            <name>INfc</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
    <!-- TODO (b/130079343):
    <hal format="hidl">
        <name>android.hardware.oemlock</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IOemLock</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
    <hal format="hidl">
        <name>android.hardware.radio</name>
        <transport>hwbinder</transport>
        <version>1.5</version>
        <interface>
            <name>IRadio</name>
            <instance>slot1</instance>
            <!-- cuttlefish doesn't support SIM slot 2/3 -->
        </interface>
        <!-- TODO (b/130079344):
        <interface>
            <name>ISap</name>
            <instance>slot1</instance>
        </interface>
        -->
    </hal>
    <!-- TODO (b/130076972):
    <hal format="hidl">
        <name>android.hardware.radio.config</name>
        <transport>hwbinder</transport>
        <version>1.2</version>
        <interface>
            <name>IRadioConfig</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
    <!-- TODO (b/130079239):
    <hal format="hidl">
        <name>android.hardware.secure_element</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>ISecureElement</name>
            <instance>eSE1</instance>
        </interface>
    </hal>
    -->
    <!--
    <hal format="hidl">
        <name>android.hardware.soundtrigger</name>
        <transport>hwbinder</transport>
        <version>2.3</version>
        <interface>
            <name>ISoundTriggerHw</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
    <!-- TODO (b/130079321):
    <hal format="hidl">
        <name>android.hardware.tetheroffload.config</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IOffloadConfig</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
    <!-- TODO (b/130080416):
    <hal format="hidl">
        <name>android.hardware.tetheroffload.control</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IOffloadControl</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
    <!-- TODO (b/130076572):
    <hal format="hidl">
        <name>android.hardware.usb.gadget</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IUsbGadget</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
    <!-- TODO (b/130079219):
    <hal format="hidl">
        <name>android.hardware.vr</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IVr</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
    <!-- TODO (b/130079240):
    <hal format="hidl">
        <name>android.hardware.weaver</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IWeaver</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
    <!-- TODO (b/130079638):
    <hal format="hidl">
        <name>android.hardware.wifi</name>
        <transport>hwbinder</transport>
        <version>1.3</version>
        <interface>
            <name>IWifi</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
    <!-- TODO (b/130079936):
    <hal format="hidl">
        <name>android.hardware.wifi.hostapd</name>
        <transport>hwbinder</transport>
        <version>1.1</version>
        <interface>
            <name>IHostapd</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
    <!-- TODO (b/130080335):
    <hal format="hidl">
        <name>android.hardware.wifi.offload</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IOffload</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
</manifest>

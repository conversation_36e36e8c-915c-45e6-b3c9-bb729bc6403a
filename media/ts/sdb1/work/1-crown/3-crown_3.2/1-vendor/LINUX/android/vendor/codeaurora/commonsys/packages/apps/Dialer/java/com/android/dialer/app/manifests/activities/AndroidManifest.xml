<!-- Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- This manifest file contains activites that are subclasses by
     Google Dialer. TODO: Need to stop subclassing activities and move this
     back into the main manifest file. -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.android.dialer.app">

  <application>

    <activity
        android:name="com.android.dialer.app.settings.DialerSettingsActivity"
        android:exported="false"
        android:label="@string/dialer_settings_label"
        android:parentActivityName="com.android.dialer.main.impl.MainActivity"
        android:theme="@style/SettingsStyle">
      <intent-filter>
        <action android:name="android.intent.action.VIEW"/>
        <category android:name="android.intent.category.DEFAULT"/>
        <data android:scheme="header"/>
      </intent-filter>
    </activity>

  </application>
</manifest>

/*
 * Copyright (C) 2016-2017 STMicroelectronics
 *
 * Author: <PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

    <---------- Introduction ---------->

In order to build the driver for a platform some macros (see Macros list) must
be defined into variant.h file. [variant/{VARIANT_NAME}/inc/variant/variant.h]
Those macros define specific configuration that is platform dependent (SPI bus,
SPI frequency, etc).

Optional FLAGS (FLAGS list) can be used to enable specific features using
variant makefile. [variant/{VARIANT_NAME}/{VARIANT_NAME}.mk]
Those flags modify @ compile time the behavior of the driver enabling for
example i2c master support or calibration libraries.


    <---------- Macros list ---------->

#define LSM6DSM_SPI_SLAVE_BUS_ID                    1                           /* SPI bus ID, on STM32F4xx indicate SPI2 */
#define LSM6DSM_SPI_SLAVE_FREQUENCY_HZ              8000000                     /* SPI frequency in Hz */
#define LSM6DSM_SPI_SLAVE_CS_GPIO                   GPIO_PB(12)                 /* SPI NSS pin, on STM32F4xx indicate NSS of SPI2 */

#define LSM6DSM_INT_IRQ                             EXTI2_IRQn
#define LSM6DSM_INT1_GPIO                           GPIO_PB(6)                  /* LSM6DSM INT1 is required, here connected to STM32F4xx PB6 */

#define LSM6DSM_ACCEL_GYRO_ROT_MATRIX               1, 0, 0, 0, 1, 0, 0, 0, 1   /* Accelerometer and gyroscope axis orientation */
#define LSM6DSM_MAGN_ROT_MATRIX                     1, 0, 0, 0, 1, 0, 0, 0, 1   /* Magnetometer axis orientation [MUST be set if a magn sensor is enabled] */

#define LSM6DSM_LIS3MDL_I2C_ADDRESS                 0x1e                        /* STM LIS3MDL I2C address */
#define LSM6DSM_LPS22HB_I2C_ADDRESS                 0x5d                        /* STM LPS22HB I2C address */


    <---------- FLAGS list ---------->

- LSM6DSM_DBG_ENABLED                       /* Enable debug messages */
- LSM6DSM_ACCEL_CALIB_ENABLED               /* Enable accelerometer bias calibration */
- LSM6DSM_GYRO_CALIB_ENABLED                /* Enable gyroscope bias calibration */
- LSM6DSM_OVERTEMP_CALIB_ENABLED            /* Enable gyroscope over temperature bias calibration [Require LSM6DSM_GYRO_CALIB_ENABLED] */
- LSM6DSM_MAGN_CALIB_ENABLED                /* Enable magnetometer offset calibration */

- LSM6DSM_I2C_MASTER_USE_INTERNAL_PULLUP    /* Use internal pull-up resistors for I2C master interface */

/* Magnetometer sensor (only one per time can be used) */
- LSM6DSM_I2C_MASTER_LIS3MDL                /* Enable STM LIS3MDL magn sensor */
- LSM6DSM_I2C_MASTER_LSM303AGR              /* Enable STM LSM303AGR magn sensor */
- LSM6DSM_I2C_MASTER_AK09916                /* Enable AKM AK09916 magn sensor */

/* Barometer sensor (only one per time can be used) */
- LSM6DSM_I2C_MASTER_LPS22HB                /* Enable STM LPS22HB pressure sensor */

Example: variant/nucleo/nucleo.mk

FLAGS += -DLSM6DSM_DBG_ENABLED -DLSM6DSM_ACCEL_CALIB_ENABLED -DLSM6DSM_GYRO_CALIB_ENABLED -DLSM6DSM_OVERTEMP_CALIB_ENABLED
FLAGS += -DLSM6DSM_I2C_MASTER_LSM303AGR -DLSM6DSM_I2C_MASTER_USE_INTERNAL_PULLUP -DLSM6DSM_MAGN_CALIB_ENABLED


    <---------- ROT_MATRIX macro explanation ---------->

Sensors orientation can be modified through rotation matrices. Accelerometer and gyroscope are sharing same
configuration (LSM6DSM_ACCEL_GYRO_ROT_MATRIX), magnetometer sensor different one (LSM6DSM_MAGN_ROT_MATRIX).
It is following standard rule of matrices moltiplications.
Here an example:

                       [r11 r12 r13]
[x" y" z"] = [x y z] * [r21 r22 r23] = [(x*r11 + y*r21 + z*r31) (x*r12 + y*r22 + z*r32) (x*r13 + y*r23 + z*r33)]
                       [r31 r32 r33]

where x,y,z are sensors output and x",y",z" are android coordinate system data.
Matrix is so defined:
#define LSM6DSM_ACCEL_GYRO_ROT_MATRIX                   r11,r12,r13,r21,r22,r23,r31,r32,r33

r** can only be: 0 or 1 or -1.


    <---------- Supported features ---------->

> Accel & Gyro data;
> Accel bias calibration through accel_cal lib;
> Gyro bias calibration through gyro_cal & gyro_stillnes_detect libs;
> Step detector & counter;
> Significant motion;
> Magnetometer sensor connected through I2C master interface (LIS3MDL, LSM303AGR, AK09916);
> Magnetometer calibration though mag_cal lib;
> Pressure sensor connected through I2C master interface (LPS22HB);

// Generated Code - DO NOT EDIT !!
// generated by 'emugen'
#ifndef __foo_wrapper_proc_t_h
#define __foo_wrapper_proc_t_h



#include "foo_types.h"
#ifndef foo_APIENTRY
#define foo_APIENTRY 
#endif
typedef void (foo_APIENTRY *fooAlphaFunc_wrapper_proc_t) (FooInt, FooFloat);
typedef FooBoolean (foo_APIENTRY *fooIsBuffer_wrapper_proc_t) (void*);
typedef void (foo_APIENTRY *fooUnsupported_wrapper_proc_t) (void*);
typedef void (foo_APIENTRY *fooDoEncoderFlush_wrapper_proc_t) (FooInt);
typedef void (foo_APIENTRY *fooTakeConstVoidPtrConstPtr_wrapper_proc_t) (const void* const*);
typedef void (foo_APIENTRY *fooSetComplexStruct_wrapper_proc_t) (const FooStruct*);
typedef void (foo_APIENTRY *fooGetComplexStruct_wrapper_proc_t) (FooStruct*);
typedef void (foo_APIENTRY *fooInout_wrapper_proc_t) (uint32_t*);


#endif

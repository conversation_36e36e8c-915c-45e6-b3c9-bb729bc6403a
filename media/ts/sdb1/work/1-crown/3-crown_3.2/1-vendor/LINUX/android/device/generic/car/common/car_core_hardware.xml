<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- This is a copy of frameworks/native/data/etc/car_core_hardware.xml that is somewhat
     combined with device/generic/goldfish/data/etc/handheld_core_hardware.xml.
-->

<!-- These are the hardware components that all handheld devices
     must include. Devices with optional hardware must also include extra
     hardware files, per the comments below.

     Handheld devices include phones, mobile Internet devices (MIDs),
     Personal Media Players (PMPs), small tablets (7" or less), and similar
     devices.
-->
<permissions>
    <!-- This is Android and fully CTS compatible.  Basically this is for CTS tests to use. -->
    <feature name="android.software.cts" />

    <feature name="android.hardware.audio.output" />
    <feature name="android.hardware.location" />
    <feature name="android.hardware.location.gps" />
    <feature name="android.hardware.location.network" />
    <feature name="android.hardware.touchscreen" />
    <feature name="android.hardware.microphone" />

     <!-- Notably bluetooth is added to avoid crashing. It does not work in the emulator. -->
    <feature name="android.hardware.bluetooth" />

    <!-- basic system services -->
    <feature name="android.software.connectionservice" />
    <feature name="android.software.voice_recognizers" notLowRam="true" />
    <feature name="android.software.home_screen" />
    <feature name="android.software.cant_save_state" />
    <feature name="android.software.input_methods" />
    <feature name="android.software.midi" />
    <feature name="android.software.secure_lock_screen" />

    <!-- devices with GPS must include android.hardware.location.gps.xml -->
    <!-- devices with an autofocus camera and/or flash must include either
         android.hardware.camera.autofocus.xml or
         android.hardware.camera.autofocus-flash.xml -->
    <!-- devices with a front facing camera must include
         android.hardware.camera.front.xml -->
    <!-- devices with WiFi must also include android.hardware.wifi.xml -->
    <!-- devices that support multitouch must include the most appropriate one
         of these files:

         If only partial (non-independent) pointers are supported:
         android.hardware.touchscreen.multitouch.xml

         If up to 4 independently tracked pointers are supported:
         include android.hardware.touchscreen.multitouch.distinct.xml

         If 5 or more independently tracked pointers are supported:
         include android.hardware.touchscreen.multitouch.jazzhand.xml

         ONLY ONE of the above should be included. -->
    <!-- devices with an ambient light sensor must also include
         android.hardware.sensor.light.xml -->
    <!-- devices with a proximity sensor must also include
         android.hardware.sensor.proximity.xml -->
    <!-- GSM phones must also include android.hardware.telephony.gsm.xml -->
    <!-- CDMA phones must also include android.hardware.telephony.cdma.xml -->
    <!-- Devices that have low-latency audio stacks suitable for apps like
         VoIP may include android.hardware.audio.low_latency.xml. ONLY apps
         that meet the requirements specified in the CDD may include this. -->
</permissions>

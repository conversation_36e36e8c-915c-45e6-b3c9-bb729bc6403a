# goldfish logcat service:  runs logcat -Q in logpersist domain

# See global logcat.te/logpersist.te, only set for eng & userdebug,
# allow for all builds in a non-conflicting manner.

domain_auto_trans(init, logcat_exec, logpersist)

# Read from logd.
unix_socket_connect(logpersist, logdr, logd)

# Write to /dev/ttyS2 and /dev/ttyGF2.
allow logpersist serial_device:chr_file { write open };
get_prop(logpersist, qemu_cmdline)

type vendor_init_radio_prop, property_type;
type cf_graphics_config_prop, property_type;
type cf_fstab_name_prop, property_type;

allow vendor_init {
  audio_device
}:chr_file { getattr };

set_prop(vendor_init, hal_bluetooth_sim_prop)

set_prop(vendor_init, vendor_init_radio_prop)

get_prop(vendor_init, vendor_ser_prop)

get_prop(vendor_init, cf_graphics_config_prop)

get_prop(vendor_init, cf_fstab_name_prop)

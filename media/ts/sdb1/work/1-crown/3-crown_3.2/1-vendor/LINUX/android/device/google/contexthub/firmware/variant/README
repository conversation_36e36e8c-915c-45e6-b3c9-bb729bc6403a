0. Directory structure

For ease of source browsing this directory may contain soft links to variant
directories located elsewhere;
build scripts fully support such layout, however this is not necessary in order
to build such variants.

1. HW Customization

1.1 General description
- wakeup-gpio has direction from Application Processor (AP) to ContextHub
- irq1 has direction from ContextHub to AP (wakeup interrupt)
- irq2 has direction from ContextHub to AP (non-wakeup interrupt) (optional)

1.2 Kernel side
- wakeup-gpio = SH_INT_WAKEUP
- irq1-gpio   = AP_INT_WAKEUP
- irq2-gpio   = AP_INT_NONWAKEUP

1.3 ContextHub side
- #define SH_INT_WAKEUP    PX(N0)
- #define AP_INT_WAKEUP    PY(N1)
- #define AP_INT_NONWAKEUP PZ(N2)

/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

SECTIONS
{
	/* bootloader */
	.bl : {
		_BL = ABSOLUTE(.);
		KEEP (*(.blvec) ) ;
		KEEP (*(.blapi) ) ;
		*(.text) *(.text.*) ;
		*(.rodata) *(.rodata.*) ;
		. = ALIGN(4);
		 __pubkeys_start = ABSOLUTE(.);
		KEEP (*(.pubkeys) ) ;
		__pubkeys_end = ABSOLUTE(.);
		. = ALIGN(4);
	} > bl = 0xff

	/* initial EEDATA contents */
	.eedata : {

		. = ALIGN(4);
		KEEP (*(.eedata) ) ;
		. = LENGTH(eedata) - 1; /* make sure it is all full of 0xFFs */
                BYTE (0xff) ;

	} > eedata = 0xff

	/* at least a byte of code is needed, or ld cannot locate "__code_start" symbol properly */
	.codeplaceholder (NOLOAD) : {

		BYTE (0xff) ;

	} > code

	.stack (NOLOAD) : {
		. = ALIGN(4);
		__stack_bottom = ABSOLUTE(.);
		KEEP ( *(.stack) );
		KEEP ( *(.stack.*) );
		. = ALIGN(4);
		__stack_top = ABSOLUTE(.);
	 } > ram

	.data : {
		. = ALIGN(4);
		__data_start = ABSOLUTE(.);
		*(.data);
		*(.data.*);
		. = ALIGN(4);
		__data_end = ABSOLUTE(.);
	 } > ram AT > bl

	.bss : {
		. = ALIGN(4);
		__bss_start = ABSOLUTE(.);
		*(.bss) *(.bss.*) *(COMMON);
		. = ALIGN(4);
		__bss_end = ABSOLUTE(.);
	} > ram
}

ENTRY(__blEntry)

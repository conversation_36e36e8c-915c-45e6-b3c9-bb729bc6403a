on early-init
#    loglevel 8
    mkdir /var/run 0755 root root
    mkdir /var/run/media 0755 media root
    mkdir /var/run/system 0755 system root
    mkdir /dev/gce 0750
    chown system system /dev/gce

    # For KCOV
    mount debugfs debugfs /sys/kernel/debug
    chmod 0755 /sys/kernel/debug

    setprop ro.sf.lcd_density ${ro.boot.lcd_density}
    setprop ro.hardware.egl ${ro.boot.hardware.egl}
    setprop ro.hardware.gralloc ${ro.boot.hardware.gralloc}
    setprop ro.hardware.hwcomposer ${ro.boot.hardware.hwcomposer}
    setprop ro.hardware.vulkan ${ro.boot.hardware.vulkan}

    # start module load in the background
    start vendor.insmod_sh

on init
    # ZRAM setup
    write /sys/block/zram0/comp_algorithm lz4
    #
    # EAS uclamp interfaces
    #
    mkdir /dev/cpuctl/foreground
    mkdir /dev/cpuctl/background
    mkdir /dev/cpuctl/top-app
    mkdir /dev/cpuctl/rt
    chown system system /dev/cpuctl
    chown system system /dev/cpuctl/foreground
    chown system system /dev/cpuctl/background
    chown system system /dev/cpuctl/top-app
    chown system system /dev/cpuctl/rt
    chown system system /dev/cpuctl/tasks
    chown system system /dev/cpuctl/foreground/tasks
    chown system system /dev/cpuctl/background/tasks
    chown system system /dev/cpuctl/top-app/tasks
    chown system system /dev/cpuctl/rt/tasks
    chmod 0664 /dev/cpuctl/tasks
    chmod 0664 /dev/cpuctl/foreground/tasks
    chmod 0664 /dev/cpuctl/background/tasks
    chmod 0664 /dev/cpuctl/top-app/tasks
    chmod 0664 /dev/cpuctl/rt/tasks


on fs
    # Mount everything that does not require fsck
    mount_all --early
    restorecon_recursive /vendor

    start setup_wifi
    # works around framework netiface enumeration issue
    start rename_eth1

    # TODO(ender): Find better way to talk to serial port.
    chmod 622 /dev/kmsg

    # for GCE camera HAL
    mkdir /var/media 0770 audio media

    chmod 0664 /sys/kernel/debug/ieee80211/phy1/hwsim/group
    chmod 0664 /sys/kernel/debug/ieee80211/phy1/hwsim/ps
    chmod 0664 /sys/kernel/debug/ieee80211/phy0/rc/fixed_rate_idx
    chmod 0664 /sys/kernel/debug/ieee80211/phy0/hwsim/group
    chmod 0664 /sys/kernel/debug/ieee80211/phy0/hwsim/ps
    chmod 0664 /sys/kernel/debug/ieee80211/phy1/rc/fixed_rate_idx


on post-fs
    # set RLIMIT_MEMLOCK to 64MB
    setrlimit 8 67108864 67108864


on post-fs-data
    start vport_trigger

    mkdir /data/vendor/modem_dump 0777 system system
    mkdir /data/vendor/radio 0777 system system


on late-fs
    # Wait for keymaster
    exec_start wait_for_keymaster

    # Mount RW partitions which need run fsck
    mount_all --late

    write /dev/kmsg "GUEST_BUILD_FINGERPRINT: ${ro.build.fingerprint}"


on boot
    chmod 0660 /dev/cpuctl
    mkdir /data/vendor/wifi 0770 wifi wifi
    mkdir /data/vendor/wifi/wpa 0770 wifi wifi
    mkdir /data/vendor/wifi/wpa/sockets 0770 wifi wifi
    start socket_vsock_proxy


service setup_wifi /vendor/bin/setup_wifi
    oneshot


service rename_eth1 /vendor/bin/rename_netiface eth1 rmnet0
    oneshot


on property:sys.boot_completed=1
    trigger sys-boot-completed-set


# We want one opportunity per boot to enable zram, so we
# use a trigger we fire from the above stanza. If
# persist.sys.zram_enabled becomes true after boot,
# we don't want to run swapon_all at that time.
on sys-boot-completed-set && property:persist.sys.zram_enabled=1
    swapon_all

service vendor.insmod_sh /vendor/bin/init.insmod.sh
    class main
    user root
    group root system
    disabled
    oneshot

service socket_vsock_proxy /vendor/bin/socket_vsock_proxy -server=vsock -tcp_port=5555 -vsock_port=6520


service vport_trigger /vendor/bin/vport_trigger
    oneshot


on property:vendor.ser.cf-logcat=*
    symlink ${vendor.ser.cf-logcat} /dev/cf-logcat
    enable seriallogging


service seriallogging /system/bin/logcat -b all -v threadtime -f /dev/cf-logcat *:V
    class main
    user root
    disabled


on property:ro.boot.vsock_logcat_port=*
   enable vsock_logcat


service vsock_logcat /vendor/bin/vsock_logcat
    class main
    user root
    disabled

service vsoc_input_service /vendor/bin/vsoc_input_service -touch_port=${ro.boot.vsock_touch_port} -keyboard_port=${ro.boot.vsock_keyboard_port}
    group root uhid
    disabled

on early-boot && property:ro.boot.vsock_touch_port=*
   enable vsoc_input_service
   start vsoc_input_service

on early-boot && property:ro.boot.vsock_keyboard_port=*
   enable vsoc_input_service
   start vsoc_input_service

service wpa_supplicant /vendor/bin/hw/wpa_supplicant -g@android:wpa_wlan0
    interface android.hardware.wifi.supplicant@1.0::ISupplicant default
    interface android.hardware.wifi.supplicant@1.1::ISupplicant default
    interface android.hardware.wifi.supplicant@1.2::ISupplicant default
    interface android.hardware.wifi.supplicant@1.3::ISupplicant default
    socket wpa_wlan0 dgram 660 wifi wifi
    group system wifi inet
    disabled
    oneshot


service bugreport /system/bin/dumpstate -d -p -z
    class main
    disabled
    oneshot
    keycodes 30 48

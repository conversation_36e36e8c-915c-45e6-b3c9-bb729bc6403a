/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef LEDS_GPIO_H_
#define LEDS_GPIO_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

#define LEDS_GPIO_MAX    16

struct LedsCfg {
    uint32_t led_num;
    uint32_t value;
};

struct LedsGpio {
    uint32_t *leds_array;
    uint32_t num;
};

const struct LedsGpio *ledsGpioBoardCfg(void);

#ifdef __cplusplus
}
#endif

#endif

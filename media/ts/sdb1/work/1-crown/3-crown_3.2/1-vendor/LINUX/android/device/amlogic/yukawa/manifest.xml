<manifest version="1.0" type="device" target-level="3">
    <hal format="hidl">
        <name>android.hardware.configstore</name>
        <transport>hwbinder</transport>
        <version>1.1</version>
        <interface>
            <name>ISurfaceFlingerConfigs</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.audio</name>
        <transport>hwbinder</transport>
        <version>6.0</version>
        <interface>
            <name>IDevicesFactory</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.audio.effect</name>
        <transport>hwbinder</transport>
        <version>6.0</version>
        <interface>
            <name>IEffectsFactory</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.soundtrigger</name>
        <transport>hwbinder</transport>
        <version>2.2</version>
        <interface>
            <name>ISoundTriggerHw</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal>
        <name>android.hardware.tv.cec</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IHdmiCec</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.bluetooth</name>
        <transport>hwbinder</transport>
        <version>1.1</version>
        <interface>
            <name>IBluetoothHci</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.bluetooth.a2dp</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IBluetoothAudioOffload</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.media.omx</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IOmx</name>
            <instance>default</instance>
        </interface>
        <interface>
            <name>IOmxStore</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.drm</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>ICryptoFactory</name>
            <instance>default</instance>
        </interface>
        <interface>
            <name>IDrmFactory</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.keymaster</name>
        <transport>hwbinder</transport>
        <version>3.0</version>
        <interface>
            <name>IKeymasterDevice</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.health</name>
        <transport>hwbinder</transport>
        <version>2.0</version>
        <interface>
            <name>IHealth</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.graphics.allocator</name>
        <transport>hwbinder</transport>
        <version>2.0</version>
        <interface>
            <name>IAllocator</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.graphics.mapper</name>
        <transport arch="32+64">passthrough</transport>
        <version>2.0</version>
        <interface>
            <name>IMapper</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.graphics.composer</name>
        <transport>hwbinder</transport>
        <version>2.1</version>
        <interface>
            <name>IComposer</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.renderscript</name>
        <transport arch="32+64">passthrough</transport>
        <version>1.0</version>
            <interface>
            <name>IDevice</name>
        <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.memtrack</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IMemtrack</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.power</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IPower</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl">
        <name>android.hardware.thermal</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IThermal</name>
            <instance>default</instance>
        </interface>
    </hal>
</manifest>

/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef _CM4F_ATOMIC_H_
#define _CM4F_ATOMIC_H_

static inline bool atomicCmpXchgPtr(volatile uintptr_t *word, uintptr_t prevVal, uintptr_t newVal) {
    // 32-bit CPU architecture so fall back appropriately
    return atomicCmpXchg32bits((volatile uint32_t *) word, (uint32_t) prevVal, (uint32_t) newVal);
}

#endif


0. ENVIRONMENT SETUP (Common ANDROID and NON-AND<PERSON><PERSON> builds)

# Create Android lunch shell and add external toolchain

bash
. build/envsetup.sh
. device/google/contexthub/firmware/toolchain-setup.sh
lunch <your lunch option>

# First time you run toolchain-setup.sh it will download and install
# the external toolchain from linaro launchpad.
# Next time it will simply define environment variables for it.


1. NON-ANDROID BUILD (unsupported)

1.1. to build any OS variant, run

make -C <variant_path>

# where <variant_path> is relative path to OS variant dir;
# or simply "cd  <variant_path>" and run "make".
# for local variants, variant_path is variant/<variant_name>

1.2. to build nanoapp, run

make -C <nanapp_path>

# where <nanoapp_path> is relative path to nanoapp;
# or simply "cd  <nanoapp_path>" and run "make".
# for local nanoapps, nanoapp_path is app/<app_name>


2. ANDROID BUILD

2.1 to build nanohub OS and all apps

# execute
make auxiliary -j24

2.2 Build Artifacts Location

# Artifacts will be found in
# $OUT/aux/$(AUX_OS_VARIANT)/$(AUX_OS)-$(AUX_ARCH)-$(AUX_CPU)/bin
# e.g. for nanohub OS on STM32 series MCU (ARM cortex m4 core) for Angler this will be in
# $OUT/aux/angler/nanohub-stm32-cortexm4/bin
# where $OUT is lunch shell environment variable

2.3 partial build or build without dependencies

# Any app or static library could be built in isolation with
mmm <path-to-module>

# or, for current path
mm

# Nanohub OS system image is only built with
m auxiliary

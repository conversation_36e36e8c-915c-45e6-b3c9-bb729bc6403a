<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2011 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- Layout parameters are set programmatically. -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  android:id="@+id/call_log_fragment_root"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:orientation="vertical">

  <LinearLayout
      android:id="@+id/multi_select_select_all_view_content"
      android:layout_width="match_parent"
      android:layout_height="@dimen/tab_height"
      android:layout_gravity="start"
      android:background="?android:attr/colorPrimary"
      android:orientation="horizontal"
      android:visibility="gone">

    <ImageView
        android:id="@+id/select_all_view_icon"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:paddingLeft="@dimen/select_all_icon_padding"
        android:paddingRight="@dimen/select_all_icon_padding"
        android:gravity="center_vertical"
        android:contentDescription="@string/select_all"
        android:src="@drawable/ic_empty_check_mark_white_24dp"/>
    <TextView
        android:id="@+id/select_all_view_text"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:paddingLeft="@dimen/select_all_text_left_padding"
        android:gravity="center_vertical"
        android:text="@string/select_all"
        android:textAllCaps="true"
        android:textColor="?android:attr/textColorPrimaryInverse"
        android:textSize="@dimen/select_all_text_size"/>
  </LinearLayout>

  <include layout="@layout/call_log_spinner" />

  <FrameLayout
    android:id="@+id/modal_message_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="gone"/>

  <android.support.v7.widget.RecyclerView
    android:id="@+id/recycler_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/floating_action_button_list_bottom_padding"
    android:paddingStart="@dimen/call_log_horizontal_margin"
    android:paddingEnd="@dimen/call_log_horizontal_margin"
    android:clipToPadding="false"/>

  <com.android.dialer.widget.EmptyContentView
    android:id="@+id/empty_list_view"
    android:layout_width="match_parent"
    android:layout_height="0dp"
    android:layout_weight="1"
    android:layout_gravity="center"
    android:gravity="center_vertical"/>

</LinearLayout>

<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<resources>

    <!-- Do not enable auto rotation switching -->
    <bool name="def_accelerometer_rotation">false</bool>

    <!-- Disable the lockscreen -->
    <bool name="def_lockscreen_disabled">true</bool>

    <!-- Keep screen on at all times by default -->
    <bool name="def_stay_on_while_plugged_in">true</bool>

    <!-- Do not give up on DHCP -->
    <integer name="def_max_dhcp_retries">0</integer>

    <!-- disable network monitoring support
         NOTE: we may need to reenable this in a future release
         to support new developer oriented monitoring tools -->
    <bool name="def_netstats_enabled">false</bool>

    <!-- ms until the system goes to sleep, 24 hours default for TV devices -->
    <integer name="def_sleep_timeout">86400000</integer>

    <!-- Default screen timeout set when setting screensaver. Currently (15 * 60 * 1000) = 15 min -->
    <integer name="def_screen_off_timeout">900000</integer>

</resources>

/*
 * Copyright (C) 2020 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

cc_library_shared {
    name: "<EMAIL>",
    vendor: true,
    relative_install_path: "hw",
    defaults: ["hidl_defaults"],
    srcs: [
        "multihal_sensors.cpp",
        "multihal_sensors_epoll.cpp",
        "multihal_sensors_qemu.cpp",
        "sensor_list.cpp",
        "entry.cpp"
    ],
    shared_libs: [
        "android.hardware.sensors@2.0",
        "android.hardware.sensors@2.1",
        "android.hardware.sensors@2.0-ScopedWakelock",
        "libbase",
        "libhidlbase",
        "liblog",
        "libutils",
    ],
    static_libs: ["libqemud.ranchu"],
    header_libs: ["<EMAIL>"],
    cflags: [
        "-DLOG_TAG=\"<EMAIL>\"",
        "-DANDROID_BASE_UNIQUE_FD_DISABLE_IMPLICIT_CONVERSION",
    ],
}

<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2017 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<!--
This XML file declares which signature|privileged permissions should be granted to privileged
applications on GMS or Google-branded devices.
It allows additional grants on top of privapp-permissions-platform.xml
-->

<permissions>
    <privapp-permissions package="com.android.dialer">
        <permission name="android.permission.STATUS_BAR"/>
    </privapp-permissions>
</permissions>

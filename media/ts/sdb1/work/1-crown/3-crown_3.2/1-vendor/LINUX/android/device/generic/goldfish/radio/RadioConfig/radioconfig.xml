<?xml version='1.0' encoding='utf-8'?>
<!--
  ~ Copyright (C) 2020 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<mobile_data>
<meterness>

<enable>
<adb>
<string-array description="adb shell commands to turn on metered mobile data plan" num="6">
    <item value="am"/>
    <item value="start-foreground-service"/>
    <item value="-e"/>
    <item value="meter"/>
    <item value="on"/>
    <item value="com.android.emulator.radio.config/.MeterService"/>
</string-array>
</adb>
</enable>

<disable>
<adb>
<string-array description="adb shell commands to turn off metered mobile data plan" num="6">
    <item value="am"/>
    <item value="start-foreground-service"/>
    <item value="-e"/>
    <item value="meter"/>
    <item value="off"/>
    <item value="com.android.emulator.radio.config/.MeterService"/>
</string-array>
</adb>
</disable>

</meterness>
</mobile_data>

/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <cpu/barrier.h>
#include <plat/rtc.h>
#include <timer.h>
#include <platform.h>


void rtcInit(void)
{
    /* nope */
}

/* Set calendar alarm to go off after delay has expired. uint64_t delay must
 * be in valid uint64_t format and must be less than 32 s.  A negative value
 * for the 'ppm' param indicates the alarm has no accuracy requirements. */
int rtcSetWakeupTimer(uint64_t delay, int ppm)
{
    //TODO

    return 0;
}

uint64_t rtcGetTime(void)
{
    return 0;
}

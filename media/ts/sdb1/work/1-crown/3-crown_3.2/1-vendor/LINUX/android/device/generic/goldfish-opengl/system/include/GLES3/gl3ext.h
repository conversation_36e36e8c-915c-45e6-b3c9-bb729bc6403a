#ifndef __gl3ext_h_
#define __gl3ext_h_

/* $Revision: 17809 $ on $Date:: 2012-05-14 08:03:36 -0700 #$ */

/*
 * This document is licensed under the SGI Free Software B License Version
 * 2.0. For details, see http://oss.sgi.com/projects/FreeB/ .
 */

/* OpenGL ES 3 Extensions
 *
 * After an OES extension's interactions with OpenGl ES 3.0 have been documented,
 * its tokens and function definitions should be added to this file in a manner
 * that does not conflict with gl2ext.h or gl3.h.
 *
 * Tokens and function definitions for extensions that have become standard
 * features in OpenGL ES 3.0 will not be added to this file.
 *
 * Applications using OpenGL-ES-2-only extensions should include gl2ext.h
 */

#endif /* __gl3ext_h_ */


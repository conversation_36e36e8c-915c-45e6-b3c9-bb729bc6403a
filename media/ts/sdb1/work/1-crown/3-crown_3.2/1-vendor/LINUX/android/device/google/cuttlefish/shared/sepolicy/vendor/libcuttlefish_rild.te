type libcuttlefish_rild, domain;
type libcuttlefish_rild_exec, exec_type, vendor_file_type, file_type;

init_daemon_domain(libcuttlefish_rild)

hal_server_domain(libcuttlefish_rild, hal_telephony)

# Failing to create these sockets appears to be non-fatal
net_domain(libcuttlefish_rild)

get_prop(libcuttlefish_rild, cuttlefish_config_server_port_prop)

allow libcuttlefish_rild self:{ socket vsock_socket } create_socket_perms_no_ioctl;

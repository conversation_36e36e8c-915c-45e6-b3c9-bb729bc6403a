<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:card_view="http://schemas.android.com/apk/res-auto"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:focusable="false"
  android:orientation="vertical">

  <LinearLayout
    android:id="@+id/blocked_numbers_disabled_for_emergency"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="27dp"
    android:paddingBottom="29dp"
    android:paddingStart="@dimen/blocked_number_container_padding"
    android:paddingEnd="44dp"
    android:background="?android:attr/colorBackground"
    android:focusable="true"
    android:orientation="vertical"
    android:visibility="gone">

    <TextView
      style="@style/BlockedNumbersDescriptionTextStyle"
      android:textStyle="bold"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:text="@string/blocked_numbers_disabled_emergency_header_label"/>

    <TextView
      style="@style/BlockedNumbersDescriptionTextStyle"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:text="@string/blocked_numbers_disabled_emergency_desc"/>

  </LinearLayout>

  <android.support.v7.widget.CardView
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    card_view:cardCornerRadius="0dp">

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:background="@android:color/white"
      android:focusable="true"
      android:orientation="vertical">

      <TextView
        android:id="@+id/blocked_number_text_view"
        style="@android:style/TextAppearance.Material.Subhead"
        android:layout_width="wrap_content"
        android:layout_height="48dp"
        android:paddingStart="@dimen/blocked_number_container_padding"
        android:gravity="center_vertical"
        android:text="@string/block_list"
        android:textColor="?android:attr/textColorPrimary"/>

      <RelativeLayout
        android:id="@+id/import_settings"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
          android:id="@+id/import_description"
          style="@style/BlockedNumbersDescriptionTextStyle"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:paddingTop="11dp"
          android:paddingBottom="27dp"
          android:paddingStart="@dimen/blocked_number_container_padding"
          android:paddingEnd="@dimen/blocked_number_container_padding"
          android:text="@string/blocked_call_settings_import_description"
          android:textColor="?colorIcon"
          android:textSize="@dimen/blocked_number_settings_description_text_size"/>

        <Button
          android:id="@+id/import_button"
          style="@style/DialerFlatButtonStyle"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_marginEnd="@dimen/blocked_number_container_padding"
          android:layout_alignParentEnd="true"
          android:layout_below="@id/import_description"
          android:text="@string/blocked_call_settings_import_button"/>

        <Button
          android:id="@+id/view_numbers_button"
          style="@style/DialerFlatButtonStyle"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_marginEnd="8dp"
          android:layout_below="@id/import_description"
          android:layout_toStartOf="@id/import_button"
          android:text="@string/blocked_call_settings_view_numbers_button"/>

        <View
          android:layout_width="match_parent"
          android:layout_height="1dp"
          android:layout_marginTop="8dp"
          android:layout_below="@id/import_button"
          android:background="@color/dialer_divider_line_color"/>

      </RelativeLayout>

      <LinearLayout
        android:id="@+id/migrate_promo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
          android:id="@+id/migrate_promo_header"
          style="@android:style/TextAppearance.Material.Subhead"
          android:textStyle="bold"
          android:layout_width="match_parent"
          android:layout_height="48dp"
          android:paddingStart="@dimen/blocked_number_container_padding"
          android:paddingEnd="@dimen/blocked_number_container_padding"
          android:gravity="center_vertical"
          android:text="@string/migrate_blocked_numbers_dialog_title"
          android:textColor="?android:attr/textColorPrimary"/>

        <TextView
          android:id="@+id/migrate_promo_description"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:layout_marginBottom="@dimen/blocked_number_container_padding"
          android:layout_marginStart="@dimen/blocked_number_container_padding"
          android:layout_marginEnd="@dimen/blocked_number_container_padding"
          android:text="@string/migrate_blocked_numbers_dialog_message"
          android:textColor="?colorIcon"/>

        <Button
          android:id="@+id/migrate_promo_allow_button"
          style="@style/DialerPrimaryFlatButtonStyle"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_marginBottom="@dimen/blocked_number_container_padding"
          android:layout_marginStart="@dimen/blocked_number_container_padding"
          android:layout_marginEnd="@dimen/blocked_number_container_padding"
          android:layout_gravity="end"
          android:text="@string/migrate_blocked_numbers_dialog_allow_button"/>

        <View
          style="@style/FullWidthDivider"/>

      </LinearLayout>

      <View
        android:id="@+id/blocked_number_list_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginStart="72dp"
        android:background="@color/dialer_divider_line_color"/>

    </LinearLayout>

  </android.support.v7.widget.CardView>

</LinearLayout>

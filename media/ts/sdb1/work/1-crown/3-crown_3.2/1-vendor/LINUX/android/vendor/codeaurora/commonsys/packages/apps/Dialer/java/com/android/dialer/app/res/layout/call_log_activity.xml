<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2013 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/calllog_frame"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/colorBackground"
    android:orientation="vertical">
  <com.android.contacts.common.list.ViewPagerTabs
      android:id="@+id/viewpager_header"
      style="@style/DialtactsActionBarTabTextStyle"
      android:layout_width="match_parent"
      android:layout_height="@dimen/tab_height"
      android:layout_gravity="top"
      android:elevation="@dimen/tab_elevation"
      android:orientation="horizontal"
      android:textAllCaps="true"/>
  <android.support.v4.view.ViewPager
      android:id="@+id/call_log_pager"
      android:layout_width="match_parent"
      android:layout_height="0dp"
      android:layout_weight="1"/>
  <RelativeLayout
      android:id="@+id/floating_action_button_container"
      android:layout_width="0dp"
      android:layout_height="0dp"/>
</LinearLayout>

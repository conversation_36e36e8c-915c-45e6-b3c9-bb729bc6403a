<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<Included>
    <Decoders>
        <MediaCodec name="OMX.google.mpeg4.decoder" type="video/mp4v-es">
            <!-- profiles and levels:  ProfileSimple : Level3 -->
            <Limit name="size" min="2x2" max="352x288" />
            <Limit name="alignment" value="2x2" />
            <Limit name="block-size" value="16x16" />
            <Limit name="blocks-per-second" range="12-11880" />
            <Limit name="bitrate" range="1-384000" />
            <Limit name="performance-point-1920x1080" value="30" />
            <Feature name="adaptive-playback" />
        </MediaCodec>
        <MediaCodec name="OMX.google.h263.decoder" type="video/3gpp">
            <!-- profiles and levels:  ProfileBaseline : Level30, ProfileBaseline : Level45
                    ProfileISWV2 : Level30, ProfileISWV2 : Level45 -->
            <Limit name="size" min="2x2" max="352x288" />
            <Limit name="alignment" value="2x2" />
            <Limit name="bitrate" range="1-384000" />
            <Limit name="performance-point-1920x1080" value="30" />
            <Feature name="adaptive-playback" />
        </MediaCodec>
        <MediaCodec name="OMX.android.goldfish.h264.decoder" type="video/avc">
            <Limit name="size" min="96x96" max="3840x2160" />
            <Limit name="alignment" value="2x2" />
            <Limit name="block-size" value="16x16" />
            <Limit name="blocks-per-second" range="24-2073600" />
            <Limit name="bitrate" range="1-120000000" />
            <Limit name="frame-rate" range="1-480" />
            <Limit name="performance-point-3840x2160" value="60" />
            <Limit name="measured-frame-rate-320x240" range="257-266" />
            <Limit name="measured-frame-rate-720x480" range="262-264" />
            <Limit name="measured-frame-rate-1280x720" range="227-251" />
            <Limit name="measured-frame-rate-1920x1080" range="235-247" />
            <Limit name="measured-frame-rate-3840x2160" range="235-247" />
            <Feature name="adaptive-playback" />
        </MediaCodec>
        <MediaCodec name="OMX.google.h264.decoder" type="video/avc">
            <Limit name="size" min="2x2" max="2560x2560" />
            <Limit name="alignment" value="2x2" />
            <Limit name="block-size" value="16x16" />
            <Limit name="blocks-per-second" range="24-2073600" />
            <Limit name="bitrate" range="1-120000000" />
            <Limit name="frame-rate" range="1-480" />
            <Limit name="concurrent-instances" max="16" />
            <Limit name="performance-point-1920x1088" value="30" />
            <Limit name="measured-frame-rate-320x240" range="183-183" />
            <Limit name="measured-frame-rate-720x480" range="181-181" />
            <Limit name="measured-frame-rate-1280x720" range="182-184" />
            <Limit name="measured-frame-rate-1920x1080" range="30-40" />
            <Feature name="adaptive-playback" />
        </MediaCodec>
        <MediaCodec name="OMX.google.hevc.decoder" type="video/hevc">
            <!-- profiles and levels:  ProfileMain : MainTierLevel51 -->
            <Limit name="size" min="2x2" max="2048x2048" />
            <Limit name="alignment" value="2x2" />
            <Limit name="block-size" value="8x8" />
            <Limit name="block-count" range="1-139264" />
            <Limit name="blocks-per-second" range="1-2000000" />
            <Limit name="bitrate" range="1-10000000" />
            <Limit name="performance-point-1920x1080" value="30" />
            <Feature name="adaptive-playback" />
        </MediaCodec>
        <MediaCodec name="OMX.android.goldfish.vp9.decoder" type="video/x-vnd.on2.vp9">
            <Limit name="size" min="96x96" max="3840x2160" />
            <Limit name="alignment" value="2x2" />
            <Limit name="block-size" value="16x16" />
            <Limit name="blocks-per-second" min="24" max="2073600" />
            <Limit name="bitrate" range="1-120000000" />
            <Limit name="frame-rate" range="1-480" />
            <Limit name="performance-point-3840x2160" value="60" />
            <Limit name="measured-frame-rate-320x180" range="237-258" />
            <Limit name="measured-frame-rate-640x360" range="237-258" />
            <Limit name="measured-frame-rate-1280x720" range="237-258" />
            <Limit name="measured-frame-rate-1920x1080" range="293-302" />
            <Limit name="measured-frame-rate-3840x2160" range="150-150" />
            <Feature name="adaptive-playback" />
        </MediaCodec>
        <MediaCodec name="OMX.android.goldfish.vp8.decoder" type="video/x-vnd.on2.vp8">
            <Limit name="size" min="96x96" max="3840x2160" />
            <Limit name="alignment" value="2x2" />
            <Limit name="block-size" value="16x16" />
            <Limit name="blocks-per-second" min="24" max="2073600" />
            <Limit name="bitrate" range="1-120000000" />
            <Limit name="frame-rate" range="1-480" />
            <Limit name="performance-point-3840x2160" value="60" />
            <Limit name="measured-frame-rate-320x180" range="743-817" />
            <Limit name="measured-frame-rate-640x360" range="237-258" />
            <Limit name="measured-frame-rate-1280x720" range="237-258" />
            <Limit name="measured-frame-rate-1920x1080" range="30-160" />
            <Limit name="measured-frame-rate-3840x2160" range="30-90" />
            <Feature name="adaptive-playback" />
        </MediaCodec>
        <MediaCodec name="OMX.google.vp8.decoder" type="video/x-vnd.on2.vp8">
            <Limit name="size" min="2x2" max="2560x2560" />
            <Limit name="alignment" value="2x2" />
            <Limit name="block-size" value="16x16" />
            <Limit name="blocks-per-second" min="24" max="2073600" />
            <Limit name="bitrate" range="1-120000000" />
            <Limit name="frame-rate" range="1-480" />
            <Limit name="performance-point-1920x1088" value="60" />
            <Limit name="measured-frame-rate-320x240" range="183-183" />
            <Limit name="measured-frame-rate-720x480" range="181-181" />
            <Limit name="measured-frame-rate-1280x720" range="182-184" />
            <Limit name="measured-frame-rate-1920x1088" range="30-50" />
            <Limit name="measured-frame-rate-2560x1440" range="30-40" />
            <Feature name="adaptive-playback" />
        </MediaCodec>
        <MediaCodec name="OMX.google.vp9.decoder" type="video/x-vnd.on2.vp9">
            <Limit name="size" min="2x2" max="2560x2560" />
            <Limit name="alignment" value="2x2" />
            <Limit name="block-size" value="16x16" />
            <Limit name="blocks-per-second" min="24" max="2073600" />
            <Limit name="bitrate" range="1-120000000" />
            <Limit name="frame-rate" range="1-480" />
            <Limit name="performance-point-1920x1088" value="60" />
            <Limit name="measured-frame-rate-320x240" range="183-183" />
            <Limit name="measured-frame-rate-720x480" range="181-181" />
            <Limit name="measured-frame-rate-1280x720" range="121-125" />
            <Limit name="measured-frame-rate-1920x1088" range="30-50" />
            <Limit name="measured-frame-rate-2560x1440" range="30-40" />
            <Feature name="adaptive-playback" />
        </MediaCodec>
    </Decoders>

    <Encoders>
        <MediaCodec name="OMX.google.h263.encoder" type="video/3gpp">
            <!-- profiles and levels:  ProfileBaseline : Level45 -->
            <Limit name="size" min="176x144" max="176x144" />
            <Limit name="alignment" value="16x16" />
            <Limit name="bitrate" range="1-128000" />
        </MediaCodec>
        <MediaCodec name="OMX.google.h264.encoder" type="video/avc">
            <!-- profiles and levels:  ProfileBaseline : Level41 -->
            <Limit name="size" min="16x16" max="1920x1088" />
            <Limit name="alignment" value="2x2" />
            <Limit name="block-size" value="16x16" />
            <Limit name="blocks-per-second" range="1-244800" />
            <!-- Changed range from 12000000 to 20000000 for b/31648354 -->
            <Limit name="bitrate" range="1-20000000" />
            <Feature name="intra-refresh" />
        </MediaCodec>
        <MediaCodec name="OMX.google.mpeg4.encoder" type="video/mp4v-es">
            <!-- profiles and levels:  ProfileCore : Level2 -->
            <Limit name="size" min="16x16" max="176x144" />
            <Limit name="alignment" value="16x16" />
            <Limit name="block-size" value="16x16" />
            <Limit name="blocks-per-second" range="12-1485" />
            <Limit name="bitrate" range="1-64000" />
        </MediaCodec>
        <MediaCodec name="OMX.google.vp8.encoder" type="video/x-vnd.on2.vp8">
            <!-- profiles and levels:  ProfileMain : Level_Version0-3 -->
            <Limit name="size" min="2x2" max="2048x2048" />
            <Limit name="alignment" value="2x2" />
            <Limit name="bitrate" range="1-40000000" />
            <Feature name="bitrate-modes" value="VBR,CBR" />
        </MediaCodec>
    </Encoders>
</Included>

<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout
  xmlns:android="http://schemas.android.com/apk/res/android"
  android:id="@+id/caller_information"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:paddingStart="@dimen/blocked_number_horizontal_margin"
  android:background="@android:color/white"
  android:baselineAligned="false"
  android:focusable="true"
  android:gravity="center_vertical"
  android:orientation="horizontal">

  <QuickContactBadge
    android:id="@+id/quick_contact_photo"
    android:layout_width="@dimen/contact_photo_size"
    android:layout_height="@dimen/contact_photo_size"
    android:layout_marginTop="@dimen/blocked_number_top_margin"
    android:layout_marginBottom="@dimen/blocked_number_bottom_margin"
    android:focusable="true"/>
  <LinearLayout
    android:layout_width="0dp"
    android:layout_height="match_parent"
    android:layout_weight="1"
    android:layout_marginStart="@dimen/blocked_number_horizontal_margin"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <TextView
      android:id="@+id/caller_name"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:includeFontPadding="false"
      style="@style/Dialer.TextAppearance.Primary.Ellipsize"/>

    <TextView
      android:id="@+id/caller_number"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      style="@style/Dialer.TextAppearance.Secondary.Ellipsize"/>
  </LinearLayout>

  <ImageView
    android:id="@+id/delete_button"
    android:layout_width="@dimen/blocked_number_delete_icon_size"
    android:layout_height="@dimen/blocked_number_delete_icon_size"
    android:layout_marginEnd="24dp"
    android:background="?android:attr/selectableItemBackgroundBorderless"
    android:contentDescription="@string/description_blocked_number_list_delete"
    android:scaleType="center"
    android:src="@drawable/ic_remove"
    android:tint="?colorIcon"/>
</LinearLayout>

// Copyright (C) 2018 The Android Open Source Project
// Copyright (C) 2018 Google Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Autogenerated module VkEncoder
// (header) generated by android/android-emugl/host/libs/libOpenglRender/vulkan-registry/xml/genvk.py -registry android/android-emugl/host/libs/libOpenglRender/vulkan-registry/xml/vk.xml cereal -o android/android-emugl/host/libs/libOpenglRender/vulkan/cereal
// Please do not modify directly;
// re-run android/scripts/generate-vulkan-sources.sh,
// or directly from Python by defining:
// VULKAN_REGISTRY_XML_DIR : Directory containing genvk.py and vk.xml
// CEREAL_OUTPUT_DIR: Where to put the generated sources.
// python3 $VULKAN_REGISTRY_XML_DIR/genvk.py -registry $VULKAN_REGISTRY_XML_DIR/vk.xml cereal -o $CEREAL_OUTPUT_DIR

#pragma once

#include <vulkan/vulkan.h>


#include "goldfish_vk_private_defs.h"
#include <functional>
#include <memory>
class IOStream;


namespace goldfish_vk {


class VkEncoder {
public:
    VkEncoder(IOStream* stream);
    ~VkEncoder();

    void flush();

    using CleanupCallback = std::function<void()>;
    void registerCleanupCallback(void* handle, CleanupCallback cb);
    void unregisterCleanupCallback(void* handle);
#ifdef VK_VERSION_1_0
    VkResult vkCreateInstance(
    const VkInstanceCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkInstance* pInstance);
    void vkDestroyInstance(
    VkInstance instance,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkEnumeratePhysicalDevices(
    VkInstance instance,
        uint32_t* pPhysicalDeviceCount,
        VkPhysicalDevice* pPhysicalDevices);
    void vkGetPhysicalDeviceFeatures(
    VkPhysicalDevice physicalDevice,
        VkPhysicalDeviceFeatures* pFeatures);
    void vkGetPhysicalDeviceFormatProperties(
    VkPhysicalDevice physicalDevice,
        VkFormat format,
        VkFormatProperties* pFormatProperties);
    VkResult vkGetPhysicalDeviceImageFormatProperties(
    VkPhysicalDevice physicalDevice,
        VkFormat format,
        VkImageType type,
        VkImageTiling tiling,
        VkImageUsageFlags usage,
        VkImageCreateFlags flags,
        VkImageFormatProperties* pImageFormatProperties);
    void vkGetPhysicalDeviceProperties(
    VkPhysicalDevice physicalDevice,
        VkPhysicalDeviceProperties* pProperties);
    void vkGetPhysicalDeviceQueueFamilyProperties(
    VkPhysicalDevice physicalDevice,
        uint32_t* pQueueFamilyPropertyCount,
        VkQueueFamilyProperties* pQueueFamilyProperties);
    void vkGetPhysicalDeviceMemoryProperties(
    VkPhysicalDevice physicalDevice,
        VkPhysicalDeviceMemoryProperties* pMemoryProperties);
    PFN_vkVoidFunction vkGetInstanceProcAddr(
    VkInstance instance,
        const char* pName);
    PFN_vkVoidFunction vkGetDeviceProcAddr(
    VkDevice device,
        const char* pName);
    VkResult vkCreateDevice(
    VkPhysicalDevice physicalDevice,
        const VkDeviceCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkDevice* pDevice);
    void vkDestroyDevice(
    VkDevice device,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkEnumerateInstanceExtensionProperties(
    const char* pLayerName,
        uint32_t* pPropertyCount,
        VkExtensionProperties* pProperties);
    VkResult vkEnumerateDeviceExtensionProperties(
    VkPhysicalDevice physicalDevice,
        const char* pLayerName,
        uint32_t* pPropertyCount,
        VkExtensionProperties* pProperties);
    VkResult vkEnumerateInstanceLayerProperties(
    uint32_t* pPropertyCount,
        VkLayerProperties* pProperties);
    VkResult vkEnumerateDeviceLayerProperties(
    VkPhysicalDevice physicalDevice,
        uint32_t* pPropertyCount,
        VkLayerProperties* pProperties);
    void vkGetDeviceQueue(
    VkDevice device,
        uint32_t queueFamilyIndex,
        uint32_t queueIndex,
        VkQueue* pQueue);
    VkResult vkQueueSubmit(
    VkQueue queue,
        uint32_t submitCount,
        const VkSubmitInfo* pSubmits,
        VkFence fence);
    VkResult vkQueueWaitIdle(
    VkQueue queue);
    VkResult vkDeviceWaitIdle(
    VkDevice device);
    VkResult vkAllocateMemory(
    VkDevice device,
        const VkMemoryAllocateInfo* pAllocateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkDeviceMemory* pMemory);
    void vkFreeMemory(
    VkDevice device,
        VkDeviceMemory memory,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkMapMemory(
    VkDevice device,
        VkDeviceMemory memory,
        VkDeviceSize offset,
        VkDeviceSize size,
        VkMemoryMapFlags flags,
        void** ppData);
    void vkUnmapMemory(
    VkDevice device,
        VkDeviceMemory memory);
    VkResult vkFlushMappedMemoryRanges(
    VkDevice device,
        uint32_t memoryRangeCount,
        const VkMappedMemoryRange* pMemoryRanges);
    VkResult vkInvalidateMappedMemoryRanges(
    VkDevice device,
        uint32_t memoryRangeCount,
        const VkMappedMemoryRange* pMemoryRanges);
    void vkGetDeviceMemoryCommitment(
    VkDevice device,
        VkDeviceMemory memory,
        VkDeviceSize* pCommittedMemoryInBytes);
    VkResult vkBindBufferMemory(
    VkDevice device,
        VkBuffer buffer,
        VkDeviceMemory memory,
        VkDeviceSize memoryOffset);
    VkResult vkBindImageMemory(
    VkDevice device,
        VkImage image,
        VkDeviceMemory memory,
        VkDeviceSize memoryOffset);
    void vkGetBufferMemoryRequirements(
    VkDevice device,
        VkBuffer buffer,
        VkMemoryRequirements* pMemoryRequirements);
    void vkGetImageMemoryRequirements(
    VkDevice device,
        VkImage image,
        VkMemoryRequirements* pMemoryRequirements);
    void vkGetImageSparseMemoryRequirements(
    VkDevice device,
        VkImage image,
        uint32_t* pSparseMemoryRequirementCount,
        VkSparseImageMemoryRequirements* pSparseMemoryRequirements);
    void vkGetPhysicalDeviceSparseImageFormatProperties(
    VkPhysicalDevice physicalDevice,
        VkFormat format,
        VkImageType type,
        VkSampleCountFlagBits samples,
        VkImageUsageFlags usage,
        VkImageTiling tiling,
        uint32_t* pPropertyCount,
        VkSparseImageFormatProperties* pProperties);
    VkResult vkQueueBindSparse(
    VkQueue queue,
        uint32_t bindInfoCount,
        const VkBindSparseInfo* pBindInfo,
        VkFence fence);
    VkResult vkCreateFence(
    VkDevice device,
        const VkFenceCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkFence* pFence);
    void vkDestroyFence(
    VkDevice device,
        VkFence fence,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkResetFences(
    VkDevice device,
        uint32_t fenceCount,
        const VkFence* pFences);
    VkResult vkGetFenceStatus(
    VkDevice device,
        VkFence fence);
    VkResult vkWaitForFences(
    VkDevice device,
        uint32_t fenceCount,
        const VkFence* pFences,
        VkBool32 waitAll,
        uint64_t timeout);
    VkResult vkCreateSemaphore(
    VkDevice device,
        const VkSemaphoreCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkSemaphore* pSemaphore);
    void vkDestroySemaphore(
    VkDevice device,
        VkSemaphore semaphore,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkCreateEvent(
    VkDevice device,
        const VkEventCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkEvent* pEvent);
    void vkDestroyEvent(
    VkDevice device,
        VkEvent event,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkGetEventStatus(
    VkDevice device,
        VkEvent event);
    VkResult vkSetEvent(
    VkDevice device,
        VkEvent event);
    VkResult vkResetEvent(
    VkDevice device,
        VkEvent event);
    VkResult vkCreateQueryPool(
    VkDevice device,
        const VkQueryPoolCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkQueryPool* pQueryPool);
    void vkDestroyQueryPool(
    VkDevice device,
        VkQueryPool queryPool,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkGetQueryPoolResults(
    VkDevice device,
        VkQueryPool queryPool,
        uint32_t firstQuery,
        uint32_t queryCount,
        size_t dataSize,
        void* pData,
        VkDeviceSize stride,
        VkQueryResultFlags flags);
    VkResult vkCreateBuffer(
    VkDevice device,
        const VkBufferCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkBuffer* pBuffer);
    void vkDestroyBuffer(
    VkDevice device,
        VkBuffer buffer,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkCreateBufferView(
    VkDevice device,
        const VkBufferViewCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkBufferView* pView);
    void vkDestroyBufferView(
    VkDevice device,
        VkBufferView bufferView,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkCreateImage(
    VkDevice device,
        const VkImageCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkImage* pImage);
    void vkDestroyImage(
    VkDevice device,
        VkImage image,
        const VkAllocationCallbacks* pAllocator);
    void vkGetImageSubresourceLayout(
    VkDevice device,
        VkImage image,
        const VkImageSubresource* pSubresource,
        VkSubresourceLayout* pLayout);
    VkResult vkCreateImageView(
    VkDevice device,
        const VkImageViewCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkImageView* pView);
    void vkDestroyImageView(
    VkDevice device,
        VkImageView imageView,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkCreateShaderModule(
    VkDevice device,
        const VkShaderModuleCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkShaderModule* pShaderModule);
    void vkDestroyShaderModule(
    VkDevice device,
        VkShaderModule shaderModule,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkCreatePipelineCache(
    VkDevice device,
        const VkPipelineCacheCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkPipelineCache* pPipelineCache);
    void vkDestroyPipelineCache(
    VkDevice device,
        VkPipelineCache pipelineCache,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkGetPipelineCacheData(
    VkDevice device,
        VkPipelineCache pipelineCache,
        size_t* pDataSize,
        void* pData);
    VkResult vkMergePipelineCaches(
    VkDevice device,
        VkPipelineCache dstCache,
        uint32_t srcCacheCount,
        const VkPipelineCache* pSrcCaches);
    VkResult vkCreateGraphicsPipelines(
    VkDevice device,
        VkPipelineCache pipelineCache,
        uint32_t createInfoCount,
        const VkGraphicsPipelineCreateInfo* pCreateInfos,
        const VkAllocationCallbacks* pAllocator,
        VkPipeline* pPipelines);
    VkResult vkCreateComputePipelines(
    VkDevice device,
        VkPipelineCache pipelineCache,
        uint32_t createInfoCount,
        const VkComputePipelineCreateInfo* pCreateInfos,
        const VkAllocationCallbacks* pAllocator,
        VkPipeline* pPipelines);
    void vkDestroyPipeline(
    VkDevice device,
        VkPipeline pipeline,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkCreatePipelineLayout(
    VkDevice device,
        const VkPipelineLayoutCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkPipelineLayout* pPipelineLayout);
    void vkDestroyPipelineLayout(
    VkDevice device,
        VkPipelineLayout pipelineLayout,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkCreateSampler(
    VkDevice device,
        const VkSamplerCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkSampler* pSampler);
    void vkDestroySampler(
    VkDevice device,
        VkSampler sampler,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkCreateDescriptorSetLayout(
    VkDevice device,
        const VkDescriptorSetLayoutCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkDescriptorSetLayout* pSetLayout);
    void vkDestroyDescriptorSetLayout(
    VkDevice device,
        VkDescriptorSetLayout descriptorSetLayout,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkCreateDescriptorPool(
    VkDevice device,
        const VkDescriptorPoolCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkDescriptorPool* pDescriptorPool);
    void vkDestroyDescriptorPool(
    VkDevice device,
        VkDescriptorPool descriptorPool,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkResetDescriptorPool(
    VkDevice device,
        VkDescriptorPool descriptorPool,
        VkDescriptorPoolResetFlags flags);
    VkResult vkAllocateDescriptorSets(
    VkDevice device,
        const VkDescriptorSetAllocateInfo* pAllocateInfo,
        VkDescriptorSet* pDescriptorSets);
    VkResult vkFreeDescriptorSets(
    VkDevice device,
        VkDescriptorPool descriptorPool,
        uint32_t descriptorSetCount,
        const VkDescriptorSet* pDescriptorSets);
    void vkUpdateDescriptorSets(
    VkDevice device,
        uint32_t descriptorWriteCount,
        const VkWriteDescriptorSet* pDescriptorWrites,
        uint32_t descriptorCopyCount,
        const VkCopyDescriptorSet* pDescriptorCopies);
    VkResult vkCreateFramebuffer(
    VkDevice device,
        const VkFramebufferCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkFramebuffer* pFramebuffer);
    void vkDestroyFramebuffer(
    VkDevice device,
        VkFramebuffer framebuffer,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkCreateRenderPass(
    VkDevice device,
        const VkRenderPassCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkRenderPass* pRenderPass);
    void vkDestroyRenderPass(
    VkDevice device,
        VkRenderPass renderPass,
        const VkAllocationCallbacks* pAllocator);
    void vkGetRenderAreaGranularity(
    VkDevice device,
        VkRenderPass renderPass,
        VkExtent2D* pGranularity);
    VkResult vkCreateCommandPool(
    VkDevice device,
        const VkCommandPoolCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkCommandPool* pCommandPool);
    void vkDestroyCommandPool(
    VkDevice device,
        VkCommandPool commandPool,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkResetCommandPool(
    VkDevice device,
        VkCommandPool commandPool,
        VkCommandPoolResetFlags flags);
    VkResult vkAllocateCommandBuffers(
    VkDevice device,
        const VkCommandBufferAllocateInfo* pAllocateInfo,
        VkCommandBuffer* pCommandBuffers);
    void vkFreeCommandBuffers(
    VkDevice device,
        VkCommandPool commandPool,
        uint32_t commandBufferCount,
        const VkCommandBuffer* pCommandBuffers);
    VkResult vkBeginCommandBuffer(
    VkCommandBuffer commandBuffer,
        const VkCommandBufferBeginInfo* pBeginInfo);
    VkResult vkEndCommandBuffer(
    VkCommandBuffer commandBuffer);
    VkResult vkResetCommandBuffer(
    VkCommandBuffer commandBuffer,
        VkCommandBufferResetFlags flags);
    void vkCmdBindPipeline(
    VkCommandBuffer commandBuffer,
        VkPipelineBindPoint pipelineBindPoint,
        VkPipeline pipeline);
    void vkCmdSetViewport(
    VkCommandBuffer commandBuffer,
        uint32_t firstViewport,
        uint32_t viewportCount,
        const VkViewport* pViewports);
    void vkCmdSetScissor(
    VkCommandBuffer commandBuffer,
        uint32_t firstScissor,
        uint32_t scissorCount,
        const VkRect2D* pScissors);
    void vkCmdSetLineWidth(
    VkCommandBuffer commandBuffer,
        float lineWidth);
    void vkCmdSetDepthBias(
    VkCommandBuffer commandBuffer,
        float depthBiasConstantFactor,
        float depthBiasClamp,
        float depthBiasSlopeFactor);
    void vkCmdSetBlendConstants(
    VkCommandBuffer commandBuffer,
        const float blendConstants[4]);
    void vkCmdSetDepthBounds(
    VkCommandBuffer commandBuffer,
        float minDepthBounds,
        float maxDepthBounds);
    void vkCmdSetStencilCompareMask(
    VkCommandBuffer commandBuffer,
        VkStencilFaceFlags faceMask,
        uint32_t compareMask);
    void vkCmdSetStencilWriteMask(
    VkCommandBuffer commandBuffer,
        VkStencilFaceFlags faceMask,
        uint32_t writeMask);
    void vkCmdSetStencilReference(
    VkCommandBuffer commandBuffer,
        VkStencilFaceFlags faceMask,
        uint32_t reference);
    void vkCmdBindDescriptorSets(
    VkCommandBuffer commandBuffer,
        VkPipelineBindPoint pipelineBindPoint,
        VkPipelineLayout layout,
        uint32_t firstSet,
        uint32_t descriptorSetCount,
        const VkDescriptorSet* pDescriptorSets,
        uint32_t dynamicOffsetCount,
        const uint32_t* pDynamicOffsets);
    void vkCmdBindIndexBuffer(
    VkCommandBuffer commandBuffer,
        VkBuffer buffer,
        VkDeviceSize offset,
        VkIndexType indexType);
    void vkCmdBindVertexBuffers(
    VkCommandBuffer commandBuffer,
        uint32_t firstBinding,
        uint32_t bindingCount,
        const VkBuffer* pBuffers,
        const VkDeviceSize* pOffsets);
    void vkCmdDraw(
    VkCommandBuffer commandBuffer,
        uint32_t vertexCount,
        uint32_t instanceCount,
        uint32_t firstVertex,
        uint32_t firstInstance);
    void vkCmdDrawIndexed(
    VkCommandBuffer commandBuffer,
        uint32_t indexCount,
        uint32_t instanceCount,
        uint32_t firstIndex,
        int32_t vertexOffset,
        uint32_t firstInstance);
    void vkCmdDrawIndirect(
    VkCommandBuffer commandBuffer,
        VkBuffer buffer,
        VkDeviceSize offset,
        uint32_t drawCount,
        uint32_t stride);
    void vkCmdDrawIndexedIndirect(
    VkCommandBuffer commandBuffer,
        VkBuffer buffer,
        VkDeviceSize offset,
        uint32_t drawCount,
        uint32_t stride);
    void vkCmdDispatch(
    VkCommandBuffer commandBuffer,
        uint32_t groupCountX,
        uint32_t groupCountY,
        uint32_t groupCountZ);
    void vkCmdDispatchIndirect(
    VkCommandBuffer commandBuffer,
        VkBuffer buffer,
        VkDeviceSize offset);
    void vkCmdCopyBuffer(
    VkCommandBuffer commandBuffer,
        VkBuffer srcBuffer,
        VkBuffer dstBuffer,
        uint32_t regionCount,
        const VkBufferCopy* pRegions);
    void vkCmdCopyImage(
    VkCommandBuffer commandBuffer,
        VkImage srcImage,
        VkImageLayout srcImageLayout,
        VkImage dstImage,
        VkImageLayout dstImageLayout,
        uint32_t regionCount,
        const VkImageCopy* pRegions);
    void vkCmdBlitImage(
    VkCommandBuffer commandBuffer,
        VkImage srcImage,
        VkImageLayout srcImageLayout,
        VkImage dstImage,
        VkImageLayout dstImageLayout,
        uint32_t regionCount,
        const VkImageBlit* pRegions,
        VkFilter filter);
    void vkCmdCopyBufferToImage(
    VkCommandBuffer commandBuffer,
        VkBuffer srcBuffer,
        VkImage dstImage,
        VkImageLayout dstImageLayout,
        uint32_t regionCount,
        const VkBufferImageCopy* pRegions);
    void vkCmdCopyImageToBuffer(
    VkCommandBuffer commandBuffer,
        VkImage srcImage,
        VkImageLayout srcImageLayout,
        VkBuffer dstBuffer,
        uint32_t regionCount,
        const VkBufferImageCopy* pRegions);
    void vkCmdUpdateBuffer(
    VkCommandBuffer commandBuffer,
        VkBuffer dstBuffer,
        VkDeviceSize dstOffset,
        VkDeviceSize dataSize,
        const void* pData);
    void vkCmdFillBuffer(
    VkCommandBuffer commandBuffer,
        VkBuffer dstBuffer,
        VkDeviceSize dstOffset,
        VkDeviceSize size,
        uint32_t data);
    void vkCmdClearColorImage(
    VkCommandBuffer commandBuffer,
        VkImage image,
        VkImageLayout imageLayout,
        const VkClearColorValue* pColor,
        uint32_t rangeCount,
        const VkImageSubresourceRange* pRanges);
    void vkCmdClearDepthStencilImage(
    VkCommandBuffer commandBuffer,
        VkImage image,
        VkImageLayout imageLayout,
        const VkClearDepthStencilValue* pDepthStencil,
        uint32_t rangeCount,
        const VkImageSubresourceRange* pRanges);
    void vkCmdClearAttachments(
    VkCommandBuffer commandBuffer,
        uint32_t attachmentCount,
        const VkClearAttachment* pAttachments,
        uint32_t rectCount,
        const VkClearRect* pRects);
    void vkCmdResolveImage(
    VkCommandBuffer commandBuffer,
        VkImage srcImage,
        VkImageLayout srcImageLayout,
        VkImage dstImage,
        VkImageLayout dstImageLayout,
        uint32_t regionCount,
        const VkImageResolve* pRegions);
    void vkCmdSetEvent(
    VkCommandBuffer commandBuffer,
        VkEvent event,
        VkPipelineStageFlags stageMask);
    void vkCmdResetEvent(
    VkCommandBuffer commandBuffer,
        VkEvent event,
        VkPipelineStageFlags stageMask);
    void vkCmdWaitEvents(
    VkCommandBuffer commandBuffer,
        uint32_t eventCount,
        const VkEvent* pEvents,
        VkPipelineStageFlags srcStageMask,
        VkPipelineStageFlags dstStageMask,
        uint32_t memoryBarrierCount,
        const VkMemoryBarrier* pMemoryBarriers,
        uint32_t bufferMemoryBarrierCount,
        const VkBufferMemoryBarrier* pBufferMemoryBarriers,
        uint32_t imageMemoryBarrierCount,
        const VkImageMemoryBarrier* pImageMemoryBarriers);
    void vkCmdPipelineBarrier(
    VkCommandBuffer commandBuffer,
        VkPipelineStageFlags srcStageMask,
        VkPipelineStageFlags dstStageMask,
        VkDependencyFlags dependencyFlags,
        uint32_t memoryBarrierCount,
        const VkMemoryBarrier* pMemoryBarriers,
        uint32_t bufferMemoryBarrierCount,
        const VkBufferMemoryBarrier* pBufferMemoryBarriers,
        uint32_t imageMemoryBarrierCount,
        const VkImageMemoryBarrier* pImageMemoryBarriers);
    void vkCmdBeginQuery(
    VkCommandBuffer commandBuffer,
        VkQueryPool queryPool,
        uint32_t query,
        VkQueryControlFlags flags);
    void vkCmdEndQuery(
    VkCommandBuffer commandBuffer,
        VkQueryPool queryPool,
        uint32_t query);
    void vkCmdResetQueryPool(
    VkCommandBuffer commandBuffer,
        VkQueryPool queryPool,
        uint32_t firstQuery,
        uint32_t queryCount);
    void vkCmdWriteTimestamp(
    VkCommandBuffer commandBuffer,
        VkPipelineStageFlagBits pipelineStage,
        VkQueryPool queryPool,
        uint32_t query);
    void vkCmdCopyQueryPoolResults(
    VkCommandBuffer commandBuffer,
        VkQueryPool queryPool,
        uint32_t firstQuery,
        uint32_t queryCount,
        VkBuffer dstBuffer,
        VkDeviceSize dstOffset,
        VkDeviceSize stride,
        VkQueryResultFlags flags);
    void vkCmdPushConstants(
    VkCommandBuffer commandBuffer,
        VkPipelineLayout layout,
        VkShaderStageFlags stageFlags,
        uint32_t offset,
        uint32_t size,
        const void* pValues);
    void vkCmdBeginRenderPass(
    VkCommandBuffer commandBuffer,
        const VkRenderPassBeginInfo* pRenderPassBegin,
        VkSubpassContents contents);
    void vkCmdNextSubpass(
    VkCommandBuffer commandBuffer,
        VkSubpassContents contents);
    void vkCmdEndRenderPass(
    VkCommandBuffer commandBuffer);
    void vkCmdExecuteCommands(
    VkCommandBuffer commandBuffer,
        uint32_t commandBufferCount,
        const VkCommandBuffer* pCommandBuffers);
#endif
#ifdef VK_VERSION_1_1
    VkResult vkEnumerateInstanceVersion(
    uint32_t* pApiVersion);
    VkResult vkBindBufferMemory2(
    VkDevice device,
        uint32_t bindInfoCount,
        const VkBindBufferMemoryInfo* pBindInfos);
    VkResult vkBindImageMemory2(
    VkDevice device,
        uint32_t bindInfoCount,
        const VkBindImageMemoryInfo* pBindInfos);
    void vkGetDeviceGroupPeerMemoryFeatures(
    VkDevice device,
        uint32_t heapIndex,
        uint32_t localDeviceIndex,
        uint32_t remoteDeviceIndex,
        VkPeerMemoryFeatureFlags* pPeerMemoryFeatures);
    void vkCmdSetDeviceMask(
    VkCommandBuffer commandBuffer,
        uint32_t deviceMask);
    void vkCmdDispatchBase(
    VkCommandBuffer commandBuffer,
        uint32_t baseGroupX,
        uint32_t baseGroupY,
        uint32_t baseGroupZ,
        uint32_t groupCountX,
        uint32_t groupCountY,
        uint32_t groupCountZ);
    VkResult vkEnumeratePhysicalDeviceGroups(
    VkInstance instance,
        uint32_t* pPhysicalDeviceGroupCount,
        VkPhysicalDeviceGroupProperties* pPhysicalDeviceGroupProperties);
    void vkGetImageMemoryRequirements2(
    VkDevice device,
        const VkImageMemoryRequirementsInfo2* pInfo,
        VkMemoryRequirements2* pMemoryRequirements);
    void vkGetBufferMemoryRequirements2(
    VkDevice device,
        const VkBufferMemoryRequirementsInfo2* pInfo,
        VkMemoryRequirements2* pMemoryRequirements);
    void vkGetImageSparseMemoryRequirements2(
    VkDevice device,
        const VkImageSparseMemoryRequirementsInfo2* pInfo,
        uint32_t* pSparseMemoryRequirementCount,
        VkSparseImageMemoryRequirements2* pSparseMemoryRequirements);
    void vkGetPhysicalDeviceFeatures2(
    VkPhysicalDevice physicalDevice,
        VkPhysicalDeviceFeatures2* pFeatures);
    void vkGetPhysicalDeviceProperties2(
    VkPhysicalDevice physicalDevice,
        VkPhysicalDeviceProperties2* pProperties);
    void vkGetPhysicalDeviceFormatProperties2(
    VkPhysicalDevice physicalDevice,
        VkFormat format,
        VkFormatProperties2* pFormatProperties);
    VkResult vkGetPhysicalDeviceImageFormatProperties2(
    VkPhysicalDevice physicalDevice,
        const VkPhysicalDeviceImageFormatInfo2* pImageFormatInfo,
        VkImageFormatProperties2* pImageFormatProperties);
    void vkGetPhysicalDeviceQueueFamilyProperties2(
    VkPhysicalDevice physicalDevice,
        uint32_t* pQueueFamilyPropertyCount,
        VkQueueFamilyProperties2* pQueueFamilyProperties);
    void vkGetPhysicalDeviceMemoryProperties2(
    VkPhysicalDevice physicalDevice,
        VkPhysicalDeviceMemoryProperties2* pMemoryProperties);
    void vkGetPhysicalDeviceSparseImageFormatProperties2(
    VkPhysicalDevice physicalDevice,
        const VkPhysicalDeviceSparseImageFormatInfo2* pFormatInfo,
        uint32_t* pPropertyCount,
        VkSparseImageFormatProperties2* pProperties);
    void vkTrimCommandPool(
    VkDevice device,
        VkCommandPool commandPool,
        VkCommandPoolTrimFlags flags);
    void vkGetDeviceQueue2(
    VkDevice device,
        const VkDeviceQueueInfo2* pQueueInfo,
        VkQueue* pQueue);
    VkResult vkCreateSamplerYcbcrConversion(
    VkDevice device,
        const VkSamplerYcbcrConversionCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkSamplerYcbcrConversion* pYcbcrConversion);
    void vkDestroySamplerYcbcrConversion(
    VkDevice device,
        VkSamplerYcbcrConversion ycbcrConversion,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkCreateDescriptorUpdateTemplate(
    VkDevice device,
        const VkDescriptorUpdateTemplateCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkDescriptorUpdateTemplate* pDescriptorUpdateTemplate);
    void vkDestroyDescriptorUpdateTemplate(
    VkDevice device,
        VkDescriptorUpdateTemplate descriptorUpdateTemplate,
        const VkAllocationCallbacks* pAllocator);
    void vkUpdateDescriptorSetWithTemplate(
    VkDevice device,
        VkDescriptorSet descriptorSet,
        VkDescriptorUpdateTemplate descriptorUpdateTemplate,
        const void* pData);
    void vkGetPhysicalDeviceExternalBufferProperties(
    VkPhysicalDevice physicalDevice,
        const VkPhysicalDeviceExternalBufferInfo* pExternalBufferInfo,
        VkExternalBufferProperties* pExternalBufferProperties);
    void vkGetPhysicalDeviceExternalFenceProperties(
    VkPhysicalDevice physicalDevice,
        const VkPhysicalDeviceExternalFenceInfo* pExternalFenceInfo,
        VkExternalFenceProperties* pExternalFenceProperties);
    void vkGetPhysicalDeviceExternalSemaphoreProperties(
    VkPhysicalDevice physicalDevice,
        const VkPhysicalDeviceExternalSemaphoreInfo* pExternalSemaphoreInfo,
        VkExternalSemaphoreProperties* pExternalSemaphoreProperties);
    void vkGetDescriptorSetLayoutSupport(
    VkDevice device,
        const VkDescriptorSetLayoutCreateInfo* pCreateInfo,
        VkDescriptorSetLayoutSupport* pSupport);
#endif
#ifdef VK_KHR_surface
    void vkDestroySurfaceKHR(
    VkInstance instance,
        VkSurfaceKHR surface,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkGetPhysicalDeviceSurfaceSupportKHR(
    VkPhysicalDevice physicalDevice,
        uint32_t queueFamilyIndex,
        VkSurfaceKHR surface,
        VkBool32* pSupported);
    VkResult vkGetPhysicalDeviceSurfaceCapabilitiesKHR(
    VkPhysicalDevice physicalDevice,
        VkSurfaceKHR surface,
        VkSurfaceCapabilitiesKHR* pSurfaceCapabilities);
    VkResult vkGetPhysicalDeviceSurfaceFormatsKHR(
    VkPhysicalDevice physicalDevice,
        VkSurfaceKHR surface,
        uint32_t* pSurfaceFormatCount,
        VkSurfaceFormatKHR* pSurfaceFormats);
    VkResult vkGetPhysicalDeviceSurfacePresentModesKHR(
    VkPhysicalDevice physicalDevice,
        VkSurfaceKHR surface,
        uint32_t* pPresentModeCount,
        VkPresentModeKHR* pPresentModes);
#endif
#ifdef VK_KHR_swapchain
    VkResult vkCreateSwapchainKHR(
    VkDevice device,
        const VkSwapchainCreateInfoKHR* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkSwapchainKHR* pSwapchain);
    void vkDestroySwapchainKHR(
    VkDevice device,
        VkSwapchainKHR swapchain,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkGetSwapchainImagesKHR(
    VkDevice device,
        VkSwapchainKHR swapchain,
        uint32_t* pSwapchainImageCount,
        VkImage* pSwapchainImages);
    VkResult vkAcquireNextImageKHR(
    VkDevice device,
        VkSwapchainKHR swapchain,
        uint64_t timeout,
        VkSemaphore semaphore,
        VkFence fence,
        uint32_t* pImageIndex);
    VkResult vkQueuePresentKHR(
    VkQueue queue,
        const VkPresentInfoKHR* pPresentInfo);
    VkResult vkGetDeviceGroupPresentCapabilitiesKHR(
    VkDevice device,
        VkDeviceGroupPresentCapabilitiesKHR* pDeviceGroupPresentCapabilities);
    VkResult vkGetDeviceGroupSurfacePresentModesKHR(
    VkDevice device,
        VkSurfaceKHR surface,
        VkDeviceGroupPresentModeFlagsKHR* pModes);
    VkResult vkGetPhysicalDevicePresentRectanglesKHR(
    VkPhysicalDevice physicalDevice,
        VkSurfaceKHR surface,
        uint32_t* pRectCount,
        VkRect2D* pRects);
    VkResult vkAcquireNextImage2KHR(
    VkDevice device,
        const VkAcquireNextImageInfoKHR* pAcquireInfo,
        uint32_t* pImageIndex);
#endif
#ifdef VK_KHR_display
    VkResult vkGetPhysicalDeviceDisplayPropertiesKHR(
    VkPhysicalDevice physicalDevice,
        uint32_t* pPropertyCount,
        VkDisplayPropertiesKHR* pProperties);
    VkResult vkGetPhysicalDeviceDisplayPlanePropertiesKHR(
    VkPhysicalDevice physicalDevice,
        uint32_t* pPropertyCount,
        VkDisplayPlanePropertiesKHR* pProperties);
    VkResult vkGetDisplayPlaneSupportedDisplaysKHR(
    VkPhysicalDevice physicalDevice,
        uint32_t planeIndex,
        uint32_t* pDisplayCount,
        VkDisplayKHR* pDisplays);
    VkResult vkGetDisplayModePropertiesKHR(
    VkPhysicalDevice physicalDevice,
        VkDisplayKHR display,
        uint32_t* pPropertyCount,
        VkDisplayModePropertiesKHR* pProperties);
    VkResult vkCreateDisplayModeKHR(
    VkPhysicalDevice physicalDevice,
        VkDisplayKHR display,
        const VkDisplayModeCreateInfoKHR* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkDisplayModeKHR* pMode);
    VkResult vkGetDisplayPlaneCapabilitiesKHR(
    VkPhysicalDevice physicalDevice,
        VkDisplayModeKHR mode,
        uint32_t planeIndex,
        VkDisplayPlaneCapabilitiesKHR* pCapabilities);
    VkResult vkCreateDisplayPlaneSurfaceKHR(
    VkInstance instance,
        const VkDisplaySurfaceCreateInfoKHR* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkSurfaceKHR* pSurface);
#endif
#ifdef VK_KHR_display_swapchain
    VkResult vkCreateSharedSwapchainsKHR(
    VkDevice device,
        uint32_t swapchainCount,
        const VkSwapchainCreateInfoKHR* pCreateInfos,
        const VkAllocationCallbacks* pAllocator,
        VkSwapchainKHR* pSwapchains);
#endif
#ifdef VK_KHR_xlib_surface
    VkResult vkCreateXlibSurfaceKHR(
    VkInstance instance,
        const VkXlibSurfaceCreateInfoKHR* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkSurfaceKHR* pSurface);
    VkBool32 vkGetPhysicalDeviceXlibPresentationSupportKHR(
    VkPhysicalDevice physicalDevice,
        uint32_t queueFamilyIndex,
        Display* dpy,
        VisualID visualID);
#endif
#ifdef VK_KHR_xcb_surface
    VkResult vkCreateXcbSurfaceKHR(
    VkInstance instance,
        const VkXcbSurfaceCreateInfoKHR* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkSurfaceKHR* pSurface);
    VkBool32 vkGetPhysicalDeviceXcbPresentationSupportKHR(
    VkPhysicalDevice physicalDevice,
        uint32_t queueFamilyIndex,
        xcb_connection_t* connection,
        xcb_visualid_t visual_id);
#endif
#ifdef VK_KHR_wayland_surface
    VkResult vkCreateWaylandSurfaceKHR(
    VkInstance instance,
        const VkWaylandSurfaceCreateInfoKHR* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkSurfaceKHR* pSurface);
    VkBool32 vkGetPhysicalDeviceWaylandPresentationSupportKHR(
    VkPhysicalDevice physicalDevice,
        uint32_t queueFamilyIndex,
        wl_display* display);
#endif
#ifdef VK_KHR_mir_surface
    VkResult vkCreateMirSurfaceKHR(
    VkInstance instance,
        const VkMirSurfaceCreateInfoKHR* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkSurfaceKHR* pSurface);
    VkBool32 vkGetPhysicalDeviceMirPresentationSupportKHR(
    VkPhysicalDevice physicalDevice,
        uint32_t queueFamilyIndex,
        MirConnection* connection);
#endif
#ifdef VK_KHR_android_surface
    VkResult vkCreateAndroidSurfaceKHR(
    VkInstance instance,
        const VkAndroidSurfaceCreateInfoKHR* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkSurfaceKHR* pSurface);
#endif
#ifdef VK_KHR_win32_surface
    VkResult vkCreateWin32SurfaceKHR(
    VkInstance instance,
        const VkWin32SurfaceCreateInfoKHR* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkSurfaceKHR* pSurface);
    VkBool32 vkGetPhysicalDeviceWin32PresentationSupportKHR(
    VkPhysicalDevice physicalDevice,
        uint32_t queueFamilyIndex);
#endif
#ifdef VK_KHR_sampler_mirror_clamp_to_edge
#endif
#ifdef VK_KHR_multiview
#endif
#ifdef VK_KHR_get_physical_device_properties2
    void vkGetPhysicalDeviceFeatures2KHR(
    VkPhysicalDevice physicalDevice,
        VkPhysicalDeviceFeatures2* pFeatures);
    void vkGetPhysicalDeviceProperties2KHR(
    VkPhysicalDevice physicalDevice,
        VkPhysicalDeviceProperties2* pProperties);
    void vkGetPhysicalDeviceFormatProperties2KHR(
    VkPhysicalDevice physicalDevice,
        VkFormat format,
        VkFormatProperties2* pFormatProperties);
    VkResult vkGetPhysicalDeviceImageFormatProperties2KHR(
    VkPhysicalDevice physicalDevice,
        const VkPhysicalDeviceImageFormatInfo2* pImageFormatInfo,
        VkImageFormatProperties2* pImageFormatProperties);
    void vkGetPhysicalDeviceQueueFamilyProperties2KHR(
    VkPhysicalDevice physicalDevice,
        uint32_t* pQueueFamilyPropertyCount,
        VkQueueFamilyProperties2* pQueueFamilyProperties);
    void vkGetPhysicalDeviceMemoryProperties2KHR(
    VkPhysicalDevice physicalDevice,
        VkPhysicalDeviceMemoryProperties2* pMemoryProperties);
    void vkGetPhysicalDeviceSparseImageFormatProperties2KHR(
    VkPhysicalDevice physicalDevice,
        const VkPhysicalDeviceSparseImageFormatInfo2* pFormatInfo,
        uint32_t* pPropertyCount,
        VkSparseImageFormatProperties2* pProperties);
#endif
#ifdef VK_KHR_device_group
    void vkGetDeviceGroupPeerMemoryFeaturesKHR(
    VkDevice device,
        uint32_t heapIndex,
        uint32_t localDeviceIndex,
        uint32_t remoteDeviceIndex,
        VkPeerMemoryFeatureFlags* pPeerMemoryFeatures);
    void vkCmdSetDeviceMaskKHR(
    VkCommandBuffer commandBuffer,
        uint32_t deviceMask);
    void vkCmdDispatchBaseKHR(
    VkCommandBuffer commandBuffer,
        uint32_t baseGroupX,
        uint32_t baseGroupY,
        uint32_t baseGroupZ,
        uint32_t groupCountX,
        uint32_t groupCountY,
        uint32_t groupCountZ);
#endif
#ifdef VK_KHR_shader_draw_parameters
#endif
#ifdef VK_KHR_maintenance1
    void vkTrimCommandPoolKHR(
    VkDevice device,
        VkCommandPool commandPool,
        VkCommandPoolTrimFlags flags);
#endif
#ifdef VK_KHR_device_group_creation
    VkResult vkEnumeratePhysicalDeviceGroupsKHR(
    VkInstance instance,
        uint32_t* pPhysicalDeviceGroupCount,
        VkPhysicalDeviceGroupProperties* pPhysicalDeviceGroupProperties);
#endif
#ifdef VK_KHR_external_memory_capabilities
    void vkGetPhysicalDeviceExternalBufferPropertiesKHR(
    VkPhysicalDevice physicalDevice,
        const VkPhysicalDeviceExternalBufferInfo* pExternalBufferInfo,
        VkExternalBufferProperties* pExternalBufferProperties);
#endif
#ifdef VK_KHR_external_memory
#endif
#ifdef VK_KHR_external_memory_win32
    VkResult vkGetMemoryWin32HandleKHR(
    VkDevice device,
        const VkMemoryGetWin32HandleInfoKHR* pGetWin32HandleInfo,
        HANDLE* pHandle);
    VkResult vkGetMemoryWin32HandlePropertiesKHR(
    VkDevice device,
        VkExternalMemoryHandleTypeFlagBits handleType,
        HANDLE handle,
        VkMemoryWin32HandlePropertiesKHR* pMemoryWin32HandleProperties);
#endif
#ifdef VK_KHR_external_memory_fd
    VkResult vkGetMemoryFdKHR(
    VkDevice device,
        const VkMemoryGetFdInfoKHR* pGetFdInfo,
        int* pFd);
    VkResult vkGetMemoryFdPropertiesKHR(
    VkDevice device,
        VkExternalMemoryHandleTypeFlagBits handleType,
        int fd,
        VkMemoryFdPropertiesKHR* pMemoryFdProperties);
#endif
#ifdef VK_KHR_win32_keyed_mutex
#endif
#ifdef VK_KHR_external_semaphore_capabilities
    void vkGetPhysicalDeviceExternalSemaphorePropertiesKHR(
    VkPhysicalDevice physicalDevice,
        const VkPhysicalDeviceExternalSemaphoreInfo* pExternalSemaphoreInfo,
        VkExternalSemaphoreProperties* pExternalSemaphoreProperties);
#endif
#ifdef VK_KHR_external_semaphore
#endif
#ifdef VK_KHR_external_semaphore_win32
    VkResult vkImportSemaphoreWin32HandleKHR(
    VkDevice device,
        const VkImportSemaphoreWin32HandleInfoKHR* pImportSemaphoreWin32HandleInfo);
    VkResult vkGetSemaphoreWin32HandleKHR(
    VkDevice device,
        const VkSemaphoreGetWin32HandleInfoKHR* pGetWin32HandleInfo,
        HANDLE* pHandle);
#endif
#ifdef VK_KHR_external_semaphore_fd
    VkResult vkImportSemaphoreFdKHR(
    VkDevice device,
        const VkImportSemaphoreFdInfoKHR* pImportSemaphoreFdInfo);
    VkResult vkGetSemaphoreFdKHR(
    VkDevice device,
        const VkSemaphoreGetFdInfoKHR* pGetFdInfo,
        int* pFd);
#endif
#ifdef VK_KHR_push_descriptor
    void vkCmdPushDescriptorSetKHR(
    VkCommandBuffer commandBuffer,
        VkPipelineBindPoint pipelineBindPoint,
        VkPipelineLayout layout,
        uint32_t set,
        uint32_t descriptorWriteCount,
        const VkWriteDescriptorSet* pDescriptorWrites);
    void vkCmdPushDescriptorSetWithTemplateKHR(
    VkCommandBuffer commandBuffer,
        VkDescriptorUpdateTemplate descriptorUpdateTemplate,
        VkPipelineLayout layout,
        uint32_t set,
        const void* pData);
#endif
#ifdef VK_KHR_16bit_storage
#endif
#ifdef VK_KHR_incremental_present
#endif
#ifdef VK_KHR_descriptor_update_template
    VkResult vkCreateDescriptorUpdateTemplateKHR(
    VkDevice device,
        const VkDescriptorUpdateTemplateCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkDescriptorUpdateTemplate* pDescriptorUpdateTemplate);
    void vkDestroyDescriptorUpdateTemplateKHR(
    VkDevice device,
        VkDescriptorUpdateTemplate descriptorUpdateTemplate,
        const VkAllocationCallbacks* pAllocator);
    void vkUpdateDescriptorSetWithTemplateKHR(
    VkDevice device,
        VkDescriptorSet descriptorSet,
        VkDescriptorUpdateTemplate descriptorUpdateTemplate,
        const void* pData);
#endif
#ifdef VK_KHR_create_renderpass2
    VkResult vkCreateRenderPass2KHR(
    VkDevice device,
        const VkRenderPassCreateInfo2KHR* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkRenderPass* pRenderPass);
    void vkCmdBeginRenderPass2KHR(
    VkCommandBuffer commandBuffer,
        const VkRenderPassBeginInfo* pRenderPassBegin,
        const VkSubpassBeginInfoKHR* pSubpassBeginInfo);
    void vkCmdNextSubpass2KHR(
    VkCommandBuffer commandBuffer,
        const VkSubpassBeginInfoKHR* pSubpassBeginInfo,
        const VkSubpassEndInfoKHR* pSubpassEndInfo);
    void vkCmdEndRenderPass2KHR(
    VkCommandBuffer commandBuffer,
        const VkSubpassEndInfoKHR* pSubpassEndInfo);
#endif
#ifdef VK_KHR_shared_presentable_image
    VkResult vkGetSwapchainStatusKHR(
    VkDevice device,
        VkSwapchainKHR swapchain);
#endif
#ifdef VK_KHR_external_fence_capabilities
    void vkGetPhysicalDeviceExternalFencePropertiesKHR(
    VkPhysicalDevice physicalDevice,
        const VkPhysicalDeviceExternalFenceInfo* pExternalFenceInfo,
        VkExternalFenceProperties* pExternalFenceProperties);
#endif
#ifdef VK_KHR_external_fence
#endif
#ifdef VK_KHR_external_fence_win32
    VkResult vkImportFenceWin32HandleKHR(
    VkDevice device,
        const VkImportFenceWin32HandleInfoKHR* pImportFenceWin32HandleInfo);
    VkResult vkGetFenceWin32HandleKHR(
    VkDevice device,
        const VkFenceGetWin32HandleInfoKHR* pGetWin32HandleInfo,
        HANDLE* pHandle);
#endif
#ifdef VK_KHR_external_fence_fd
    VkResult vkImportFenceFdKHR(
    VkDevice device,
        const VkImportFenceFdInfoKHR* pImportFenceFdInfo);
    VkResult vkGetFenceFdKHR(
    VkDevice device,
        const VkFenceGetFdInfoKHR* pGetFdInfo,
        int* pFd);
#endif
#ifdef VK_KHR_maintenance2
#endif
#ifdef VK_KHR_get_surface_capabilities2
    VkResult vkGetPhysicalDeviceSurfaceCapabilities2KHR(
    VkPhysicalDevice physicalDevice,
        const VkPhysicalDeviceSurfaceInfo2KHR* pSurfaceInfo,
        VkSurfaceCapabilities2KHR* pSurfaceCapabilities);
    VkResult vkGetPhysicalDeviceSurfaceFormats2KHR(
    VkPhysicalDevice physicalDevice,
        const VkPhysicalDeviceSurfaceInfo2KHR* pSurfaceInfo,
        uint32_t* pSurfaceFormatCount,
        VkSurfaceFormat2KHR* pSurfaceFormats);
#endif
#ifdef VK_KHR_variable_pointers
#endif
#ifdef VK_KHR_get_display_properties2
    VkResult vkGetPhysicalDeviceDisplayProperties2KHR(
    VkPhysicalDevice physicalDevice,
        uint32_t* pPropertyCount,
        VkDisplayProperties2KHR* pProperties);
    VkResult vkGetPhysicalDeviceDisplayPlaneProperties2KHR(
    VkPhysicalDevice physicalDevice,
        uint32_t* pPropertyCount,
        VkDisplayPlaneProperties2KHR* pProperties);
    VkResult vkGetDisplayModeProperties2KHR(
    VkPhysicalDevice physicalDevice,
        VkDisplayKHR display,
        uint32_t* pPropertyCount,
        VkDisplayModeProperties2KHR* pProperties);
    VkResult vkGetDisplayPlaneCapabilities2KHR(
    VkPhysicalDevice physicalDevice,
        const VkDisplayPlaneInfo2KHR* pDisplayPlaneInfo,
        VkDisplayPlaneCapabilities2KHR* pCapabilities);
#endif
#ifdef VK_KHR_dedicated_allocation
#endif
#ifdef VK_KHR_storage_buffer_storage_class
#endif
#ifdef VK_KHR_relaxed_block_layout
#endif
#ifdef VK_KHR_get_memory_requirements2
    void vkGetImageMemoryRequirements2KHR(
    VkDevice device,
        const VkImageMemoryRequirementsInfo2* pInfo,
        VkMemoryRequirements2* pMemoryRequirements);
    void vkGetBufferMemoryRequirements2KHR(
    VkDevice device,
        const VkBufferMemoryRequirementsInfo2* pInfo,
        VkMemoryRequirements2* pMemoryRequirements);
    void vkGetImageSparseMemoryRequirements2KHR(
    VkDevice device,
        const VkImageSparseMemoryRequirementsInfo2* pInfo,
        uint32_t* pSparseMemoryRequirementCount,
        VkSparseImageMemoryRequirements2* pSparseMemoryRequirements);
#endif
#ifdef VK_KHR_image_format_list
#endif
#ifdef VK_KHR_sampler_ycbcr_conversion
    VkResult vkCreateSamplerYcbcrConversionKHR(
    VkDevice device,
        const VkSamplerYcbcrConversionCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkSamplerYcbcrConversion* pYcbcrConversion);
    void vkDestroySamplerYcbcrConversionKHR(
    VkDevice device,
        VkSamplerYcbcrConversion ycbcrConversion,
        const VkAllocationCallbacks* pAllocator);
#endif
#ifdef VK_KHR_bind_memory2
    VkResult vkBindBufferMemory2KHR(
    VkDevice device,
        uint32_t bindInfoCount,
        const VkBindBufferMemoryInfo* pBindInfos);
    VkResult vkBindImageMemory2KHR(
    VkDevice device,
        uint32_t bindInfoCount,
        const VkBindImageMemoryInfo* pBindInfos);
#endif
#ifdef VK_KHR_maintenance3
    void vkGetDescriptorSetLayoutSupportKHR(
    VkDevice device,
        const VkDescriptorSetLayoutCreateInfo* pCreateInfo,
        VkDescriptorSetLayoutSupport* pSupport);
#endif
#ifdef VK_KHR_draw_indirect_count
    void vkCmdDrawIndirectCountKHR(
    VkCommandBuffer commandBuffer,
        VkBuffer buffer,
        VkDeviceSize offset,
        VkBuffer countBuffer,
        VkDeviceSize countBufferOffset,
        uint32_t maxDrawCount,
        uint32_t stride);
    void vkCmdDrawIndexedIndirectCountKHR(
    VkCommandBuffer commandBuffer,
        VkBuffer buffer,
        VkDeviceSize offset,
        VkBuffer countBuffer,
        VkDeviceSize countBufferOffset,
        uint32_t maxDrawCount,
        uint32_t stride);
#endif
#ifdef VK_KHR_8bit_storage
#endif
#ifdef VK_ANDROID_native_buffer
    VkResult vkGetSwapchainGrallocUsageANDROID(
    VkDevice device,
        VkFormat format,
        VkImageUsageFlags imageUsage,
        int* grallocUsage);
    VkResult vkAcquireImageANDROID(
    VkDevice device,
        VkImage image,
        int nativeFenceFd,
        VkSemaphore semaphore,
        VkFence fence);
    VkResult vkQueueSignalReleaseImageANDROID(
    VkQueue queue,
        uint32_t waitSemaphoreCount,
        const VkSemaphore* pWaitSemaphores,
        VkImage image,
        int* pNativeFenceFd);
#endif
#ifdef VK_EXT_debug_report
    VkResult vkCreateDebugReportCallbackEXT(
    VkInstance instance,
        const VkDebugReportCallbackCreateInfoEXT* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkDebugReportCallbackEXT* pCallback);
    void vkDestroyDebugReportCallbackEXT(
    VkInstance instance,
        VkDebugReportCallbackEXT callback,
        const VkAllocationCallbacks* pAllocator);
    void vkDebugReportMessageEXT(
    VkInstance instance,
        VkDebugReportFlagsEXT flags,
        VkDebugReportObjectTypeEXT objectType,
        uint64_t object,
        size_t location,
        int32_t messageCode,
        const char* pLayerPrefix,
        const char* pMessage);
#endif
#ifdef VK_NV_glsl_shader
#endif
#ifdef VK_EXT_depth_range_unrestricted
#endif
#ifdef VK_IMG_filter_cubic
#endif
#ifdef VK_AMD_rasterization_order
#endif
#ifdef VK_AMD_shader_trinary_minmax
#endif
#ifdef VK_AMD_shader_explicit_vertex_parameter
#endif
#ifdef VK_EXT_debug_marker
    VkResult vkDebugMarkerSetObjectTagEXT(
    VkDevice device,
        const VkDebugMarkerObjectTagInfoEXT* pTagInfo);
    VkResult vkDebugMarkerSetObjectNameEXT(
    VkDevice device,
        const VkDebugMarkerObjectNameInfoEXT* pNameInfo);
    void vkCmdDebugMarkerBeginEXT(
    VkCommandBuffer commandBuffer,
        const VkDebugMarkerMarkerInfoEXT* pMarkerInfo);
    void vkCmdDebugMarkerEndEXT(
    VkCommandBuffer commandBuffer);
    void vkCmdDebugMarkerInsertEXT(
    VkCommandBuffer commandBuffer,
        const VkDebugMarkerMarkerInfoEXT* pMarkerInfo);
#endif
#ifdef VK_AMD_gcn_shader
#endif
#ifdef VK_NV_dedicated_allocation
#endif
#ifdef VK_AMD_draw_indirect_count
    void vkCmdDrawIndirectCountAMD(
    VkCommandBuffer commandBuffer,
        VkBuffer buffer,
        VkDeviceSize offset,
        VkBuffer countBuffer,
        VkDeviceSize countBufferOffset,
        uint32_t maxDrawCount,
        uint32_t stride);
    void vkCmdDrawIndexedIndirectCountAMD(
    VkCommandBuffer commandBuffer,
        VkBuffer buffer,
        VkDeviceSize offset,
        VkBuffer countBuffer,
        VkDeviceSize countBufferOffset,
        uint32_t maxDrawCount,
        uint32_t stride);
#endif
#ifdef VK_AMD_negative_viewport_height
#endif
#ifdef VK_AMD_gpu_shader_half_float
#endif
#ifdef VK_AMD_shader_ballot
#endif
#ifdef VK_AMD_texture_gather_bias_lod
#endif
#ifdef VK_AMD_shader_info
    VkResult vkGetShaderInfoAMD(
    VkDevice device,
        VkPipeline pipeline,
        VkShaderStageFlagBits shaderStage,
        VkShaderInfoTypeAMD infoType,
        size_t* pInfoSize,
        void* pInfo);
#endif
#ifdef VK_AMD_shader_image_load_store_lod
#endif
#ifdef VK_IMG_format_pvrtc
#endif
#ifdef VK_NV_external_memory_capabilities
    VkResult vkGetPhysicalDeviceExternalImageFormatPropertiesNV(
    VkPhysicalDevice physicalDevice,
        VkFormat format,
        VkImageType type,
        VkImageTiling tiling,
        VkImageUsageFlags usage,
        VkImageCreateFlags flags,
        VkExternalMemoryHandleTypeFlagsNV externalHandleType,
        VkExternalImageFormatPropertiesNV* pExternalImageFormatProperties);
#endif
#ifdef VK_NV_external_memory
#endif
#ifdef VK_NV_external_memory_win32
    VkResult vkGetMemoryWin32HandleNV(
    VkDevice device,
        VkDeviceMemory memory,
        VkExternalMemoryHandleTypeFlagsNV handleType,
        HANDLE* pHandle);
#endif
#ifdef VK_NV_win32_keyed_mutex
#endif
#ifdef VK_EXT_validation_flags
#endif
#ifdef VK_NN_vi_surface
    VkResult vkCreateViSurfaceNN(
    VkInstance instance,
        const VkViSurfaceCreateInfoNN* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkSurfaceKHR* pSurface);
#endif
#ifdef VK_EXT_shader_subgroup_ballot
#endif
#ifdef VK_EXT_shader_subgroup_vote
#endif
#ifdef VK_EXT_conditional_rendering
    void vkCmdBeginConditionalRenderingEXT(
    VkCommandBuffer commandBuffer,
        const VkConditionalRenderingBeginInfoEXT* pConditionalRenderingBegin);
    void vkCmdEndConditionalRenderingEXT(
    VkCommandBuffer commandBuffer);
#endif
#ifdef VK_NVX_device_generated_commands
    void vkCmdProcessCommandsNVX(
    VkCommandBuffer commandBuffer,
        const VkCmdProcessCommandsInfoNVX* pProcessCommandsInfo);
    void vkCmdReserveSpaceForCommandsNVX(
    VkCommandBuffer commandBuffer,
        const VkCmdReserveSpaceForCommandsInfoNVX* pReserveSpaceInfo);
    VkResult vkCreateIndirectCommandsLayoutNVX(
    VkDevice device,
        const VkIndirectCommandsLayoutCreateInfoNVX* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkIndirectCommandsLayoutNVX* pIndirectCommandsLayout);
    void vkDestroyIndirectCommandsLayoutNVX(
    VkDevice device,
        VkIndirectCommandsLayoutNVX indirectCommandsLayout,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkCreateObjectTableNVX(
    VkDevice device,
        const VkObjectTableCreateInfoNVX* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkObjectTableNVX* pObjectTable);
    void vkDestroyObjectTableNVX(
    VkDevice device,
        VkObjectTableNVX objectTable,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkRegisterObjectsNVX(
    VkDevice device,
        VkObjectTableNVX objectTable,
        uint32_t objectCount,
        const VkObjectTableEntryNVX* const* ppObjectTableEntries,
        const uint32_t* pObjectIndices);
    VkResult vkUnregisterObjectsNVX(
    VkDevice device,
        VkObjectTableNVX objectTable,
        uint32_t objectCount,
        const VkObjectEntryTypeNVX* pObjectEntryTypes,
        const uint32_t* pObjectIndices);
    void vkGetPhysicalDeviceGeneratedCommandsPropertiesNVX(
    VkPhysicalDevice physicalDevice,
        VkDeviceGeneratedCommandsFeaturesNVX* pFeatures,
        VkDeviceGeneratedCommandsLimitsNVX* pLimits);
#endif
#ifdef VK_NV_clip_space_w_scaling
    void vkCmdSetViewportWScalingNV(
    VkCommandBuffer commandBuffer,
        uint32_t firstViewport,
        uint32_t viewportCount,
        const VkViewportWScalingNV* pViewportWScalings);
#endif
#ifdef VK_EXT_direct_mode_display
    VkResult vkReleaseDisplayEXT(
    VkPhysicalDevice physicalDevice,
        VkDisplayKHR display);
#endif
#ifdef VK_EXT_acquire_xlib_display
    VkResult vkAcquireXlibDisplayEXT(
    VkPhysicalDevice physicalDevice,
        Display* dpy,
        VkDisplayKHR display);
    VkResult vkGetRandROutputDisplayEXT(
    VkPhysicalDevice physicalDevice,
        Display* dpy,
        RROutput rrOutput,
        VkDisplayKHR* pDisplay);
#endif
#ifdef VK_EXT_display_surface_counter
    VkResult vkGetPhysicalDeviceSurfaceCapabilities2EXT(
    VkPhysicalDevice physicalDevice,
        VkSurfaceKHR surface,
        VkSurfaceCapabilities2EXT* pSurfaceCapabilities);
#endif
#ifdef VK_EXT_display_control
    VkResult vkDisplayPowerControlEXT(
    VkDevice device,
        VkDisplayKHR display,
        const VkDisplayPowerInfoEXT* pDisplayPowerInfo);
    VkResult vkRegisterDeviceEventEXT(
    VkDevice device,
        const VkDeviceEventInfoEXT* pDeviceEventInfo,
        const VkAllocationCallbacks* pAllocator,
        VkFence* pFence);
    VkResult vkRegisterDisplayEventEXT(
    VkDevice device,
        VkDisplayKHR display,
        const VkDisplayEventInfoEXT* pDisplayEventInfo,
        const VkAllocationCallbacks* pAllocator,
        VkFence* pFence);
    VkResult vkGetSwapchainCounterEXT(
    VkDevice device,
        VkSwapchainKHR swapchain,
        VkSurfaceCounterFlagBitsEXT counter,
        uint64_t* pCounterValue);
#endif
#ifdef VK_GOOGLE_display_timing
    VkResult vkGetRefreshCycleDurationGOOGLE(
    VkDevice device,
        VkSwapchainKHR swapchain,
        VkRefreshCycleDurationGOOGLE* pDisplayTimingProperties);
    VkResult vkGetPastPresentationTimingGOOGLE(
    VkDevice device,
        VkSwapchainKHR swapchain,
        uint32_t* pPresentationTimingCount,
        VkPastPresentationTimingGOOGLE* pPresentationTimings);
#endif
#ifdef VK_NV_sample_mask_override_coverage
#endif
#ifdef VK_NV_geometry_shader_passthrough
#endif
#ifdef VK_NV_viewport_array2
#endif
#ifdef VK_NVX_multiview_per_view_attributes
#endif
#ifdef VK_NV_viewport_swizzle
#endif
#ifdef VK_EXT_discard_rectangles
    void vkCmdSetDiscardRectangleEXT(
    VkCommandBuffer commandBuffer,
        uint32_t firstDiscardRectangle,
        uint32_t discardRectangleCount,
        const VkRect2D* pDiscardRectangles);
#endif
#ifdef VK_EXT_conservative_rasterization
#endif
#ifdef VK_EXT_swapchain_colorspace
#endif
#ifdef VK_EXT_hdr_metadata
    void vkSetHdrMetadataEXT(
    VkDevice device,
        uint32_t swapchainCount,
        const VkSwapchainKHR* pSwapchains,
        const VkHdrMetadataEXT* pMetadata);
#endif
#ifdef VK_MVK_ios_surface
    VkResult vkCreateIOSSurfaceMVK(
    VkInstance instance,
        const VkIOSSurfaceCreateInfoMVK* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkSurfaceKHR* pSurface);
#endif
#ifdef VK_MVK_macos_surface
    VkResult vkCreateMacOSSurfaceMVK(
    VkInstance instance,
        const VkMacOSSurfaceCreateInfoMVK* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkSurfaceKHR* pSurface);
#endif
#ifdef VK_EXT_external_memory_dma_buf
#endif
#ifdef VK_EXT_queue_family_foreign
#endif
#ifdef VK_EXT_debug_utils
    VkResult vkSetDebugUtilsObjectNameEXT(
    VkDevice device,
        const VkDebugUtilsObjectNameInfoEXT* pNameInfo);
    VkResult vkSetDebugUtilsObjectTagEXT(
    VkDevice device,
        const VkDebugUtilsObjectTagInfoEXT* pTagInfo);
    void vkQueueBeginDebugUtilsLabelEXT(
    VkQueue queue,
        const VkDebugUtilsLabelEXT* pLabelInfo);
    void vkQueueEndDebugUtilsLabelEXT(
    VkQueue queue);
    void vkQueueInsertDebugUtilsLabelEXT(
    VkQueue queue,
        const VkDebugUtilsLabelEXT* pLabelInfo);
    void vkCmdBeginDebugUtilsLabelEXT(
    VkCommandBuffer commandBuffer,
        const VkDebugUtilsLabelEXT* pLabelInfo);
    void vkCmdEndDebugUtilsLabelEXT(
    VkCommandBuffer commandBuffer);
    void vkCmdInsertDebugUtilsLabelEXT(
    VkCommandBuffer commandBuffer,
        const VkDebugUtilsLabelEXT* pLabelInfo);
    VkResult vkCreateDebugUtilsMessengerEXT(
    VkInstance instance,
        const VkDebugUtilsMessengerCreateInfoEXT* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkDebugUtilsMessengerEXT* pMessenger);
    void vkDestroyDebugUtilsMessengerEXT(
    VkInstance instance,
        VkDebugUtilsMessengerEXT messenger,
        const VkAllocationCallbacks* pAllocator);
    void vkSubmitDebugUtilsMessageEXT(
    VkInstance instance,
        VkDebugUtilsMessageSeverityFlagBitsEXT messageSeverity,
        VkDebugUtilsMessageTypeFlagsEXT messageTypes,
        const VkDebugUtilsMessengerCallbackDataEXT* pCallbackData);
#endif
#ifdef VK_ANDROID_external_memory_android_hardware_buffer
    VkResult vkGetAndroidHardwareBufferPropertiesANDROID(
    VkDevice device,
        const AHardwareBuffer* buffer,
        VkAndroidHardwareBufferPropertiesANDROID* pProperties);
    VkResult vkGetMemoryAndroidHardwareBufferANDROID(
    VkDevice device,
        const VkMemoryGetAndroidHardwareBufferInfoANDROID* pInfo,
        AHardwareBuffer** pBuffer);
#endif
#ifdef VK_EXT_sampler_filter_minmax
#endif
#ifdef VK_AMD_gpu_shader_int16
#endif
#ifdef VK_AMD_mixed_attachment_samples
#endif
#ifdef VK_AMD_shader_fragment_mask
#endif
#ifdef VK_EXT_shader_stencil_export
#endif
#ifdef VK_EXT_sample_locations
    void vkCmdSetSampleLocationsEXT(
    VkCommandBuffer commandBuffer,
        const VkSampleLocationsInfoEXT* pSampleLocationsInfo);
    void vkGetPhysicalDeviceMultisamplePropertiesEXT(
    VkPhysicalDevice physicalDevice,
        VkSampleCountFlagBits samples,
        VkMultisamplePropertiesEXT* pMultisampleProperties);
#endif
#ifdef VK_EXT_blend_operation_advanced
#endif
#ifdef VK_NV_fragment_coverage_to_color
#endif
#ifdef VK_NV_framebuffer_mixed_samples
#endif
#ifdef VK_NV_fill_rectangle
#endif
#ifdef VK_EXT_post_depth_coverage
#endif
#ifdef VK_EXT_validation_cache
    VkResult vkCreateValidationCacheEXT(
    VkDevice device,
        const VkValidationCacheCreateInfoEXT* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkValidationCacheEXT* pValidationCache);
    void vkDestroyValidationCacheEXT(
    VkDevice device,
        VkValidationCacheEXT validationCache,
        const VkAllocationCallbacks* pAllocator);
    VkResult vkMergeValidationCachesEXT(
    VkDevice device,
        VkValidationCacheEXT dstCache,
        uint32_t srcCacheCount,
        const VkValidationCacheEXT* pSrcCaches);
    VkResult vkGetValidationCacheDataEXT(
    VkDevice device,
        VkValidationCacheEXT validationCache,
        size_t* pDataSize,
        void* pData);
#endif
#ifdef VK_EXT_descriptor_indexing
#endif
#ifdef VK_EXT_shader_viewport_index_layer
#endif
#ifdef VK_EXT_global_priority
#endif
#ifdef VK_EXT_external_memory_host
    VkResult vkGetMemoryHostPointerPropertiesEXT(
    VkDevice device,
        VkExternalMemoryHandleTypeFlagBits handleType,
        const void* pHostPointer,
        VkMemoryHostPointerPropertiesEXT* pMemoryHostPointerProperties);
#endif
#ifdef VK_AMD_buffer_marker
    void vkCmdWriteBufferMarkerAMD(
    VkCommandBuffer commandBuffer,
        VkPipelineStageFlagBits pipelineStage,
        VkBuffer dstBuffer,
        VkDeviceSize dstOffset,
        uint32_t marker);
#endif
#ifdef VK_AMD_shader_core_properties
#endif
#ifdef VK_EXT_vertex_attribute_divisor
#endif
#ifdef VK_NV_shader_subgroup_partitioned
#endif
#ifdef VK_NV_device_diagnostic_checkpoints
    void vkCmdSetCheckpointNV(
    VkCommandBuffer commandBuffer,
        const void* pCheckpointMarker);
    void vkGetQueueCheckpointDataNV(
    VkQueue queue,
        uint32_t* pCheckpointDataCount,
        VkCheckpointDataNV* pCheckpointData);
#endif
#ifdef VK_GOOGLE_address_space
    VkResult vkMapMemoryIntoAddressSpaceGOOGLE(
    VkDevice device,
        VkDeviceMemory memory,
        uint64_t* pAddress);
#endif
#ifdef VK_GOOGLE_color_buffer
    VkResult vkRegisterImageColorBufferGOOGLE(
    VkDevice device,
        VkImage image,
        uint32_t colorBuffer);
    VkResult vkRegisterBufferColorBufferGOOGLE(
    VkDevice device,
        VkBuffer buffer,
        uint32_t colorBuffer);
#endif
#ifdef VK_GOOGLE_sized_descriptor_update_template
    void vkUpdateDescriptorSetWithTemplateSizedGOOGLE(
    VkDevice device,
        VkDescriptorSet descriptorSet,
        VkDescriptorUpdateTemplate descriptorUpdateTemplate,
        uint32_t imageInfoCount,
        uint32_t bufferInfoCount,
        uint32_t bufferViewCount,
        const uint32_t* pImageInfoEntryIndices,
        const uint32_t* pBufferInfoEntryIndices,
        const uint32_t* pBufferViewEntryIndices,
        const VkDescriptorImageInfo* pImageInfos,
        const VkDescriptorBufferInfo* pBufferInfos,
        const VkBufferView* pBufferViews);
#endif
#ifdef VK_GOOGLE_async_command_buffers
    void vkBeginCommandBufferAsyncGOOGLE(
    VkCommandBuffer commandBuffer,
        const VkCommandBufferBeginInfo* pBeginInfo);
    void vkEndCommandBufferAsyncGOOGLE(
    VkCommandBuffer commandBuffer);
    void vkResetCommandBufferAsyncGOOGLE(
    VkCommandBuffer commandBuffer,
        VkCommandBufferResetFlags flags);
    void vkCommandBufferHostSyncGOOGLE(
    VkCommandBuffer commandBuffer,
        uint32_t needHostSync,
        uint32_t sequenceNumber);
#endif
#ifdef VK_GOOGLE_create_resources_with_requirements
    VkResult vkCreateImageWithRequirementsGOOGLE(
    VkDevice device,
        const VkImageCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkImage* pImage,
        VkMemoryRequirements* pMemoryRequirements);
    VkResult vkCreateBufferWithRequirementsGOOGLE(
    VkDevice device,
        const VkBufferCreateInfo* pCreateInfo,
        const VkAllocationCallbacks* pAllocator,
        VkBuffer* pBuffer,
        VkMemoryRequirements* pMemoryRequirements);
#endif
#ifdef VK_GOOGLE_address_space_info
    VkResult vkGetMemoryHostAddressInfoGOOGLE(
    VkDevice device,
        VkDeviceMemory memory,
        uint64_t* pAddress,
        uint64_t* pSize,
        uint64_t* pHostmemId);
#endif
#ifdef VK_GOOGLE_free_memory_sync
    VkResult vkFreeMemorySyncGOOGLE(
    VkDevice device,
        VkDeviceMemory memory,
        const VkAllocationCallbacks* pAllocator);
#endif

private:
    class Impl;
    std::unique_ptr<Impl> mImpl;
};

} // namespace goldfish_vk

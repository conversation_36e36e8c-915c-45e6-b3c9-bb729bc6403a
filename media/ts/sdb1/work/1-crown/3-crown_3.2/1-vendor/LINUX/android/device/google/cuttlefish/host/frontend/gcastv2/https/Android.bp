//
// Copyright (C) 2019 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

cc_library_static {
    name: "libhttps",
    host_supported: true,
    srcs: [
        "BaseConnection.cpp",
        "BufferedSocket.cpp",
        "ClientSocket.cpp",
        "HTTPClientConnection.cpp",
        "HTTPRequestResponse.cpp",
        "HTTPServer.cpp",
        "PlainSocket.cpp",
        "SSLSocket.cpp",
        "ServerSocket.cpp",
        "WebSocketHandler.cpp",
        "RunLoop.cpp",
        "Support.cpp",
    ],
    defaults: ["cuttlefish_host_only"],
    shared_libs: [
        "libbase",
        "libcrypto",
        "libssl",
	"liblog",
    ],
    local_include_dirs: ["include"],
    export_include_dirs: ["include"],
}


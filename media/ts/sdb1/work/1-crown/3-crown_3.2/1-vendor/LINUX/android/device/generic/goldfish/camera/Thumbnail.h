/*
* Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef GOLDFISH_CAMERA_THUMBNAIL_H
#define GOLDFISH_CAMERA_THUMBNAIL_H

struct _ExifData;
typedef struct _ExifData ExifData;

namespace android {

/* Create a thumbnail from NV21 source data in |sourceImage| with the given
 * dimensions. The resulting thumbnail is JPEG compressed and a pointer and size
 * is placed in |exifData| which takes ownership of the allocated memory.
 */
bool createThumbnail(const unsigned char* sourceImage,
                     int sourceWidth, int sourceHeight,
                     int thumbnailWidth, int thumbnailHeight, int quality,
                     ExifData* exifData);

}  // namespace android

#endif  // GOLDFISH_CAMERA_THUMBNAIL_H


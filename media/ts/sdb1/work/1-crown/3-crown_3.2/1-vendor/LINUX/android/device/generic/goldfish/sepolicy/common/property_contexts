qemu.                   u:object_r:qemu_prop:s0
qemu.cmdline            u:object_r:qemu_cmdline:s0
vendor.qemu		u:object_r:qemu_prop:s0
vendor.network          u:object_r:vendor_net:s0
ro.aae.simulateMultiZoneAudio u:object_r:hal_audio_default_prop:s0
ro.emu.                 u:object_r:qemu_prop:s0
ro.emulator.            u:object_r:qemu_prop:s0
ro.radio.noril          u:object_r:radio_noril_prop:s0
net.wlan0.               u:object_r:net_wlan0_prop:s0
net.eth0.               u:object_r:net_eth0_prop:s0
net.radio0.             u:object_r:net_radio0_prop:s0
net.shared_net_ip       u:object_r:net_share_prop:s0
net.wifi_mac_prefix     u:object_r:net_share_prop:s0
ro.zygote.disable_gl_preload            u:object_r:qemu_prop:s0
persist.dumpstate.verbose_logging.enabled u:object_r:hal_dumpstate_default_prop:s0
bt.rootcanal_mac_address  u:object_r:hal_bluetooth_sim_prop:s0
bt.rootcanal_test_console  u:object_r:hal_bluetooth_sim_prop:s0

<?xml version="1.0" encoding="UTF-8"?>
<!--
 Copyright (C) 2020 The Android Open Source Project

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
 -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
        package="com.android.emulator.radio.config">

    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
    <uses-permission android:name="android.permission.MANAGE_SUBSCRIPTION_PLANS"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <application>
        <service
            android:name=".MeterService"
            android:enabled="true"
            android:exported="true">
            </service>
    </application>
</manifest>


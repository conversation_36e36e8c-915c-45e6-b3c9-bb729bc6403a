// Copyright (C) 2019 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.


cc_library_static {
    name: "hwcomposer_common",
    defaults: ["cuttlefish_guest_only"],
    vendor: true,
    srcs: [
        "base_composer.cpp",
        "cpu_composer.cpp",
        "drm_utils.cpp",
        "geometry_utils.cpp",
        "gralloc_utils.cpp",
        "hwcomposer.cpp",
        "screen_view.cpp",
        "stats_keeper.cpp",
    ],
    static_libs: [
        "libyuv_static",
    ],
    header_libs: ["libhardware_headers"],
    shared_libs: [
        "android.hardware.graphics.mapper@4.0",
        "libbase",
        "libcutils",
        "libcuttlefish_utils",
        "libcuttlefish_fs",
        "libdrm",
        "libgralloctypes",
        "libhidlbase",
        "libjpeg",
        "liblog",
        "libsync",
        "libutils",
    ],
}

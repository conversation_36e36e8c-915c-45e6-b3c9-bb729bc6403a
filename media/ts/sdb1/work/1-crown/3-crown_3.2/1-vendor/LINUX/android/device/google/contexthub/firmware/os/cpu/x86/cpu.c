/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <cpu.h>


void cpuInit(void)
{
    /* nothing to do for x86 */
}

uint64_t cpuIntsOff(void)
{
    /* no such luck */

    return 0;
}

uint64_t cpuIntsOn(void)
{
    /* no such luck */

    return 0;
}

void cpuIntsRestore(uint64_t state)
{
    /* no such luck */

}


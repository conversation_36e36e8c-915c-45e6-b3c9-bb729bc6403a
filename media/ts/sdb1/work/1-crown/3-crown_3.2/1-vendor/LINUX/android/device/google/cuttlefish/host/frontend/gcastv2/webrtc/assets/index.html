<html>
    <head>
        <title>My Virtual Device Playground</title>

        <link rel="stylesheet" type="text/css" href="style.css" >
    </head>

    <body>
        <button id="receiveButton">Receive Media</button>
        <button id="keyboardCaptureBtn">Capture Keyboard</button>
        <button id="resizeButton">Resize Viewport</button>
        <hr>
        <section class="noscroll">
            <div class="one" >
                <video id="deviceScreen" autoplay width="540" height="1080" style="touch-action:none" ></video>
            </div>

            <div class="two" >
                <textarea id="logcat" rows="55" cols="120" readonly >
                </textarea>
            </div>
        </section>

        <script src="js/receive.js"></script>
        <script src="js/logcat.js"></script>
        <script src="js/viewpane.js"></script>
    </body>

</html>


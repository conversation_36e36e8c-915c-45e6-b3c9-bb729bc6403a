<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2017 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">

    <!-- Message shown in dialog when user is in the process of enabling the accessibility
    service on Android TV devices via the remote control buttons shortcut for the first time.
    [CHAR LIMIT=none] -->
    <string name="accessibility_shortcut_toogle_warning">
        When the shortcut is on, pressing both the back and down buttons for 3 seconds will start
        an accessibility feature.\n\n
        Current accessibility feature:\n
        <xliff:g id="service_name" example="TalkBack">%1$s</xliff:g>\n\n
        You can change the feature in Settings > Accessibility.
    </string>

</resources>

// Copyright (C) 2017 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// HAL module implemenation stored in
// hw/<POWERS_HARDWARE_MODULE_ID>.<ro.hardware>.so
cc_library_shared {
    name: "memtrack.default",

    relative_install_path: "hw",
    proprietary: true,
    cflags: [
        "-Wconversion",
        "-Wall",
        "-Werror",
        "-Wno-sign-conversion",
    ],
    shared_libs: [
        "liblog",
        "libhardware",
    ],
    srcs: ["memtrack_yukawa.c"],
}

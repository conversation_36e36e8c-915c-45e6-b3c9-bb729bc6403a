# Android fstab file.
#<src>                                                  <mnt_point>         <type>    <mnt_flags and options>                              <fs_mgr_flags>
# The filesystem that contains the filesystem checker binary (typically /system) cannot
# specify MF_CHECK, and must come before any filesystems that do specify MF_CHECK
/dev/block/ubda                                         /system             ext4      ro                                                   wait
/dev/block/ubdb                                         /data               ext4      noatime,nosuid,nodev,nomblk_io_submit,errors=panic   wait,check
# When UML sees type "hostfs", it will mount the hostfs to the mount point instead of checking the <src>
none                                                    /host               hostfs    defaults                                             none

# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep enough data for stack traces
-renamesourcefileattribute SourceFile
-keepattributes SourceFile,LineNumberTable,RuntimeVisible*Annotations,AnnotationDefault

# Keep classes and methods that have the guava @VisibleForTesting annotation
-keep @com.google.common.annotations.VisibleForTesting class *
-keepclassmembers class * {
  @com.google.common.annotations.VisibleForTesting *;
}

-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver

# -dontobfuscate
-keep,allowshrinking class * { *; }

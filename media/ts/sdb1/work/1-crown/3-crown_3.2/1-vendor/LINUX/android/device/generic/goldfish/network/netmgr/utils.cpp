/*
 * Copyright 2018, The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "utils.h"

#include <string.h>

std::vector<std::string> explode(const char* str, char divider) {
    const char* cur = str;
    const char* space = nullptr;
    std::vector<std::string> result;
    do {
        space = ::strchr(cur, divider);
        if (space) {
            result.emplace_back(cur, space);
            cur = space + 1;
        } else {
            result.emplace_back(cur);
        }
    } while (space);

    return result;
}


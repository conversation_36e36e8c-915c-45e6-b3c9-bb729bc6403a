# DHCP client
type dhcpclient, domain;
type dhcpclient_exec, exec_type, vendor_file_type, file_type;

init_daemon_domain(dhcpclient)
net_domain(dhcpclient)

allow dhcpclient execns:fd use;

set_prop(dhcpclient, net_wlan0_prop);
set_prop(dhcpclient, net_eth0_prop);
set_prop(dhcpclient, net_radio0_prop);
dontaudit dhcpclient kernel:system module_request;
allow dhcpclient self:capability { net_admin net_raw };
allow dhcpclient self:netlink_route_socket { ioctl write nlmsg_write };
allow dhcpclient varrun_file:dir search;
allow dhcpclient self:packet_socket { create bind write read };
allowxperm dhcpclient self:netlink_route_socket ioctl { SIOCGIFFLAGS
                                                        SIOCSIFFLAGS
                                                        SIOCSIFMTU
                                                        SIOCGIFINDEX
                                                        SIOCGIFHWADDR };

#pragma once
/*
 * Copyright (C) 2017 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <memory>

#include "virtual_keyboard.h"
#include "virtual_power_button.h"
#include "virtual_touchscreen.h"

namespace vsoc {
namespace input_events {

struct InputEvent {
  uint16_t type;
  uint16_t code;
  uint32_t value;
};

} // namespace input_events
} // namespace vsoc

namespace vsoc_input_service {

class VSoCInputService {
 public:
  bool SetUpDevices();
  bool ProcessEvents();

 private:
  std::shared_ptr<VirtualPowerButton> virtual_power_button_;
  std::shared_ptr<VirtualKeyboard> virtual_keyboard_;
  std::shared_ptr<VirtualTouchScreen> virtual_touchscreen_;
};

}  // namespace vsoc_input_service

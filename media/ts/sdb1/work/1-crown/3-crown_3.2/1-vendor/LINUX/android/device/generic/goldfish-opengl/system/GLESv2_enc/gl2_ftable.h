// Generated Code - DO NOT EDIT !!
// generated by 'emugen'
#ifndef __gl2_client_ftable_t_h
#define __gl2_client_ftable_t_h


static const struct _gl2_funcs_by_name {
	const char *name;
	void *proc;
} gl2_funcs_by_name[] = {
	{"glActiveTexture", (void*)glActiveTexture},
	{"glAttachShader", (void*)glAttachShader},
	{"glBindAttribLocation", (void*)glBindAttribLocation},
	{"glBindBuffer", (void*)glBindBuffer},
	{"glBindFramebuffer", (void*)glBindFramebuffer},
	{"glBindRenderbuffer", (void*)glBindRenderbuffer},
	{"glBindTexture", (void*)glBindTexture},
	{"glBlendColor", (void*)glBlendColor},
	{"glBlendEquation", (void*)glBlendEquation},
	{"glBlendEquationSeparate", (void*)glBlendEquationSeparate},
	{"glBlendFunc", (void*)glBlendFunc},
	{"glBlendFuncSeparate", (void*)glBlendFuncSeparate},
	{"glBufferData", (void*)glBufferData},
	{"glBufferSubData", (void*)glBufferSubData},
	{"glCheckFramebufferStatus", (void*)glCheckFramebufferStatus},
	{"glClear", (void*)glClear},
	{"glClearColor", (void*)glClearColor},
	{"glClearDepthf", (void*)glClearDepthf},
	{"glClearStencil", (void*)glClearStencil},
	{"glColorMask", (void*)glColorMask},
	{"glCompileShader", (void*)glCompileShader},
	{"glCompressedTexImage2D", (void*)glCompressedTexImage2D},
	{"glCompressedTexSubImage2D", (void*)glCompressedTexSubImage2D},
	{"glCopyTexImage2D", (void*)glCopyTexImage2D},
	{"glCopyTexSubImage2D", (void*)glCopyTexSubImage2D},
	{"glCreateProgram", (void*)glCreateProgram},
	{"glCreateShader", (void*)glCreateShader},
	{"glCullFace", (void*)glCullFace},
	{"glDeleteBuffers", (void*)glDeleteBuffers},
	{"glDeleteFramebuffers", (void*)glDeleteFramebuffers},
	{"glDeleteProgram", (void*)glDeleteProgram},
	{"glDeleteRenderbuffers", (void*)glDeleteRenderbuffers},
	{"glDeleteShader", (void*)glDeleteShader},
	{"glDeleteTextures", (void*)glDeleteTextures},
	{"glDepthFunc", (void*)glDepthFunc},
	{"glDepthMask", (void*)glDepthMask},
	{"glDepthRangef", (void*)glDepthRangef},
	{"glDetachShader", (void*)glDetachShader},
	{"glDisable", (void*)glDisable},
	{"glDisableVertexAttribArray", (void*)glDisableVertexAttribArray},
	{"glDrawArrays", (void*)glDrawArrays},
	{"glDrawElements", (void*)glDrawElements},
	{"glEnable", (void*)glEnable},
	{"glEnableVertexAttribArray", (void*)glEnableVertexAttribArray},
	{"glFinish", (void*)glFinish},
	{"glFlush", (void*)glFlush},
	{"glFramebufferRenderbuffer", (void*)glFramebufferRenderbuffer},
	{"glFramebufferTexture2D", (void*)glFramebufferTexture2D},
	{"glFrontFace", (void*)glFrontFace},
	{"glGenBuffers", (void*)glGenBuffers},
	{"glGenerateMipmap", (void*)glGenerateMipmap},
	{"glGenFramebuffers", (void*)glGenFramebuffers},
	{"glGenRenderbuffers", (void*)glGenRenderbuffers},
	{"glGenTextures", (void*)glGenTextures},
	{"glGetActiveAttrib", (void*)glGetActiveAttrib},
	{"glGetActiveUniform", (void*)glGetActiveUniform},
	{"glGetAttachedShaders", (void*)glGetAttachedShaders},
	{"glGetAttribLocation", (void*)glGetAttribLocation},
	{"glGetBooleanv", (void*)glGetBooleanv},
	{"glGetBufferParameteriv", (void*)glGetBufferParameteriv},
	{"glGetError", (void*)glGetError},
	{"glGetFloatv", (void*)glGetFloatv},
	{"glGetFramebufferAttachmentParameteriv", (void*)glGetFramebufferAttachmentParameteriv},
	{"glGetIntegerv", (void*)glGetIntegerv},
	{"glGetProgramiv", (void*)glGetProgramiv},
	{"glGetProgramInfoLog", (void*)glGetProgramInfoLog},
	{"glGetRenderbufferParameteriv", (void*)glGetRenderbufferParameteriv},
	{"glGetShaderiv", (void*)glGetShaderiv},
	{"glGetShaderInfoLog", (void*)glGetShaderInfoLog},
	{"glGetShaderPrecisionFormat", (void*)glGetShaderPrecisionFormat},
	{"glGetShaderSource", (void*)glGetShaderSource},
	{"glGetString", (void*)glGetString},
	{"glGetTexParameterfv", (void*)glGetTexParameterfv},
	{"glGetTexParameteriv", (void*)glGetTexParameteriv},
	{"glGetUniformfv", (void*)glGetUniformfv},
	{"glGetUniformiv", (void*)glGetUniformiv},
	{"glGetUniformLocation", (void*)glGetUniformLocation},
	{"glGetVertexAttribfv", (void*)glGetVertexAttribfv},
	{"glGetVertexAttribiv", (void*)glGetVertexAttribiv},
	{"glGetVertexAttribPointerv", (void*)glGetVertexAttribPointerv},
	{"glHint", (void*)glHint},
	{"glIsBuffer", (void*)glIsBuffer},
	{"glIsEnabled", (void*)glIsEnabled},
	{"glIsFramebuffer", (void*)glIsFramebuffer},
	{"glIsProgram", (void*)glIsProgram},
	{"glIsRenderbuffer", (void*)glIsRenderbuffer},
	{"glIsShader", (void*)glIsShader},
	{"glIsTexture", (void*)glIsTexture},
	{"glLineWidth", (void*)glLineWidth},
	{"glLinkProgram", (void*)glLinkProgram},
	{"glPixelStorei", (void*)glPixelStorei},
	{"glPolygonOffset", (void*)glPolygonOffset},
	{"glReadPixels", (void*)glReadPixels},
	{"glReleaseShaderCompiler", (void*)glReleaseShaderCompiler},
	{"glRenderbufferStorage", (void*)glRenderbufferStorage},
	{"glSampleCoverage", (void*)glSampleCoverage},
	{"glScissor", (void*)glScissor},
	{"glShaderBinary", (void*)glShaderBinary},
	{"glShaderSource", (void*)glShaderSource},
	{"glStencilFunc", (void*)glStencilFunc},
	{"glStencilFuncSeparate", (void*)glStencilFuncSeparate},
	{"glStencilMask", (void*)glStencilMask},
	{"glStencilMaskSeparate", (void*)glStencilMaskSeparate},
	{"glStencilOp", (void*)glStencilOp},
	{"glStencilOpSeparate", (void*)glStencilOpSeparate},
	{"glTexImage2D", (void*)glTexImage2D},
	{"glTexParameterf", (void*)glTexParameterf},
	{"glTexParameterfv", (void*)glTexParameterfv},
	{"glTexParameteri", (void*)glTexParameteri},
	{"glTexParameteriv", (void*)glTexParameteriv},
	{"glTexSubImage2D", (void*)glTexSubImage2D},
	{"glUniform1f", (void*)glUniform1f},
	{"glUniform1fv", (void*)glUniform1fv},
	{"glUniform1i", (void*)glUniform1i},
	{"glUniform1iv", (void*)glUniform1iv},
	{"glUniform2f", (void*)glUniform2f},
	{"glUniform2fv", (void*)glUniform2fv},
	{"glUniform2i", (void*)glUniform2i},
	{"glUniform2iv", (void*)glUniform2iv},
	{"glUniform3f", (void*)glUniform3f},
	{"glUniform3fv", (void*)glUniform3fv},
	{"glUniform3i", (void*)glUniform3i},
	{"glUniform3iv", (void*)glUniform3iv},
	{"glUniform4f", (void*)glUniform4f},
	{"glUniform4fv", (void*)glUniform4fv},
	{"glUniform4i", (void*)glUniform4i},
	{"glUniform4iv", (void*)glUniform4iv},
	{"glUniformMatrix2fv", (void*)glUniformMatrix2fv},
	{"glUniformMatrix3fv", (void*)glUniformMatrix3fv},
	{"glUniformMatrix4fv", (void*)glUniformMatrix4fv},
	{"glUseProgram", (void*)glUseProgram},
	{"glValidateProgram", (void*)glValidateProgram},
	{"glVertexAttrib1f", (void*)glVertexAttrib1f},
	{"glVertexAttrib1fv", (void*)glVertexAttrib1fv},
	{"glVertexAttrib2f", (void*)glVertexAttrib2f},
	{"glVertexAttrib2fv", (void*)glVertexAttrib2fv},
	{"glVertexAttrib3f", (void*)glVertexAttrib3f},
	{"glVertexAttrib3fv", (void*)glVertexAttrib3fv},
	{"glVertexAttrib4f", (void*)glVertexAttrib4f},
	{"glVertexAttrib4fv", (void*)glVertexAttrib4fv},
	{"glVertexAttribPointer", (void*)glVertexAttribPointer},
	{"glViewport", (void*)glViewport},
	{"glEGLImageTargetTexture2DOES", (void*)glEGLImageTargetTexture2DOES},
	{"glEGLImageTargetRenderbufferStorageOES", (void*)glEGLImageTargetRenderbufferStorageOES},
	{"glGetProgramBinaryOES", (void*)glGetProgramBinaryOES},
	{"glProgramBinaryOES", (void*)glProgramBinaryOES},
	{"glMapBufferOES", (void*)glMapBufferOES},
	{"glUnmapBufferOES", (void*)glUnmapBufferOES},
	{"glTexImage3DOES", (void*)glTexImage3DOES},
	{"glTexSubImage3DOES", (void*)glTexSubImage3DOES},
	{"glCopyTexSubImage3DOES", (void*)glCopyTexSubImage3DOES},
	{"glCompressedTexImage3DOES", (void*)glCompressedTexImage3DOES},
	{"glCompressedTexSubImage3DOES", (void*)glCompressedTexSubImage3DOES},
	{"glFramebufferTexture3DOES", (void*)glFramebufferTexture3DOES},
	{"glBindVertexArrayOES", (void*)glBindVertexArrayOES},
	{"glDeleteVertexArraysOES", (void*)glDeleteVertexArraysOES},
	{"glGenVertexArraysOES", (void*)glGenVertexArraysOES},
	{"glIsVertexArrayOES", (void*)glIsVertexArrayOES},
	{"glDiscardFramebufferEXT", (void*)glDiscardFramebufferEXT},
	{"glMultiDrawArraysEXT", (void*)glMultiDrawArraysEXT},
	{"glMultiDrawElementsEXT", (void*)glMultiDrawElementsEXT},
	{"glGetPerfMonitorGroupsAMD", (void*)glGetPerfMonitorGroupsAMD},
	{"glGetPerfMonitorCountersAMD", (void*)glGetPerfMonitorCountersAMD},
	{"glGetPerfMonitorGroupStringAMD", (void*)glGetPerfMonitorGroupStringAMD},
	{"glGetPerfMonitorCounterStringAMD", (void*)glGetPerfMonitorCounterStringAMD},
	{"glGetPerfMonitorCounterInfoAMD", (void*)glGetPerfMonitorCounterInfoAMD},
	{"glGenPerfMonitorsAMD", (void*)glGenPerfMonitorsAMD},
	{"glDeletePerfMonitorsAMD", (void*)glDeletePerfMonitorsAMD},
	{"glSelectPerfMonitorCountersAMD", (void*)glSelectPerfMonitorCountersAMD},
	{"glBeginPerfMonitorAMD", (void*)glBeginPerfMonitorAMD},
	{"glEndPerfMonitorAMD", (void*)glEndPerfMonitorAMD},
	{"glGetPerfMonitorCounterDataAMD", (void*)glGetPerfMonitorCounterDataAMD},
	{"glRenderbufferStorageMultisampleIMG", (void*)glRenderbufferStorageMultisampleIMG},
	{"glFramebufferTexture2DMultisampleIMG", (void*)glFramebufferTexture2DMultisampleIMG},
	{"glDeleteFencesNV", (void*)glDeleteFencesNV},
	{"glGenFencesNV", (void*)glGenFencesNV},
	{"glIsFenceNV", (void*)glIsFenceNV},
	{"glTestFenceNV", (void*)glTestFenceNV},
	{"glGetFenceivNV", (void*)glGetFenceivNV},
	{"glFinishFenceNV", (void*)glFinishFenceNV},
	{"glSetFenceNV", (void*)glSetFenceNV},
	{"glCoverageMaskNV", (void*)glCoverageMaskNV},
	{"glCoverageOperationNV", (void*)glCoverageOperationNV},
	{"glGetDriverControlsQCOM", (void*)glGetDriverControlsQCOM},
	{"glGetDriverControlStringQCOM", (void*)glGetDriverControlStringQCOM},
	{"glEnableDriverControlQCOM", (void*)glEnableDriverControlQCOM},
	{"glDisableDriverControlQCOM", (void*)glDisableDriverControlQCOM},
	{"glExtGetTexturesQCOM", (void*)glExtGetTexturesQCOM},
	{"glExtGetBuffersQCOM", (void*)glExtGetBuffersQCOM},
	{"glExtGetRenderbuffersQCOM", (void*)glExtGetRenderbuffersQCOM},
	{"glExtGetFramebuffersQCOM", (void*)glExtGetFramebuffersQCOM},
	{"glExtGetTexLevelParameterivQCOM", (void*)glExtGetTexLevelParameterivQCOM},
	{"glExtTexObjectStateOverrideiQCOM", (void*)glExtTexObjectStateOverrideiQCOM},
	{"glExtGetTexSubImageQCOM", (void*)glExtGetTexSubImageQCOM},
	{"glExtGetBufferPointervQCOM", (void*)glExtGetBufferPointervQCOM},
	{"glExtGetShadersQCOM", (void*)glExtGetShadersQCOM},
	{"glExtGetProgramsQCOM", (void*)glExtGetProgramsQCOM},
	{"glExtIsProgramBinaryQCOM", (void*)glExtIsProgramBinaryQCOM},
	{"glExtGetProgramBinarySourceQCOM", (void*)glExtGetProgramBinarySourceQCOM},
	{"glStartTilingQCOM", (void*)glStartTilingQCOM},
	{"glEndTilingQCOM", (void*)glEndTilingQCOM},
	{"glGenVertexArrays", (void*)glGenVertexArrays},
	{"glBindVertexArray", (void*)glBindVertexArray},
	{"glDeleteVertexArrays", (void*)glDeleteVertexArrays},
	{"glIsVertexArray", (void*)glIsVertexArray},
	{"glMapBufferRange", (void*)glMapBufferRange},
	{"glUnmapBuffer", (void*)glUnmapBuffer},
	{"glFlushMappedBufferRange", (void*)glFlushMappedBufferRange},
	{"glBindBufferRange", (void*)glBindBufferRange},
	{"glBindBufferBase", (void*)glBindBufferBase},
	{"glCopyBufferSubData", (void*)glCopyBufferSubData},
	{"glClearBufferiv", (void*)glClearBufferiv},
	{"glClearBufferuiv", (void*)glClearBufferuiv},
	{"glClearBufferfv", (void*)glClearBufferfv},
	{"glClearBufferfi", (void*)glClearBufferfi},
	{"glGetBufferParameteri64v", (void*)glGetBufferParameteri64v},
	{"glGetBufferPointerv", (void*)glGetBufferPointerv},
	{"glUniformBlockBinding", (void*)glUniformBlockBinding},
	{"glGetUniformBlockIndex", (void*)glGetUniformBlockIndex},
	{"glGetUniformIndices", (void*)glGetUniformIndices},
	{"glGetActiveUniformBlockiv", (void*)glGetActiveUniformBlockiv},
	{"glGetActiveUniformBlockName", (void*)glGetActiveUniformBlockName},
	{"glUniform1ui", (void*)glUniform1ui},
	{"glUniform2ui", (void*)glUniform2ui},
	{"glUniform3ui", (void*)glUniform3ui},
	{"glUniform4ui", (void*)glUniform4ui},
	{"glUniform1uiv", (void*)glUniform1uiv},
	{"glUniform2uiv", (void*)glUniform2uiv},
	{"glUniform3uiv", (void*)glUniform3uiv},
	{"glUniform4uiv", (void*)glUniform4uiv},
	{"glUniformMatrix2x3fv", (void*)glUniformMatrix2x3fv},
	{"glUniformMatrix3x2fv", (void*)glUniformMatrix3x2fv},
	{"glUniformMatrix2x4fv", (void*)glUniformMatrix2x4fv},
	{"glUniformMatrix4x2fv", (void*)glUniformMatrix4x2fv},
	{"glUniformMatrix3x4fv", (void*)glUniformMatrix3x4fv},
	{"glUniformMatrix4x3fv", (void*)glUniformMatrix4x3fv},
	{"glGetUniformuiv", (void*)glGetUniformuiv},
	{"glGetActiveUniformsiv", (void*)glGetActiveUniformsiv},
	{"glVertexAttribI4i", (void*)glVertexAttribI4i},
	{"glVertexAttribI4ui", (void*)glVertexAttribI4ui},
	{"glVertexAttribI4iv", (void*)glVertexAttribI4iv},
	{"glVertexAttribI4uiv", (void*)glVertexAttribI4uiv},
	{"glVertexAttribIPointer", (void*)glVertexAttribIPointer},
	{"glGetVertexAttribIiv", (void*)glGetVertexAttribIiv},
	{"glGetVertexAttribIuiv", (void*)glGetVertexAttribIuiv},
	{"glVertexAttribDivisor", (void*)glVertexAttribDivisor},
	{"glDrawArraysInstanced", (void*)glDrawArraysInstanced},
	{"glDrawElementsInstanced", (void*)glDrawElementsInstanced},
	{"glDrawRangeElements", (void*)glDrawRangeElements},
	{"glFenceSync", (void*)glFenceSync},
	{"glClientWaitSync", (void*)glClientWaitSync},
	{"glWaitSync", (void*)glWaitSync},
	{"glDeleteSync", (void*)glDeleteSync},
	{"glIsSync", (void*)glIsSync},
	{"glGetSynciv", (void*)glGetSynciv},
	{"glDrawBuffers", (void*)glDrawBuffers},
	{"glReadBuffer", (void*)glReadBuffer},
	{"glBlitFramebuffer", (void*)glBlitFramebuffer},
	{"glInvalidateFramebuffer", (void*)glInvalidateFramebuffer},
	{"glInvalidateSubFramebuffer", (void*)glInvalidateSubFramebuffer},
	{"glFramebufferTextureLayer", (void*)glFramebufferTextureLayer},
	{"glRenderbufferStorageMultisample", (void*)glRenderbufferStorageMultisample},
	{"glTexStorage2D", (void*)glTexStorage2D},
	{"glGetInternalformativ", (void*)glGetInternalformativ},
	{"glBeginTransformFeedback", (void*)glBeginTransformFeedback},
	{"glEndTransformFeedback", (void*)glEndTransformFeedback},
	{"glGenTransformFeedbacks", (void*)glGenTransformFeedbacks},
	{"glDeleteTransformFeedbacks", (void*)glDeleteTransformFeedbacks},
	{"glBindTransformFeedback", (void*)glBindTransformFeedback},
	{"glPauseTransformFeedback", (void*)glPauseTransformFeedback},
	{"glResumeTransformFeedback", (void*)glResumeTransformFeedback},
	{"glIsTransformFeedback", (void*)glIsTransformFeedback},
	{"glTransformFeedbackVaryings", (void*)glTransformFeedbackVaryings},
	{"glGetTransformFeedbackVarying", (void*)glGetTransformFeedbackVarying},
	{"glGenSamplers", (void*)glGenSamplers},
	{"glDeleteSamplers", (void*)glDeleteSamplers},
	{"glBindSampler", (void*)glBindSampler},
	{"glSamplerParameterf", (void*)glSamplerParameterf},
	{"glSamplerParameteri", (void*)glSamplerParameteri},
	{"glSamplerParameterfv", (void*)glSamplerParameterfv},
	{"glSamplerParameteriv", (void*)glSamplerParameteriv},
	{"glGetSamplerParameterfv", (void*)glGetSamplerParameterfv},
	{"glGetSamplerParameteriv", (void*)glGetSamplerParameteriv},
	{"glIsSampler", (void*)glIsSampler},
	{"glGenQueries", (void*)glGenQueries},
	{"glDeleteQueries", (void*)glDeleteQueries},
	{"glBeginQuery", (void*)glBeginQuery},
	{"glEndQuery", (void*)glEndQuery},
	{"glGetQueryiv", (void*)glGetQueryiv},
	{"glGetQueryObjectuiv", (void*)glGetQueryObjectuiv},
	{"glIsQuery", (void*)glIsQuery},
	{"glProgramParameteri", (void*)glProgramParameteri},
	{"glProgramBinary", (void*)glProgramBinary},
	{"glGetProgramBinary", (void*)glGetProgramBinary},
	{"glGetFragDataLocation", (void*)glGetFragDataLocation},
	{"glGetInteger64v", (void*)glGetInteger64v},
	{"glGetIntegeri_v", (void*)glGetIntegeri_v},
	{"glGetInteger64i_v", (void*)glGetInteger64i_v},
	{"glTexImage3D", (void*)glTexImage3D},
	{"glTexStorage3D", (void*)glTexStorage3D},
	{"glTexSubImage3D", (void*)glTexSubImage3D},
	{"glCompressedTexImage3D", (void*)glCompressedTexImage3D},
	{"glCompressedTexSubImage3D", (void*)glCompressedTexSubImage3D},
	{"glCopyTexSubImage3D", (void*)glCopyTexSubImage3D},
	{"glGetStringi", (void*)glGetStringi},
	{"glGetBooleani_v", (void*)glGetBooleani_v},
	{"glMemoryBarrier", (void*)glMemoryBarrier},
	{"glMemoryBarrierByRegion", (void*)glMemoryBarrierByRegion},
	{"glGenProgramPipelines", (void*)glGenProgramPipelines},
	{"glDeleteProgramPipelines", (void*)glDeleteProgramPipelines},
	{"glBindProgramPipeline", (void*)glBindProgramPipeline},
	{"glGetProgramPipelineiv", (void*)glGetProgramPipelineiv},
	{"glGetProgramPipelineInfoLog", (void*)glGetProgramPipelineInfoLog},
	{"glValidateProgramPipeline", (void*)glValidateProgramPipeline},
	{"glIsProgramPipeline", (void*)glIsProgramPipeline},
	{"glUseProgramStages", (void*)glUseProgramStages},
	{"glActiveShaderProgram", (void*)glActiveShaderProgram},
	{"glCreateShaderProgramv", (void*)glCreateShaderProgramv},
	{"glProgramUniform1f", (void*)glProgramUniform1f},
	{"glProgramUniform2f", (void*)glProgramUniform2f},
	{"glProgramUniform3f", (void*)glProgramUniform3f},
	{"glProgramUniform4f", (void*)glProgramUniform4f},
	{"glProgramUniform1i", (void*)glProgramUniform1i},
	{"glProgramUniform2i", (void*)glProgramUniform2i},
	{"glProgramUniform3i", (void*)glProgramUniform3i},
	{"glProgramUniform4i", (void*)glProgramUniform4i},
	{"glProgramUniform1ui", (void*)glProgramUniform1ui},
	{"glProgramUniform2ui", (void*)glProgramUniform2ui},
	{"glProgramUniform3ui", (void*)glProgramUniform3ui},
	{"glProgramUniform4ui", (void*)glProgramUniform4ui},
	{"glProgramUniform1fv", (void*)glProgramUniform1fv},
	{"glProgramUniform2fv", (void*)glProgramUniform2fv},
	{"glProgramUniform3fv", (void*)glProgramUniform3fv},
	{"glProgramUniform4fv", (void*)glProgramUniform4fv},
	{"glProgramUniform1iv", (void*)glProgramUniform1iv},
	{"glProgramUniform2iv", (void*)glProgramUniform2iv},
	{"glProgramUniform3iv", (void*)glProgramUniform3iv},
	{"glProgramUniform4iv", (void*)glProgramUniform4iv},
	{"glProgramUniform1uiv", (void*)glProgramUniform1uiv},
	{"glProgramUniform2uiv", (void*)glProgramUniform2uiv},
	{"glProgramUniform3uiv", (void*)glProgramUniform3uiv},
	{"glProgramUniform4uiv", (void*)glProgramUniform4uiv},
	{"glProgramUniformMatrix2fv", (void*)glProgramUniformMatrix2fv},
	{"glProgramUniformMatrix3fv", (void*)glProgramUniformMatrix3fv},
	{"glProgramUniformMatrix4fv", (void*)glProgramUniformMatrix4fv},
	{"glProgramUniformMatrix2x3fv", (void*)glProgramUniformMatrix2x3fv},
	{"glProgramUniformMatrix3x2fv", (void*)glProgramUniformMatrix3x2fv},
	{"glProgramUniformMatrix2x4fv", (void*)glProgramUniformMatrix2x4fv},
	{"glProgramUniformMatrix4x2fv", (void*)glProgramUniformMatrix4x2fv},
	{"glProgramUniformMatrix3x4fv", (void*)glProgramUniformMatrix3x4fv},
	{"glProgramUniformMatrix4x3fv", (void*)glProgramUniformMatrix4x3fv},
	{"glGetProgramInterfaceiv", (void*)glGetProgramInterfaceiv},
	{"glGetProgramResourceiv", (void*)glGetProgramResourceiv},
	{"glGetProgramResourceIndex", (void*)glGetProgramResourceIndex},
	{"glGetProgramResourceLocation", (void*)glGetProgramResourceLocation},
	{"glGetProgramResourceName", (void*)glGetProgramResourceName},
	{"glBindImageTexture", (void*)glBindImageTexture},
	{"glDispatchCompute", (void*)glDispatchCompute},
	{"glDispatchComputeIndirect", (void*)glDispatchComputeIndirect},
	{"glBindVertexBuffer", (void*)glBindVertexBuffer},
	{"glVertexAttribBinding", (void*)glVertexAttribBinding},
	{"glVertexAttribFormat", (void*)glVertexAttribFormat},
	{"glVertexAttribIFormat", (void*)glVertexAttribIFormat},
	{"glVertexBindingDivisor", (void*)glVertexBindingDivisor},
	{"glDrawArraysIndirect", (void*)glDrawArraysIndirect},
	{"glDrawElementsIndirect", (void*)glDrawElementsIndirect},
	{"glTexStorage2DMultisample", (void*)glTexStorage2DMultisample},
	{"glSampleMaski", (void*)glSampleMaski},
	{"glGetMultisamplefv", (void*)glGetMultisamplefv},
	{"glFramebufferParameteri", (void*)glFramebufferParameteri},
	{"glGetFramebufferParameteriv", (void*)glGetFramebufferParameteriv},
	{"glGetTexLevelParameterfv", (void*)glGetTexLevelParameterfv},
	{"glGetTexLevelParameteriv", (void*)glGetTexLevelParameteriv},
	{"glGetGraphicsResetStatusEXT", (void*)glGetGraphicsResetStatusEXT},
	{"glReadnPixelsEXT", (void*)glReadnPixelsEXT},
	{"glGetnUniformfvEXT", (void*)glGetnUniformfvEXT},
	{"glGetnUniformivEXT", (void*)glGetnUniformivEXT},
	{"glDrawArraysNullAEMU", (void*)glDrawArraysNullAEMU},
	{"glDrawElementsNullAEMU", (void*)glDrawElementsNullAEMU},
};
static const int gl2_num_funcs = sizeof(gl2_funcs_by_name) / sizeof(struct _gl2_funcs_by_name);


#endif

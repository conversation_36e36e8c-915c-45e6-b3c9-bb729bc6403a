// Copyright (C) 2017 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

cc_library_shared {
    name: "cuttlefish_net",
    srcs: [
        "netlink_client.cpp",
        "netlink_request.cpp",
        "network_interface_manager.cpp",
    ],
    shared_libs: [
        "libcuttlefish_fs",
        "libbase",
    ],
    defaults: ["cuttlefish_host_and_guest"],
}

cc_test_host {
    name: "cuttlefish_net_tests",
    srcs: [
        "netlink_request_test.cpp",
    ],
    shared_libs: [
        "libcuttlefish_fs",
        "cuttlefish_net",
        "libbase",
    ],
    static_libs: [
        "libgmock",
        "libgtest_host",
    ],
    defaults: ["cuttlefish_host_only"],
    test_suites: ["general-tests"],
}

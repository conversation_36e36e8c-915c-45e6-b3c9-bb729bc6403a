//
// Copyright (C) 2016 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

cc_binary_host {
    name: "nanoapp_postprocess",

    srcs: ["postprocess_elf.c"],

    cflags: [
        "-<PERSON>",
        "-Werror",
        "-Wextra",
    ],

    static_libs: [
        "libnanohub_common",

        // libelf needed for ELF parsing support, libz required by libelf
        "libelf",
        "libz",
    ],

    // Statically linking libc++ so this binary can be copied out of the tree and
    // still work (needed by dependencies)
    stl: "libc++_static",

    target: {
        // libelf is not available in the Mac build as of June 2016, but we currently
        // only need to use this tool on Linux, so exclude this from non-Linux builds
        darwin: {
            enabled: false,
        },
    },
}

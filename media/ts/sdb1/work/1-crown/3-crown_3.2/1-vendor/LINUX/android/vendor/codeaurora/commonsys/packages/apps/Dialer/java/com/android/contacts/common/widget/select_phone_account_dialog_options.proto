// Copyright (C) 2018 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License

syntax = "proto2";

option java_package = "com.android.contacts.common.widget";
option java_multiple_files = true;
option optimize_for = LITE_RUNTIME;


package com.android.contacts.common.widget;

// Parameters for SelectPhoneAccountDialogFragment
message SelectPhoneAccountDialogOptions {
  // The resource ID for the title. Defaults to
  // R.string.select_account_dialog_title
  optional int32 title = 1;
  // Whether the dialog should include a "set as default" checkbox. Defaults to
  // false
  optional bool can_set_default = 2;
  // The label on the "set as default" checkbox. Defaults
  // R.string.set_default_account
  optional int32 set_default_label = 3;
  // The call ID to pass back to the callback
  optional string call_id = 4;
  // Phone accounts to show in the dialog
  repeated Entry entries = 5;

  message Entry {
    // PhoneAccountHandle.getComponentName().flattenToString()
    optional string phone_account_handle_component_name = 1;
    // PhoneAccountHandle.getId()
    optional string phone_account_handle_id = 2;
    // The hint to show under the phone account, for example showing the user
    // the account was selected frequently before.
    optional string hint = 3;
    // Whether the account is actually selectable. Defaults to true. Sometimes
    // an account will be temporarily unusable, for example the user is already
    // in a call so the other SIM cannot be used. Hint should also be set to
    // inform the user why the account is unavailable.
    optional bool enabled = 4 [default = true];

    optional bytes user_handle = 5;
  }
}
###############################################################################
# Debug options
NFC_DEBUG_ENABLED=0

###############################################################################
# File used for NFA storage
NFA_STORAGE="/data/nfc"
PRESERVE_STORAGE=0x01

###############################################################################
# When screen is turned off, specify the desired power state of the controller.
# 0: power-off-sleep state; DEFAULT
# 1: full-power state
# 2: screen-off card-emulation (CE4/CE3/CE1 modes are used)
SCREEN_OFF_POWER_STATE=1

###############################################################################
# Default poll duration (in ms)
#  The defualt is 500ms if not set (see nfc_target.h)
#NFA_DM_DISC_DURATION_POLL=333

###############################################################################
# Force tag polling for the following technology(s).
# The bits are defined as tNFA_TECHNOLOGY_MASK in nfa_api.h.
# Default is NFA_TECHNOLOGY_MASK_A | NFA_TECHNOLOGY_MASK_B |
#            NFA_TECHNOLOGY_MASK_F | NFA_TECHNOLOGY_MASK_ISO15693 |
#            NFA_TECHNOLOGY_MASK_B_PRIME | NFA_TECHNOLOGY_MASK_KOVIO |
#            NFA_TECHNOLOGY_MASK_A_ACTIVE | NFA_TECHNOLOGY_MASK_F_ACTIVE.
#
# Notable bits:
# NFA_TECHNOLOGY_MASK_A             0x01    /* NFC Technology A             */
# NFA_TECHNOLOGY_MASK_B             0x02    /* NFC Technology B             */
# NFA_TECHNOLOGY_MASK_F             0x04    /* NFC Technology F             */
# NFA_TECHNOLOGY_MASK_ISO15693      0x08    /* Proprietary Technology       */
# NFA_TECHNOLOGY_MASK_KOVIO         0x20    /* Proprietary Technology       */
# NFA_TECHNOLOGY_MASK_A_ACTIVE      0x40    /* NFC Technology A active mode */
# NFA_TECHNOLOGY_MASK_F_ACTIVE      0x80    /* NFC Technology F active mode */
POLLING_TECH_MASK=0x2F

###############################################################################
# Force P2P to only listen for the following technology(s).
# The bits are defined as tNFA_TECHNOLOGY_MASK in nfa_api.h.
# Default is NFA_TECHNOLOGY_MASK_A | NFA_TECHNOLOGY_MASK_F |
#            NFA_TECHNOLOGY_MASK_A_ACTIVE | NFA_TECHNOLOGY_MASK_F_ACTIVE
#
# Notable bits:
# NFA_TECHNOLOGY_MASK_A             0x01    /* NFC Technology A             */
# NFA_TECHNOLOGY_MASK_F             0x04    /* NFC Technology F             */
# NFA_TECHNOLOGY_MASK_A_ACTIVE      0x40    /* NFC Technology A active mode */
# NFA_TECHNOLOGY_MASK_F_ACTIVE      0x80    /* NFC Technology F active mode */
P2P_LISTEN_TECH_MASK=0x00

###############################################################################
# Force UICC to only listen to the following technology(s).
# The bits are defined as tNFA_TECHNOLOGY_MASK in nfa_api.h.
# Default is NFA_TECHNOLOGY_MASK_A | NFA_TECHNOLOGY_MASK_B | NFA_TECHNOLOGY_MASK_F
UICC_LISTEN_TECH_MASK=0x07

###############################################################################
# Override the stack default for NFA_EE_MAX_EE_SUPPORTED set in nfc_target.h.
# The value is set to 3 by default as it assumes we will discover 0xF2,
# 0xF3, and 0xF4. If a platform will exclude and SE, this value can be reduced
# so that the stack will not wait any longer than necessary.
# Maximum EE supported number
# NXP PN547C2 0x02
# NXP PN65T 0x03
# NXP PN548C2 0x02
# NXP PN66T 0x03
NFA_MAX_EE_SUPPORTED=0x02

###############################################################################
# AID for Empty Select command
# If specified, this AID will be substituted when an Empty SELECT command is
# detected.  The first byte is the length of the AID.  Maximum length is 16.
AID_FOR_EMPTY_SELECT={08:A0:00:00:01:51:00:00:00}

###############################################################################
# AID_MATCHING constants
# AID_MATCHING_EXACT_ONLY 0x00
# AID_MATCHING_EXACT_OR_PREFIX 0x01
# AID_MATCHING_PREFIX_ONLY 0x02
AID_MATCHING_MODE=0x01

<?xml version="1.0" encoding="utf-8"?>
<!--
 Copyright (C) 2014 The CyanogenMod Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="16dip"
    android:paddingEnd="16dip"
    android:orientation="horizontal"
    android:minHeight="?android:attr/listPreferredItemHeight">

    <TextView
        android:id="@+id/index"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="12dp"
        android:textAppearance="?android:attr/textAppearanceLarge" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_gravity="center_vertical"
        android:orientation="vertical">

        <TextView
            android:id="@+id/name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/setting_primary_color"
            android:textSize="@dimen/call_log_primary_text_size" />

        <TextView
            android:id="@+id/number"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/call_log_name_margin_bottom"
            android:textColor="@color/setting_primary_color"
            android:textSize="@dimen/call_log_secondary_text_size" />

    </LinearLayout>

    <QuickContactBadge
        android:id="@+id/photo"
        android:layout_width="@dimen/contact_photo_size"
        android:layout_height="@dimen/contact_photo_size"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="8dp" />

</LinearLayout>


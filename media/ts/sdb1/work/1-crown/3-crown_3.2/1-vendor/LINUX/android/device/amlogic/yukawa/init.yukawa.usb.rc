on boot
    mount configfs none /config
    mkdir /config/usb_gadget/g1 0770 shell shell
    mkdir /config/usb_gadget/g1/strings/0x409 0770 shell shell
    write /config/usb_gadget/g1/bcdUSB 0x0200
    write /config/usb_gadget/g1/idVendor 0x18d1
    write /config/usb_gadget/g1/bcdDevice 0x0223
    write /config/usb_gadget/g1/strings/0x409/serialnumber ${ro.serialno}
    write /config/usb_gadget/g1/strings/0x409/manufacturer SEI
    write /config/usb_gadget/g1/strings/0x409/product ${ro.hardware}
    mkdir /config/usb_gadget/g1/functions/ffs.adb
    mkdir /config/usb_gadget/g1/functions/ffs.mtp
    mkdir /config/usb_gadget/g1/functions/ffs.ptp
    mkdir /config/usb_gadget/g1/functions/gsi.rndis
    mkdir /config/usb_gadget/g1/configs/b.1 0770 shell shell
    mkdir /config/usb_gadget/g1/configs/b.1/strings/0x409 0770 shell shell
    write /config/usb_gadget/g1/configs/b.1/MaxPower 500
    mkdir /dev/usb-ffs 0775 shell shell
    mkdir /dev/usb-ffs/adb 0770 shell shell
    mkdir /dev/usb-ffs/mtp 0770 mtp mtp
    mkdir /dev/usb-ffs/ptp 0770 mtp mtp
    mount functionfs adb /dev/usb-ffs/adb uid=2000,gid=2000
    mount functionfs mtp /dev/usb-ffs/mtp rmode=0770,fmode=0660,uid=1024,gid=1024,no_disconnect=1
    mount functionfs ptp /dev/usb-ffs/ptp rmode=0770,fmode=0660,uid=1024,gid=1024,no_disconnect=1
    setprop sys.usb.mtp.device_type 3
    setprop sys.usb.configfs 1
    setprop sys.usb.controller "ff400000.usb"
    symlink /config/usb_gadget/g1/configs/b.1 /config/usb_gadget/g1/os_desc/b.1
    write /config/usb_gadget/g1/functions/gsi.rndis/rndis_class_id 1


on property:sys.usb.config=none && property:sys.usb.configfs=1
    setprop sys.usb.ffs.ready 0

on property:init.svc.adbd=stopped
    setprop sys.usb.ffs.ready 0

on property:sys.usb.config=mtp && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/idProduct 0x4e41

on property:sys.usb.config=mtp,adb && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/idProduct 0x4e12

on property:sys.usb.config=adb && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/idProduct 0x4e40
    write /sys/class/usb_role/ffe09000.usb-role-switch/role device

on early-init
    setprop ro.setupwizard.mode ${ro.boot.setupwizard_mode}

on property:ro.boot.tombstone_transmit=1
    enable tombstone_transmit


service tombstone_transmit /product/bin/tombstone_transmit
    # Start tombstone_transmit after /data is mounted.
    class late_start
    group system
    user root
    disabled

# TODO: disable this service once cuttlefish implements system suspend
service suspend_blocker /product/bin/suspend_blocker
    class main
    group system
    user root

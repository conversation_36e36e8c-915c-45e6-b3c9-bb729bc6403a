//
// Copyright (C) 2017 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

cc_library {
    name: "libcuttlefish_fs",
    srcs: [
        "shared_buf.cc",
        "shared_fd.cpp",
    ],
    shared: {
        shared_libs: [
            "libbase",
            "liblog",
        ],
    },
    static: {
        static_libs: [
            "libbase",
            "liblog",
        ],
    },
    target: {
        vendor: {
            // Liblog does not have a vendor-static variant.
            shared_libs: ["liblog"],
            exclude_static_libs: ["liblog"],
        },
    },
    defaults: ["cuttlefish_host_and_guest"],
}

cc_library_static {
    name: "libcuttlefish_fs_product",
    srcs: [
        "shared_buf.cc",
        "shared_fd.cpp",
    ],
    shared_libs: [
        "libbase",
        "liblog",
    ],
    stl: "libc++_static",
    defaults: ["cuttlefish_guest_product_only"],
}

cc_test_host {
    name: "libcuttlefish_fs_tests",
    srcs: [
        "shared_fd_test.cpp",
    ],
    shared_libs: [
        "libcuttlefish_fs",
        "libbase",
    ],
    static_libs: [
        "libgmock",
        "libgtest_host",
    ],
    defaults: ["cuttlefish_host_only"],
    test_suites: ["general-tests"],
}

FOO_ENTRY(void, fooAlphaFunc, FooInt func, FooFloat ref)
FOO_ENTRY(FooBoolean, fooIsBuffer, void* stuff)
FOO_ENTRY(void, fooUnsupported, void* params)
FOO_ENTRY(void, fooDoEncoderFlush, FooInt param)
FOO_ENTRY(void, fooTakeConstVoidPtrConstPtr, const void* const* param)
FOO_ENTRY(void, fooSetComplexStruct, const FooStruct* obj)
FOO_ENTRY(void, fooGetComplexStruct, FooStruct* obj)
FOO_ENTRY(void, fooInout, uint32_t* count)

<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<merge xmlns:android="http://schemas.android.com/apk/res/android">
  <LinearLayout
    android:layout_width="0dp"
    android:layout_height="match_parent"
    android:layout_weight="4"
    android:orientation="vertical">

    <Space
      android:layout_width="match_parent"
      android:layout_height="0dp"
      android:layout_weight="1"/>
    <ImageView
      android:id="@+id/empty_list_view_image"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_gravity="center_horizontal"
      android:importantForAccessibility="no"/>

    <TextView
      android:id="@+id/empty_list_view_message"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:paddingTop="8dp"
      android:paddingBottom="8dp"
      android:paddingLeft="16dp"
      android:paddingRight="16dp"
      android:gravity="center_horizontal|top"
      style="Dialer.TextAppearance.Primary"/>

    <TextView
      android:id="@+id/empty_list_view_action"
      style="@style/TextActionStyle"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_gravity="center_horizontal"
      android:paddingTop="8dp"
      android:paddingBottom="8dp"
      android:paddingLeft="16dp"
      android:paddingRight="16dp"
      android:background="?android:attr/selectableItemBackground"
      android:clickable="true"
      android:gravity="center_horizontal"/>
    <Space
      android:layout_width="match_parent"
      android:layout_height="0dp"
      android:layout_weight="1"/>
  </LinearLayout>

  <Space
    android:layout_width="0dp"
    android:layout_height="match_parent"
    android:layout_weight="6"/>

</merge>

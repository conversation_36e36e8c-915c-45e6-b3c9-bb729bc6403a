#
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

include $(NANO_BUILD)/common_config.mk

LOCAL_CFLAGS += \
    -g \
    -ggdb3 \
    -D_OS_BUILD_ \
    -O2

LOCAL_CFLAGS_x86 += \
    -m32

LOCAL_NANO_MODULE_TYPE := BL
LOCAL_FORCE_STATIC_EXECUTABLE := true

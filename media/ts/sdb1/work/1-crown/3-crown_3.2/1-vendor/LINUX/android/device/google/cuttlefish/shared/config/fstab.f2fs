/dev/block/by-name/boot /boot emmc defaults recoveryonly,slotselect
/dev/block/by-name/vendor_boot /vendor_boot emmc defaults recoveryonly,slotselect
system /system ext4 noatime,ro,errors=panic wait,logical,first_stage_mount,slotselect,avb=vbmeta_system
# Add all non-dynamic partitions except system, after this comment
/dev/block/by-name/userdata /data f2fs nodev,noatime,nosuid,inlinecrypt,reserve_root=32768 latemount,wait,fileencryption=aes-256-xts:aes-256-cts:v2+inlinecrypt_optimized,fsverity,keydirectory=/metadata/vold/metadata_encryption
/dev/block/by-name/cache /cache ext4 nodev,noatime,nosuid,errors=panic wait
/dev/block/by-name/metadata /metadata ext4 nodev,noatime,nosuid,errors=panic wait,formattable,first_stage_mount,check
/dev/block/by-name/misc /misc emmc defaults defaults
# Add all dynamic partitions except system, after this comment
odm /odm ext4 noatime,ro,errors=panic wait,logical,first_stage_mount,slotselect,avb
product /product ext4 noatime,ro,errors=panic wait,logical,first_stage_mount,slotselect,avb
system_ext /system_ext ext4 noatime,ro,errors=panic wait,logical,first_stage_mount,slotselect,avb=vbmeta_system
vendor /vendor ext4 noatime,ro,errors=panic wait,logical,first_stage_mount,slotselect,avb=vbmeta
/dev/block/zram0 none swap defaults zramsize=75%
/tmp /sdcard none defaults,bind recoveryonly
/devices/*/block/vdb auto auto defaults voldmanaged=sdcard1:auto,encryptable=userdata

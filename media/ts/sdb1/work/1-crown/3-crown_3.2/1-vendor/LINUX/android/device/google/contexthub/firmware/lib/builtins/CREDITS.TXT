This file is a partial list of people who have contributed to the LLVM/CompilerRT
project.  If you have contributed a patch or made some other contribution to
LLVM/CompilerRT, please submit a patch to this file to add yourself, and it will be
done!

The list is sorted by surname and formatted to allow easy grepping and
beautification by scripts.  The fields are: name (N), email (E), web-address
(W), PGP key ID and fingerprint (P), description (D), and snail-mail address
(S).

N: <PERSON>
E: <EMAIL>
W: http://www.auroraux.org
D: Code style and Readability fixes.

N: <PERSON>
E: e<PERSON><PERSON><PERSON><PERSON>@auroraux.org
W: http://www.auroraux.org
D: CMake'ify Compiler-RT build system
D: Maintain Solaris & AuroraUX ports of Compiler-RT

N: <PERSON>
E: <EMAIL>
D: Architect and primary author of compiler-rt

N: <PERSON>uan-<PERSON>
E: <EMAIL>
D: IEEE Quad-precision functions

N: <PERSON><PERSON>
E: <EMAIL>
D: Maintains NetBSD port.

N: <PERSON>
E: <EMAIL>
D: ARM improvements.

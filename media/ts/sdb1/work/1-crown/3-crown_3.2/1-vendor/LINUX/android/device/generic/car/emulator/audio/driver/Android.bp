//
// Copyright (C) 2017 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Derived from device/generic/goldfish/audio/Android.mk

cc_library_shared {

    vendor: true,
    vintf_fragments: ["<EMAIL>"],
    name: "audio.primary.caremu",
    relative_install_path: "hw",

    srcs: [
        "audio_hw.c",
        "audio_vbuffer.c",
        "ext_pcm.c",
    ],

    include_dirs: ["external/tinyalsa/include"],

    shared_libs: [
        "libcutils",
        "liblog",
        "libdl",
        "libtinyal<PERSON>",
    ],

    cflags: ["-Wno-unused-parameter"],
    header_libs: [
        "libhardware_headers",
        "libcutils_headers",
    ],

}

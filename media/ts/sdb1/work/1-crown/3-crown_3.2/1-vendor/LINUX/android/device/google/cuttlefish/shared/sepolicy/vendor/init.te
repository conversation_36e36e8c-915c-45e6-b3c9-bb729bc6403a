# /dev/tty* I/O. Needed for /dev/ttyS0
allow init serial_device:chr_file rw_file_perms;

# Write to /configfs files. Needed only for /config/usb_gadget subtree.
allow init configfs:file w_file_perms;
allow init configfs:lnk_file create;

# Add loadable modules. Needed for usbfunc:diag, usbfunc:diag, usbfunc:gsi, usbfunc:qdss modules.
allow init kernel:system module_request;

# binfmt_misc arm for ndk translator
allow init binfmt_miscfs:file w_file_perms;
allow init proc:dir mounton;

# /mnt/sdcard -> /storage/self/primary symlink is deprecated. Ignore attempts to
# create it. This denial is fixed in core policy in Android R aosp/943799.
dontaudit init tmpfs:lnk_file create;

// Copyright (C) 2018 The Android Open Source Project
// Copyright (C) 2018 Google Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Autogenerated module func_table
// (impl) generated by android/android-emugl/host/libs/libOpenglRender/vulkan-registry/xml/genvk.py -registry android/android-emugl/host/libs/libOpenglRender/vulkan-registry/xml/vk.xml cereal -o android/android-emugl/host/libs/libOpenglRender/vulkan/cereal
// Please do not modify directly;
// re-run android/scripts/generate-vulkan-sources.sh,
// or directly from Python by defining:
// VULKAN_REGISTRY_XML_DIR : Directory containing genvk.py and vk.xml
// CEREAL_OUTPUT_DIR: Where to put the generated sources.
// python3 $VULKAN_REGISTRY_XML_DIR/genvk.py -registry $VULKAN_REGISTRY_XML_DIR/vk.xml cereal -o $CEREAL_OUTPUT_DIR

#include "func_table.h"


#include "VkEncoder.h"
#include "HostConnection.h"
#include "ResourceTracker.h"

#include "goldfish_vk_private_defs.h"

#include <log/log.h>

// Stuff we are not going to use but if included,
// will cause compile errors. These are Android Vulkan
// required extensions, but the approach will be to
// implement them completely on the guest side.
#undef VK_KHR_android_surface


namespace goldfish_vk {

static void sOnInvalidDynamicallyCheckedCall(const char* apiname, const char* neededFeature)
{
    ALOGE("invalid call to %s: %s not supported", apiname, neededFeature);
    abort();
}
#ifdef VK_VERSION_1_0
static VkResult entry_vkCreateInstance(
    const VkInstanceCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkInstance* pInstance)
{
    AEMU_SCOPED_TRACE("vkCreateInstance");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateInstance_VkResult_return = (VkResult)0;
    vkCreateInstance_VkResult_return = vkEnc->vkCreateInstance(pCreateInfo, pAllocator, pInstance);
    return vkCreateInstance_VkResult_return;
}
static void entry_vkDestroyInstance(
    VkInstance instance,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyInstance");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyInstance(instance, pAllocator);
}
static VkResult entry_vkEnumeratePhysicalDevices(
    VkInstance instance,
    uint32_t* pPhysicalDeviceCount,
    VkPhysicalDevice* pPhysicalDevices)
{
    AEMU_SCOPED_TRACE("vkEnumeratePhysicalDevices");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkEnumeratePhysicalDevices_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkEnumeratePhysicalDevices_VkResult_return = resources->on_vkEnumeratePhysicalDevices(vkEnc, VK_SUCCESS, instance, pPhysicalDeviceCount, pPhysicalDevices);
    return vkEnumeratePhysicalDevices_VkResult_return;
}
static void entry_vkGetPhysicalDeviceFeatures(
    VkPhysicalDevice physicalDevice,
    VkPhysicalDeviceFeatures* pFeatures)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceFeatures");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceFeatures(physicalDevice, pFeatures);
}
static void entry_vkGetPhysicalDeviceFormatProperties(
    VkPhysicalDevice physicalDevice,
    VkFormat format,
    VkFormatProperties* pFormatProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceFormatProperties");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceFormatProperties(physicalDevice, format, pFormatProperties);
}
static VkResult entry_vkGetPhysicalDeviceImageFormatProperties(
    VkPhysicalDevice physicalDevice,
    VkFormat format,
    VkImageType type,
    VkImageTiling tiling,
    VkImageUsageFlags usage,
    VkImageCreateFlags flags,
    VkImageFormatProperties* pImageFormatProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceImageFormatProperties");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetPhysicalDeviceImageFormatProperties_VkResult_return = (VkResult)0;
    vkGetPhysicalDeviceImageFormatProperties_VkResult_return = vkEnc->vkGetPhysicalDeviceImageFormatProperties(physicalDevice, format, type, tiling, usage, flags, pImageFormatProperties);
    return vkGetPhysicalDeviceImageFormatProperties_VkResult_return;
}
static void entry_vkGetPhysicalDeviceProperties(
    VkPhysicalDevice physicalDevice,
    VkPhysicalDeviceProperties* pProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceProperties");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceProperties(physicalDevice, pProperties);
}
static void entry_vkGetPhysicalDeviceQueueFamilyProperties(
    VkPhysicalDevice physicalDevice,
    uint32_t* pQueueFamilyPropertyCount,
    VkQueueFamilyProperties* pQueueFamilyProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceQueueFamilyProperties");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceQueueFamilyProperties(physicalDevice, pQueueFamilyPropertyCount, pQueueFamilyProperties);
}
static void entry_vkGetPhysicalDeviceMemoryProperties(
    VkPhysicalDevice physicalDevice,
    VkPhysicalDeviceMemoryProperties* pMemoryProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceMemoryProperties");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceMemoryProperties(physicalDevice, pMemoryProperties);
}
static PFN_vkVoidFunction entry_vkGetInstanceProcAddr(
    VkInstance instance,
    const char* pName)
{
    AEMU_SCOPED_TRACE("vkGetInstanceProcAddr");
    auto vkEnc = HostConnection::get()->vkEncoder();
    PFN_vkVoidFunction vkGetInstanceProcAddr_PFN_vkVoidFunction_return = (PFN_vkVoidFunction)0;
    vkGetInstanceProcAddr_PFN_vkVoidFunction_return = vkEnc->vkGetInstanceProcAddr(instance, pName);
    return vkGetInstanceProcAddr_PFN_vkVoidFunction_return;
}
static PFN_vkVoidFunction entry_vkGetDeviceProcAddr(
    VkDevice device,
    const char* pName)
{
    AEMU_SCOPED_TRACE("vkGetDeviceProcAddr");
    auto vkEnc = HostConnection::get()->vkEncoder();
    PFN_vkVoidFunction vkGetDeviceProcAddr_PFN_vkVoidFunction_return = (PFN_vkVoidFunction)0;
    vkGetDeviceProcAddr_PFN_vkVoidFunction_return = vkEnc->vkGetDeviceProcAddr(device, pName);
    return vkGetDeviceProcAddr_PFN_vkVoidFunction_return;
}
static VkResult entry_vkCreateDevice(
    VkPhysicalDevice physicalDevice,
    const VkDeviceCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkDevice* pDevice)
{
    AEMU_SCOPED_TRACE("vkCreateDevice");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateDevice_VkResult_return = (VkResult)0;
    vkCreateDevice_VkResult_return = vkEnc->vkCreateDevice(physicalDevice, pCreateInfo, pAllocator, pDevice);
    return vkCreateDevice_VkResult_return;
}
static void entry_vkDestroyDevice(
    VkDevice device,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyDevice");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyDevice(device, pAllocator);
}
static VkResult entry_vkEnumerateInstanceExtensionProperties(
    const char* pLayerName,
    uint32_t* pPropertyCount,
    VkExtensionProperties* pProperties)
{
    AEMU_SCOPED_TRACE("vkEnumerateInstanceExtensionProperties");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkEnumerateInstanceExtensionProperties_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkEnumerateInstanceExtensionProperties_VkResult_return = resources->on_vkEnumerateInstanceExtensionProperties(vkEnc, VK_SUCCESS, pLayerName, pPropertyCount, pProperties);
    return vkEnumerateInstanceExtensionProperties_VkResult_return;
}
static VkResult entry_vkEnumerateDeviceExtensionProperties(
    VkPhysicalDevice physicalDevice,
    const char* pLayerName,
    uint32_t* pPropertyCount,
    VkExtensionProperties* pProperties)
{
    AEMU_SCOPED_TRACE("vkEnumerateDeviceExtensionProperties");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkEnumerateDeviceExtensionProperties_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkEnumerateDeviceExtensionProperties_VkResult_return = resources->on_vkEnumerateDeviceExtensionProperties(vkEnc, VK_SUCCESS, physicalDevice, pLayerName, pPropertyCount, pProperties);
    return vkEnumerateDeviceExtensionProperties_VkResult_return;
}
static VkResult entry_vkEnumerateInstanceLayerProperties(
    uint32_t* pPropertyCount,
    VkLayerProperties* pProperties)
{
    AEMU_SCOPED_TRACE("vkEnumerateInstanceLayerProperties");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkEnumerateInstanceLayerProperties_VkResult_return = (VkResult)0;
    vkEnumerateInstanceLayerProperties_VkResult_return = vkEnc->vkEnumerateInstanceLayerProperties(pPropertyCount, pProperties);
    return vkEnumerateInstanceLayerProperties_VkResult_return;
}
static VkResult entry_vkEnumerateDeviceLayerProperties(
    VkPhysicalDevice physicalDevice,
    uint32_t* pPropertyCount,
    VkLayerProperties* pProperties)
{
    AEMU_SCOPED_TRACE("vkEnumerateDeviceLayerProperties");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkEnumerateDeviceLayerProperties_VkResult_return = (VkResult)0;
    vkEnumerateDeviceLayerProperties_VkResult_return = vkEnc->vkEnumerateDeviceLayerProperties(physicalDevice, pPropertyCount, pProperties);
    return vkEnumerateDeviceLayerProperties_VkResult_return;
}
static void entry_vkGetDeviceQueue(
    VkDevice device,
    uint32_t queueFamilyIndex,
    uint32_t queueIndex,
    VkQueue* pQueue)
{
    AEMU_SCOPED_TRACE("vkGetDeviceQueue");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetDeviceQueue(device, queueFamilyIndex, queueIndex, pQueue);
}
static VkResult entry_vkQueueSubmit(
    VkQueue queue,
    uint32_t submitCount,
    const VkSubmitInfo* pSubmits,
    VkFence fence)
{
    AEMU_SCOPED_TRACE("vkQueueSubmit");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkQueueSubmit_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkQueueSubmit_VkResult_return = resources->on_vkQueueSubmit(vkEnc, VK_SUCCESS, queue, submitCount, pSubmits, fence);
    return vkQueueSubmit_VkResult_return;
}
static VkResult entry_vkQueueWaitIdle(
    VkQueue queue)
{
    AEMU_SCOPED_TRACE("vkQueueWaitIdle");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkQueueWaitIdle_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkQueueWaitIdle_VkResult_return = resources->on_vkQueueWaitIdle(vkEnc, VK_SUCCESS, queue);
    return vkQueueWaitIdle_VkResult_return;
}
static VkResult entry_vkDeviceWaitIdle(
    VkDevice device)
{
    AEMU_SCOPED_TRACE("vkDeviceWaitIdle");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkDeviceWaitIdle_VkResult_return = (VkResult)0;
    vkDeviceWaitIdle_VkResult_return = vkEnc->vkDeviceWaitIdle(device);
    return vkDeviceWaitIdle_VkResult_return;
}
static VkResult entry_vkAllocateMemory(
    VkDevice device,
    const VkMemoryAllocateInfo* pAllocateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkDeviceMemory* pMemory)
{
    AEMU_SCOPED_TRACE("vkAllocateMemory");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkAllocateMemory_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkAllocateMemory_VkResult_return = resources->on_vkAllocateMemory(vkEnc, VK_SUCCESS, device, pAllocateInfo, pAllocator, pMemory);
    return vkAllocateMemory_VkResult_return;
}
static void entry_vkFreeMemory(
    VkDevice device,
    VkDeviceMemory memory,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkFreeMemory");
    auto vkEnc = HostConnection::get()->vkEncoder();
    auto resources = ResourceTracker::get();
    resources->on_vkFreeMemory(vkEnc, device, memory, pAllocator);
}
static VkResult entry_vkMapMemory(
    VkDevice device,
    VkDeviceMemory memory,
    VkDeviceSize offset,
    VkDeviceSize size,
    VkMemoryMapFlags flags,
    void** ppData)
{
    AEMU_SCOPED_TRACE("vkMapMemory");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkMapMemory_VkResult_return = (VkResult)0;
    vkMapMemory_VkResult_return = vkEnc->vkMapMemory(device, memory, offset, size, flags, ppData);
    return vkMapMemory_VkResult_return;
}
static void entry_vkUnmapMemory(
    VkDevice device,
    VkDeviceMemory memory)
{
    AEMU_SCOPED_TRACE("vkUnmapMemory");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkUnmapMemory(device, memory);
}
static VkResult entry_vkFlushMappedMemoryRanges(
    VkDevice device,
    uint32_t memoryRangeCount,
    const VkMappedMemoryRange* pMemoryRanges)
{
    AEMU_SCOPED_TRACE("vkFlushMappedMemoryRanges");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkFlushMappedMemoryRanges_VkResult_return = (VkResult)0;
    vkFlushMappedMemoryRanges_VkResult_return = vkEnc->vkFlushMappedMemoryRanges(device, memoryRangeCount, pMemoryRanges);
    return vkFlushMappedMemoryRanges_VkResult_return;
}
static VkResult entry_vkInvalidateMappedMemoryRanges(
    VkDevice device,
    uint32_t memoryRangeCount,
    const VkMappedMemoryRange* pMemoryRanges)
{
    AEMU_SCOPED_TRACE("vkInvalidateMappedMemoryRanges");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkInvalidateMappedMemoryRanges_VkResult_return = (VkResult)0;
    vkInvalidateMappedMemoryRanges_VkResult_return = vkEnc->vkInvalidateMappedMemoryRanges(device, memoryRangeCount, pMemoryRanges);
    return vkInvalidateMappedMemoryRanges_VkResult_return;
}
static void entry_vkGetDeviceMemoryCommitment(
    VkDevice device,
    VkDeviceMemory memory,
    VkDeviceSize* pCommittedMemoryInBytes)
{
    AEMU_SCOPED_TRACE("vkGetDeviceMemoryCommitment");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetDeviceMemoryCommitment(device, memory, pCommittedMemoryInBytes);
}
static VkResult entry_vkBindBufferMemory(
    VkDevice device,
    VkBuffer buffer,
    VkDeviceMemory memory,
    VkDeviceSize memoryOffset)
{
    AEMU_SCOPED_TRACE("vkBindBufferMemory");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkBindBufferMemory_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkBindBufferMemory_VkResult_return = resources->on_vkBindBufferMemory(vkEnc, VK_SUCCESS, device, buffer, memory, memoryOffset);
    return vkBindBufferMemory_VkResult_return;
}
static VkResult entry_vkBindImageMemory(
    VkDevice device,
    VkImage image,
    VkDeviceMemory memory,
    VkDeviceSize memoryOffset)
{
    AEMU_SCOPED_TRACE("vkBindImageMemory");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkBindImageMemory_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkBindImageMemory_VkResult_return = resources->on_vkBindImageMemory(vkEnc, VK_SUCCESS, device, image, memory, memoryOffset);
    return vkBindImageMemory_VkResult_return;
}
static void entry_vkGetBufferMemoryRequirements(
    VkDevice device,
    VkBuffer buffer,
    VkMemoryRequirements* pMemoryRequirements)
{
    AEMU_SCOPED_TRACE("vkGetBufferMemoryRequirements");
    auto vkEnc = HostConnection::get()->vkEncoder();
    auto resources = ResourceTracker::get();
    resources->on_vkGetBufferMemoryRequirements(vkEnc, device, buffer, pMemoryRequirements);
}
static void entry_vkGetImageMemoryRequirements(
    VkDevice device,
    VkImage image,
    VkMemoryRequirements* pMemoryRequirements)
{
    AEMU_SCOPED_TRACE("vkGetImageMemoryRequirements");
    auto vkEnc = HostConnection::get()->vkEncoder();
    auto resources = ResourceTracker::get();
    resources->on_vkGetImageMemoryRequirements(vkEnc, device, image, pMemoryRequirements);
}
static void entry_vkGetImageSparseMemoryRequirements(
    VkDevice device,
    VkImage image,
    uint32_t* pSparseMemoryRequirementCount,
    VkSparseImageMemoryRequirements* pSparseMemoryRequirements)
{
    AEMU_SCOPED_TRACE("vkGetImageSparseMemoryRequirements");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetImageSparseMemoryRequirements(device, image, pSparseMemoryRequirementCount, pSparseMemoryRequirements);
}
static void entry_vkGetPhysicalDeviceSparseImageFormatProperties(
    VkPhysicalDevice physicalDevice,
    VkFormat format,
    VkImageType type,
    VkSampleCountFlagBits samples,
    VkImageUsageFlags usage,
    VkImageTiling tiling,
    uint32_t* pPropertyCount,
    VkSparseImageFormatProperties* pProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceSparseImageFormatProperties");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceSparseImageFormatProperties(physicalDevice, format, type, samples, usage, tiling, pPropertyCount, pProperties);
}
static VkResult entry_vkQueueBindSparse(
    VkQueue queue,
    uint32_t bindInfoCount,
    const VkBindSparseInfo* pBindInfo,
    VkFence fence)
{
    AEMU_SCOPED_TRACE("vkQueueBindSparse");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkQueueBindSparse_VkResult_return = (VkResult)0;
    vkQueueBindSparse_VkResult_return = vkEnc->vkQueueBindSparse(queue, bindInfoCount, pBindInfo, fence);
    return vkQueueBindSparse_VkResult_return;
}
static VkResult entry_vkCreateFence(
    VkDevice device,
    const VkFenceCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkFence* pFence)
{
    AEMU_SCOPED_TRACE("vkCreateFence");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateFence_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkCreateFence_VkResult_return = resources->on_vkCreateFence(vkEnc, VK_SUCCESS, device, pCreateInfo, pAllocator, pFence);
    return vkCreateFence_VkResult_return;
}
static void entry_vkDestroyFence(
    VkDevice device,
    VkFence fence,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyFence");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyFence(device, fence, pAllocator);
}
static VkResult entry_vkResetFences(
    VkDevice device,
    uint32_t fenceCount,
    const VkFence* pFences)
{
    AEMU_SCOPED_TRACE("vkResetFences");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkResetFences_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkResetFences_VkResult_return = resources->on_vkResetFences(vkEnc, VK_SUCCESS, device, fenceCount, pFences);
    return vkResetFences_VkResult_return;
}
static VkResult entry_vkGetFenceStatus(
    VkDevice device,
    VkFence fence)
{
    AEMU_SCOPED_TRACE("vkGetFenceStatus");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetFenceStatus_VkResult_return = (VkResult)0;
    vkGetFenceStatus_VkResult_return = vkEnc->vkGetFenceStatus(device, fence);
    return vkGetFenceStatus_VkResult_return;
}
static VkResult entry_vkWaitForFences(
    VkDevice device,
    uint32_t fenceCount,
    const VkFence* pFences,
    VkBool32 waitAll,
    uint64_t timeout)
{
    AEMU_SCOPED_TRACE("vkWaitForFences");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkWaitForFences_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkWaitForFences_VkResult_return = resources->on_vkWaitForFences(vkEnc, VK_SUCCESS, device, fenceCount, pFences, waitAll, timeout);
    return vkWaitForFences_VkResult_return;
}
static VkResult entry_vkCreateSemaphore(
    VkDevice device,
    const VkSemaphoreCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkSemaphore* pSemaphore)
{
    AEMU_SCOPED_TRACE("vkCreateSemaphore");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateSemaphore_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkCreateSemaphore_VkResult_return = resources->on_vkCreateSemaphore(vkEnc, VK_SUCCESS, device, pCreateInfo, pAllocator, pSemaphore);
    return vkCreateSemaphore_VkResult_return;
}
static void entry_vkDestroySemaphore(
    VkDevice device,
    VkSemaphore semaphore,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroySemaphore");
    auto vkEnc = HostConnection::get()->vkEncoder();
    auto resources = ResourceTracker::get();
    resources->on_vkDestroySemaphore(vkEnc, device, semaphore, pAllocator);
}
static VkResult entry_vkCreateEvent(
    VkDevice device,
    const VkEventCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkEvent* pEvent)
{
    AEMU_SCOPED_TRACE("vkCreateEvent");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateEvent_VkResult_return = (VkResult)0;
    vkCreateEvent_VkResult_return = vkEnc->vkCreateEvent(device, pCreateInfo, pAllocator, pEvent);
    return vkCreateEvent_VkResult_return;
}
static void entry_vkDestroyEvent(
    VkDevice device,
    VkEvent event,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyEvent");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyEvent(device, event, pAllocator);
}
static VkResult entry_vkGetEventStatus(
    VkDevice device,
    VkEvent event)
{
    AEMU_SCOPED_TRACE("vkGetEventStatus");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetEventStatus_VkResult_return = (VkResult)0;
    vkGetEventStatus_VkResult_return = vkEnc->vkGetEventStatus(device, event);
    return vkGetEventStatus_VkResult_return;
}
static VkResult entry_vkSetEvent(
    VkDevice device,
    VkEvent event)
{
    AEMU_SCOPED_TRACE("vkSetEvent");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkSetEvent_VkResult_return = (VkResult)0;
    vkSetEvent_VkResult_return = vkEnc->vkSetEvent(device, event);
    return vkSetEvent_VkResult_return;
}
static VkResult entry_vkResetEvent(
    VkDevice device,
    VkEvent event)
{
    AEMU_SCOPED_TRACE("vkResetEvent");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkResetEvent_VkResult_return = (VkResult)0;
    vkResetEvent_VkResult_return = vkEnc->vkResetEvent(device, event);
    return vkResetEvent_VkResult_return;
}
static VkResult entry_vkCreateQueryPool(
    VkDevice device,
    const VkQueryPoolCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkQueryPool* pQueryPool)
{
    AEMU_SCOPED_TRACE("vkCreateQueryPool");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateQueryPool_VkResult_return = (VkResult)0;
    vkCreateQueryPool_VkResult_return = vkEnc->vkCreateQueryPool(device, pCreateInfo, pAllocator, pQueryPool);
    return vkCreateQueryPool_VkResult_return;
}
static void entry_vkDestroyQueryPool(
    VkDevice device,
    VkQueryPool queryPool,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyQueryPool");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyQueryPool(device, queryPool, pAllocator);
}
static VkResult entry_vkGetQueryPoolResults(
    VkDevice device,
    VkQueryPool queryPool,
    uint32_t firstQuery,
    uint32_t queryCount,
    size_t dataSize,
    void* pData,
    VkDeviceSize stride,
    VkQueryResultFlags flags)
{
    AEMU_SCOPED_TRACE("vkGetQueryPoolResults");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetQueryPoolResults_VkResult_return = (VkResult)0;
    vkGetQueryPoolResults_VkResult_return = vkEnc->vkGetQueryPoolResults(device, queryPool, firstQuery, queryCount, dataSize, pData, stride, flags);
    return vkGetQueryPoolResults_VkResult_return;
}
static VkResult entry_vkCreateBuffer(
    VkDevice device,
    const VkBufferCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkBuffer* pBuffer)
{
    AEMU_SCOPED_TRACE("vkCreateBuffer");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateBuffer_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkCreateBuffer_VkResult_return = resources->on_vkCreateBuffer(vkEnc, VK_SUCCESS, device, pCreateInfo, pAllocator, pBuffer);
    return vkCreateBuffer_VkResult_return;
}
static void entry_vkDestroyBuffer(
    VkDevice device,
    VkBuffer buffer,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyBuffer");
    auto vkEnc = HostConnection::get()->vkEncoder();
    auto resources = ResourceTracker::get();
    resources->on_vkDestroyBuffer(vkEnc, device, buffer, pAllocator);
}
static VkResult entry_vkCreateBufferView(
    VkDevice device,
    const VkBufferViewCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkBufferView* pView)
{
    AEMU_SCOPED_TRACE("vkCreateBufferView");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateBufferView_VkResult_return = (VkResult)0;
    vkCreateBufferView_VkResult_return = vkEnc->vkCreateBufferView(device, pCreateInfo, pAllocator, pView);
    return vkCreateBufferView_VkResult_return;
}
static void entry_vkDestroyBufferView(
    VkDevice device,
    VkBufferView bufferView,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyBufferView");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyBufferView(device, bufferView, pAllocator);
}
static VkResult entry_vkCreateImage(
    VkDevice device,
    const VkImageCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkImage* pImage)
{
    AEMU_SCOPED_TRACE("vkCreateImage");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateImage_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkCreateImage_VkResult_return = resources->on_vkCreateImage(vkEnc, VK_SUCCESS, device, pCreateInfo, pAllocator, pImage);
    return vkCreateImage_VkResult_return;
}
static void entry_vkDestroyImage(
    VkDevice device,
    VkImage image,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyImage");
    auto vkEnc = HostConnection::get()->vkEncoder();
    auto resources = ResourceTracker::get();
    resources->on_vkDestroyImage(vkEnc, device, image, pAllocator);
}
static void entry_vkGetImageSubresourceLayout(
    VkDevice device,
    VkImage image,
    const VkImageSubresource* pSubresource,
    VkSubresourceLayout* pLayout)
{
    AEMU_SCOPED_TRACE("vkGetImageSubresourceLayout");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetImageSubresourceLayout(device, image, pSubresource, pLayout);
}
static VkResult entry_vkCreateImageView(
    VkDevice device,
    const VkImageViewCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkImageView* pView)
{
    AEMU_SCOPED_TRACE("vkCreateImageView");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateImageView_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkCreateImageView_VkResult_return = resources->on_vkCreateImageView(vkEnc, VK_SUCCESS, device, pCreateInfo, pAllocator, pView);
    return vkCreateImageView_VkResult_return;
}
static void entry_vkDestroyImageView(
    VkDevice device,
    VkImageView imageView,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyImageView");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyImageView(device, imageView, pAllocator);
}
static VkResult entry_vkCreateShaderModule(
    VkDevice device,
    const VkShaderModuleCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkShaderModule* pShaderModule)
{
    AEMU_SCOPED_TRACE("vkCreateShaderModule");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateShaderModule_VkResult_return = (VkResult)0;
    vkCreateShaderModule_VkResult_return = vkEnc->vkCreateShaderModule(device, pCreateInfo, pAllocator, pShaderModule);
    return vkCreateShaderModule_VkResult_return;
}
static void entry_vkDestroyShaderModule(
    VkDevice device,
    VkShaderModule shaderModule,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyShaderModule");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyShaderModule(device, shaderModule, pAllocator);
}
static VkResult entry_vkCreatePipelineCache(
    VkDevice device,
    const VkPipelineCacheCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkPipelineCache* pPipelineCache)
{
    AEMU_SCOPED_TRACE("vkCreatePipelineCache");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreatePipelineCache_VkResult_return = (VkResult)0;
    vkCreatePipelineCache_VkResult_return = vkEnc->vkCreatePipelineCache(device, pCreateInfo, pAllocator, pPipelineCache);
    return vkCreatePipelineCache_VkResult_return;
}
static void entry_vkDestroyPipelineCache(
    VkDevice device,
    VkPipelineCache pipelineCache,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyPipelineCache");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyPipelineCache(device, pipelineCache, pAllocator);
}
static VkResult entry_vkGetPipelineCacheData(
    VkDevice device,
    VkPipelineCache pipelineCache,
    size_t* pDataSize,
    void* pData)
{
    AEMU_SCOPED_TRACE("vkGetPipelineCacheData");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetPipelineCacheData_VkResult_return = (VkResult)0;
    vkGetPipelineCacheData_VkResult_return = vkEnc->vkGetPipelineCacheData(device, pipelineCache, pDataSize, pData);
    return vkGetPipelineCacheData_VkResult_return;
}
static VkResult entry_vkMergePipelineCaches(
    VkDevice device,
    VkPipelineCache dstCache,
    uint32_t srcCacheCount,
    const VkPipelineCache* pSrcCaches)
{
    AEMU_SCOPED_TRACE("vkMergePipelineCaches");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkMergePipelineCaches_VkResult_return = (VkResult)0;
    vkMergePipelineCaches_VkResult_return = vkEnc->vkMergePipelineCaches(device, dstCache, srcCacheCount, pSrcCaches);
    return vkMergePipelineCaches_VkResult_return;
}
static VkResult entry_vkCreateGraphicsPipelines(
    VkDevice device,
    VkPipelineCache pipelineCache,
    uint32_t createInfoCount,
    const VkGraphicsPipelineCreateInfo* pCreateInfos,
    const VkAllocationCallbacks* pAllocator,
    VkPipeline* pPipelines)
{
    AEMU_SCOPED_TRACE("vkCreateGraphicsPipelines");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateGraphicsPipelines_VkResult_return = (VkResult)0;
    vkCreateGraphicsPipelines_VkResult_return = vkEnc->vkCreateGraphicsPipelines(device, pipelineCache, createInfoCount, pCreateInfos, pAllocator, pPipelines);
    return vkCreateGraphicsPipelines_VkResult_return;
}
static VkResult entry_vkCreateComputePipelines(
    VkDevice device,
    VkPipelineCache pipelineCache,
    uint32_t createInfoCount,
    const VkComputePipelineCreateInfo* pCreateInfos,
    const VkAllocationCallbacks* pAllocator,
    VkPipeline* pPipelines)
{
    AEMU_SCOPED_TRACE("vkCreateComputePipelines");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateComputePipelines_VkResult_return = (VkResult)0;
    vkCreateComputePipelines_VkResult_return = vkEnc->vkCreateComputePipelines(device, pipelineCache, createInfoCount, pCreateInfos, pAllocator, pPipelines);
    return vkCreateComputePipelines_VkResult_return;
}
static void entry_vkDestroyPipeline(
    VkDevice device,
    VkPipeline pipeline,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyPipeline");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyPipeline(device, pipeline, pAllocator);
}
static VkResult entry_vkCreatePipelineLayout(
    VkDevice device,
    const VkPipelineLayoutCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkPipelineLayout* pPipelineLayout)
{
    AEMU_SCOPED_TRACE("vkCreatePipelineLayout");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreatePipelineLayout_VkResult_return = (VkResult)0;
    vkCreatePipelineLayout_VkResult_return = vkEnc->vkCreatePipelineLayout(device, pCreateInfo, pAllocator, pPipelineLayout);
    return vkCreatePipelineLayout_VkResult_return;
}
static void entry_vkDestroyPipelineLayout(
    VkDevice device,
    VkPipelineLayout pipelineLayout,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyPipelineLayout");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyPipelineLayout(device, pipelineLayout, pAllocator);
}
static VkResult entry_vkCreateSampler(
    VkDevice device,
    const VkSamplerCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkSampler* pSampler)
{
    AEMU_SCOPED_TRACE("vkCreateSampler");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateSampler_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkCreateSampler_VkResult_return = resources->on_vkCreateSampler(vkEnc, VK_SUCCESS, device, pCreateInfo, pAllocator, pSampler);
    return vkCreateSampler_VkResult_return;
}
static void entry_vkDestroySampler(
    VkDevice device,
    VkSampler sampler,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroySampler");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroySampler(device, sampler, pAllocator);
}
static VkResult entry_vkCreateDescriptorSetLayout(
    VkDevice device,
    const VkDescriptorSetLayoutCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkDescriptorSetLayout* pSetLayout)
{
    AEMU_SCOPED_TRACE("vkCreateDescriptorSetLayout");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateDescriptorSetLayout_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkCreateDescriptorSetLayout_VkResult_return = resources->on_vkCreateDescriptorSetLayout(vkEnc, VK_SUCCESS, device, pCreateInfo, pAllocator, pSetLayout);
    return vkCreateDescriptorSetLayout_VkResult_return;
}
static void entry_vkDestroyDescriptorSetLayout(
    VkDevice device,
    VkDescriptorSetLayout descriptorSetLayout,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyDescriptorSetLayout");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyDescriptorSetLayout(device, descriptorSetLayout, pAllocator);
}
static VkResult entry_vkCreateDescriptorPool(
    VkDevice device,
    const VkDescriptorPoolCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkDescriptorPool* pDescriptorPool)
{
    AEMU_SCOPED_TRACE("vkCreateDescriptorPool");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateDescriptorPool_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkCreateDescriptorPool_VkResult_return = resources->on_vkCreateDescriptorPool(vkEnc, VK_SUCCESS, device, pCreateInfo, pAllocator, pDescriptorPool);
    return vkCreateDescriptorPool_VkResult_return;
}
static void entry_vkDestroyDescriptorPool(
    VkDevice device,
    VkDescriptorPool descriptorPool,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyDescriptorPool");
    auto vkEnc = HostConnection::get()->vkEncoder();
    auto resources = ResourceTracker::get();
    resources->on_vkDestroyDescriptorPool(vkEnc, device, descriptorPool, pAllocator);
}
static VkResult entry_vkResetDescriptorPool(
    VkDevice device,
    VkDescriptorPool descriptorPool,
    VkDescriptorPoolResetFlags flags)
{
    AEMU_SCOPED_TRACE("vkResetDescriptorPool");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkResetDescriptorPool_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkResetDescriptorPool_VkResult_return = resources->on_vkResetDescriptorPool(vkEnc, VK_SUCCESS, device, descriptorPool, flags);
    return vkResetDescriptorPool_VkResult_return;
}
static VkResult entry_vkAllocateDescriptorSets(
    VkDevice device,
    const VkDescriptorSetAllocateInfo* pAllocateInfo,
    VkDescriptorSet* pDescriptorSets)
{
    AEMU_SCOPED_TRACE("vkAllocateDescriptorSets");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkAllocateDescriptorSets_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkAllocateDescriptorSets_VkResult_return = resources->on_vkAllocateDescriptorSets(vkEnc, VK_SUCCESS, device, pAllocateInfo, pDescriptorSets);
    return vkAllocateDescriptorSets_VkResult_return;
}
static VkResult entry_vkFreeDescriptorSets(
    VkDevice device,
    VkDescriptorPool descriptorPool,
    uint32_t descriptorSetCount,
    const VkDescriptorSet* pDescriptorSets)
{
    AEMU_SCOPED_TRACE("vkFreeDescriptorSets");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkFreeDescriptorSets_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkFreeDescriptorSets_VkResult_return = resources->on_vkFreeDescriptorSets(vkEnc, VK_SUCCESS, device, descriptorPool, descriptorSetCount, pDescriptorSets);
    return vkFreeDescriptorSets_VkResult_return;
}
static void entry_vkUpdateDescriptorSets(
    VkDevice device,
    uint32_t descriptorWriteCount,
    const VkWriteDescriptorSet* pDescriptorWrites,
    uint32_t descriptorCopyCount,
    const VkCopyDescriptorSet* pDescriptorCopies)
{
    AEMU_SCOPED_TRACE("vkUpdateDescriptorSets");
    auto vkEnc = HostConnection::get()->vkEncoder();
    auto resources = ResourceTracker::get();
    resources->on_vkUpdateDescriptorSets(vkEnc, device, descriptorWriteCount, pDescriptorWrites, descriptorCopyCount, pDescriptorCopies);
}
static VkResult entry_vkCreateFramebuffer(
    VkDevice device,
    const VkFramebufferCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkFramebuffer* pFramebuffer)
{
    AEMU_SCOPED_TRACE("vkCreateFramebuffer");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateFramebuffer_VkResult_return = (VkResult)0;
    vkCreateFramebuffer_VkResult_return = vkEnc->vkCreateFramebuffer(device, pCreateInfo, pAllocator, pFramebuffer);
    return vkCreateFramebuffer_VkResult_return;
}
static void entry_vkDestroyFramebuffer(
    VkDevice device,
    VkFramebuffer framebuffer,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyFramebuffer");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyFramebuffer(device, framebuffer, pAllocator);
}
static VkResult entry_vkCreateRenderPass(
    VkDevice device,
    const VkRenderPassCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkRenderPass* pRenderPass)
{
    AEMU_SCOPED_TRACE("vkCreateRenderPass");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateRenderPass_VkResult_return = (VkResult)0;
    vkCreateRenderPass_VkResult_return = vkEnc->vkCreateRenderPass(device, pCreateInfo, pAllocator, pRenderPass);
    return vkCreateRenderPass_VkResult_return;
}
static void entry_vkDestroyRenderPass(
    VkDevice device,
    VkRenderPass renderPass,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyRenderPass");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyRenderPass(device, renderPass, pAllocator);
}
static void entry_vkGetRenderAreaGranularity(
    VkDevice device,
    VkRenderPass renderPass,
    VkExtent2D* pGranularity)
{
    AEMU_SCOPED_TRACE("vkGetRenderAreaGranularity");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetRenderAreaGranularity(device, renderPass, pGranularity);
}
static VkResult entry_vkCreateCommandPool(
    VkDevice device,
    const VkCommandPoolCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkCommandPool* pCommandPool)
{
    AEMU_SCOPED_TRACE("vkCreateCommandPool");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateCommandPool_VkResult_return = (VkResult)0;
    vkCreateCommandPool_VkResult_return = vkEnc->vkCreateCommandPool(device, pCreateInfo, pAllocator, pCommandPool);
    return vkCreateCommandPool_VkResult_return;
}
static void entry_vkDestroyCommandPool(
    VkDevice device,
    VkCommandPool commandPool,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyCommandPool");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyCommandPool(device, commandPool, pAllocator);
}
static VkResult entry_vkResetCommandPool(
    VkDevice device,
    VkCommandPool commandPool,
    VkCommandPoolResetFlags flags)
{
    AEMU_SCOPED_TRACE("vkResetCommandPool");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkResetCommandPool_VkResult_return = (VkResult)0;
    vkResetCommandPool_VkResult_return = vkEnc->vkResetCommandPool(device, commandPool, flags);
    return vkResetCommandPool_VkResult_return;
}
static VkResult entry_vkAllocateCommandBuffers(
    VkDevice device,
    const VkCommandBufferAllocateInfo* pAllocateInfo,
    VkCommandBuffer* pCommandBuffers)
{
    AEMU_SCOPED_TRACE("vkAllocateCommandBuffers");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkAllocateCommandBuffers_VkResult_return = (VkResult)0;
    vkAllocateCommandBuffers_VkResult_return = vkEnc->vkAllocateCommandBuffers(device, pAllocateInfo, pCommandBuffers);
    return vkAllocateCommandBuffers_VkResult_return;
}
static void entry_vkFreeCommandBuffers(
    VkDevice device,
    VkCommandPool commandPool,
    uint32_t commandBufferCount,
    const VkCommandBuffer* pCommandBuffers)
{
    AEMU_SCOPED_TRACE("vkFreeCommandBuffers");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkFreeCommandBuffers(device, commandPool, commandBufferCount, pCommandBuffers);
}
static VkResult entry_vkBeginCommandBuffer(
    VkCommandBuffer commandBuffer,
    const VkCommandBufferBeginInfo* pBeginInfo)
{
    AEMU_SCOPED_TRACE("vkBeginCommandBuffer");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    VkResult vkBeginCommandBuffer_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkBeginCommandBuffer_VkResult_return = resources->on_vkBeginCommandBuffer(vkEnc, VK_SUCCESS, commandBuffer, pBeginInfo);
    return vkBeginCommandBuffer_VkResult_return;
}
static VkResult entry_vkEndCommandBuffer(
    VkCommandBuffer commandBuffer)
{
    AEMU_SCOPED_TRACE("vkEndCommandBuffer");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    VkResult vkEndCommandBuffer_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkEndCommandBuffer_VkResult_return = resources->on_vkEndCommandBuffer(vkEnc, VK_SUCCESS, commandBuffer);
    return vkEndCommandBuffer_VkResult_return;
}
static VkResult entry_vkResetCommandBuffer(
    VkCommandBuffer commandBuffer,
    VkCommandBufferResetFlags flags)
{
    AEMU_SCOPED_TRACE("vkResetCommandBuffer");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    VkResult vkResetCommandBuffer_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkResetCommandBuffer_VkResult_return = resources->on_vkResetCommandBuffer(vkEnc, VK_SUCCESS, commandBuffer, flags);
    return vkResetCommandBuffer_VkResult_return;
}
static void entry_vkCmdBindPipeline(
    VkCommandBuffer commandBuffer,
    VkPipelineBindPoint pipelineBindPoint,
    VkPipeline pipeline)
{
    AEMU_SCOPED_TRACE("vkCmdBindPipeline");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdBindPipeline(commandBuffer, pipelineBindPoint, pipeline);
}
static void entry_vkCmdSetViewport(
    VkCommandBuffer commandBuffer,
    uint32_t firstViewport,
    uint32_t viewportCount,
    const VkViewport* pViewports)
{
    AEMU_SCOPED_TRACE("vkCmdSetViewport");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdSetViewport(commandBuffer, firstViewport, viewportCount, pViewports);
}
static void entry_vkCmdSetScissor(
    VkCommandBuffer commandBuffer,
    uint32_t firstScissor,
    uint32_t scissorCount,
    const VkRect2D* pScissors)
{
    AEMU_SCOPED_TRACE("vkCmdSetScissor");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdSetScissor(commandBuffer, firstScissor, scissorCount, pScissors);
}
static void entry_vkCmdSetLineWidth(
    VkCommandBuffer commandBuffer,
    float lineWidth)
{
    AEMU_SCOPED_TRACE("vkCmdSetLineWidth");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdSetLineWidth(commandBuffer, lineWidth);
}
static void entry_vkCmdSetDepthBias(
    VkCommandBuffer commandBuffer,
    float depthBiasConstantFactor,
    float depthBiasClamp,
    float depthBiasSlopeFactor)
{
    AEMU_SCOPED_TRACE("vkCmdSetDepthBias");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdSetDepthBias(commandBuffer, depthBiasConstantFactor, depthBiasClamp, depthBiasSlopeFactor);
}
static void entry_vkCmdSetBlendConstants(
    VkCommandBuffer commandBuffer,
    const float blendConstants[4])
{
    AEMU_SCOPED_TRACE("vkCmdSetBlendConstants");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdSetBlendConstants(commandBuffer, blendConstants);
}
static void entry_vkCmdSetDepthBounds(
    VkCommandBuffer commandBuffer,
    float minDepthBounds,
    float maxDepthBounds)
{
    AEMU_SCOPED_TRACE("vkCmdSetDepthBounds");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdSetDepthBounds(commandBuffer, minDepthBounds, maxDepthBounds);
}
static void entry_vkCmdSetStencilCompareMask(
    VkCommandBuffer commandBuffer,
    VkStencilFaceFlags faceMask,
    uint32_t compareMask)
{
    AEMU_SCOPED_TRACE("vkCmdSetStencilCompareMask");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdSetStencilCompareMask(commandBuffer, faceMask, compareMask);
}
static void entry_vkCmdSetStencilWriteMask(
    VkCommandBuffer commandBuffer,
    VkStencilFaceFlags faceMask,
    uint32_t writeMask)
{
    AEMU_SCOPED_TRACE("vkCmdSetStencilWriteMask");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdSetStencilWriteMask(commandBuffer, faceMask, writeMask);
}
static void entry_vkCmdSetStencilReference(
    VkCommandBuffer commandBuffer,
    VkStencilFaceFlags faceMask,
    uint32_t reference)
{
    AEMU_SCOPED_TRACE("vkCmdSetStencilReference");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdSetStencilReference(commandBuffer, faceMask, reference);
}
static void entry_vkCmdBindDescriptorSets(
    VkCommandBuffer commandBuffer,
    VkPipelineBindPoint pipelineBindPoint,
    VkPipelineLayout layout,
    uint32_t firstSet,
    uint32_t descriptorSetCount,
    const VkDescriptorSet* pDescriptorSets,
    uint32_t dynamicOffsetCount,
    const uint32_t* pDynamicOffsets)
{
    AEMU_SCOPED_TRACE("vkCmdBindDescriptorSets");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdBindDescriptorSets(commandBuffer, pipelineBindPoint, layout, firstSet, descriptorSetCount, pDescriptorSets, dynamicOffsetCount, pDynamicOffsets);
}
static void entry_vkCmdBindIndexBuffer(
    VkCommandBuffer commandBuffer,
    VkBuffer buffer,
    VkDeviceSize offset,
    VkIndexType indexType)
{
    AEMU_SCOPED_TRACE("vkCmdBindIndexBuffer");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdBindIndexBuffer(commandBuffer, buffer, offset, indexType);
}
static void entry_vkCmdBindVertexBuffers(
    VkCommandBuffer commandBuffer,
    uint32_t firstBinding,
    uint32_t bindingCount,
    const VkBuffer* pBuffers,
    const VkDeviceSize* pOffsets)
{
    AEMU_SCOPED_TRACE("vkCmdBindVertexBuffers");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdBindVertexBuffers(commandBuffer, firstBinding, bindingCount, pBuffers, pOffsets);
}
static void entry_vkCmdDraw(
    VkCommandBuffer commandBuffer,
    uint32_t vertexCount,
    uint32_t instanceCount,
    uint32_t firstVertex,
    uint32_t firstInstance)
{
    AEMU_SCOPED_TRACE("vkCmdDraw");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdDraw(commandBuffer, vertexCount, instanceCount, firstVertex, firstInstance);
}
static void entry_vkCmdDrawIndexed(
    VkCommandBuffer commandBuffer,
    uint32_t indexCount,
    uint32_t instanceCount,
    uint32_t firstIndex,
    int32_t vertexOffset,
    uint32_t firstInstance)
{
    AEMU_SCOPED_TRACE("vkCmdDrawIndexed");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdDrawIndexed(commandBuffer, indexCount, instanceCount, firstIndex, vertexOffset, firstInstance);
}
static void entry_vkCmdDrawIndirect(
    VkCommandBuffer commandBuffer,
    VkBuffer buffer,
    VkDeviceSize offset,
    uint32_t drawCount,
    uint32_t stride)
{
    AEMU_SCOPED_TRACE("vkCmdDrawIndirect");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdDrawIndirect(commandBuffer, buffer, offset, drawCount, stride);
}
static void entry_vkCmdDrawIndexedIndirect(
    VkCommandBuffer commandBuffer,
    VkBuffer buffer,
    VkDeviceSize offset,
    uint32_t drawCount,
    uint32_t stride)
{
    AEMU_SCOPED_TRACE("vkCmdDrawIndexedIndirect");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdDrawIndexedIndirect(commandBuffer, buffer, offset, drawCount, stride);
}
static void entry_vkCmdDispatch(
    VkCommandBuffer commandBuffer,
    uint32_t groupCountX,
    uint32_t groupCountY,
    uint32_t groupCountZ)
{
    AEMU_SCOPED_TRACE("vkCmdDispatch");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdDispatch(commandBuffer, groupCountX, groupCountY, groupCountZ);
}
static void entry_vkCmdDispatchIndirect(
    VkCommandBuffer commandBuffer,
    VkBuffer buffer,
    VkDeviceSize offset)
{
    AEMU_SCOPED_TRACE("vkCmdDispatchIndirect");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdDispatchIndirect(commandBuffer, buffer, offset);
}
static void entry_vkCmdCopyBuffer(
    VkCommandBuffer commandBuffer,
    VkBuffer srcBuffer,
    VkBuffer dstBuffer,
    uint32_t regionCount,
    const VkBufferCopy* pRegions)
{
    AEMU_SCOPED_TRACE("vkCmdCopyBuffer");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdCopyBuffer(commandBuffer, srcBuffer, dstBuffer, regionCount, pRegions);
}
static void entry_vkCmdCopyImage(
    VkCommandBuffer commandBuffer,
    VkImage srcImage,
    VkImageLayout srcImageLayout,
    VkImage dstImage,
    VkImageLayout dstImageLayout,
    uint32_t regionCount,
    const VkImageCopy* pRegions)
{
    AEMU_SCOPED_TRACE("vkCmdCopyImage");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdCopyImage(commandBuffer, srcImage, srcImageLayout, dstImage, dstImageLayout, regionCount, pRegions);
}
static void entry_vkCmdBlitImage(
    VkCommandBuffer commandBuffer,
    VkImage srcImage,
    VkImageLayout srcImageLayout,
    VkImage dstImage,
    VkImageLayout dstImageLayout,
    uint32_t regionCount,
    const VkImageBlit* pRegions,
    VkFilter filter)
{
    AEMU_SCOPED_TRACE("vkCmdBlitImage");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdBlitImage(commandBuffer, srcImage, srcImageLayout, dstImage, dstImageLayout, regionCount, pRegions, filter);
}
static void entry_vkCmdCopyBufferToImage(
    VkCommandBuffer commandBuffer,
    VkBuffer srcBuffer,
    VkImage dstImage,
    VkImageLayout dstImageLayout,
    uint32_t regionCount,
    const VkBufferImageCopy* pRegions)
{
    AEMU_SCOPED_TRACE("vkCmdCopyBufferToImage");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdCopyBufferToImage(commandBuffer, srcBuffer, dstImage, dstImageLayout, regionCount, pRegions);
}
static void entry_vkCmdCopyImageToBuffer(
    VkCommandBuffer commandBuffer,
    VkImage srcImage,
    VkImageLayout srcImageLayout,
    VkBuffer dstBuffer,
    uint32_t regionCount,
    const VkBufferImageCopy* pRegions)
{
    AEMU_SCOPED_TRACE("vkCmdCopyImageToBuffer");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdCopyImageToBuffer(commandBuffer, srcImage, srcImageLayout, dstBuffer, regionCount, pRegions);
}
static void entry_vkCmdUpdateBuffer(
    VkCommandBuffer commandBuffer,
    VkBuffer dstBuffer,
    VkDeviceSize dstOffset,
    VkDeviceSize dataSize,
    const void* pData)
{
    AEMU_SCOPED_TRACE("vkCmdUpdateBuffer");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdUpdateBuffer(commandBuffer, dstBuffer, dstOffset, dataSize, pData);
}
static void entry_vkCmdFillBuffer(
    VkCommandBuffer commandBuffer,
    VkBuffer dstBuffer,
    VkDeviceSize dstOffset,
    VkDeviceSize size,
    uint32_t data)
{
    AEMU_SCOPED_TRACE("vkCmdFillBuffer");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdFillBuffer(commandBuffer, dstBuffer, dstOffset, size, data);
}
static void entry_vkCmdClearColorImage(
    VkCommandBuffer commandBuffer,
    VkImage image,
    VkImageLayout imageLayout,
    const VkClearColorValue* pColor,
    uint32_t rangeCount,
    const VkImageSubresourceRange* pRanges)
{
    AEMU_SCOPED_TRACE("vkCmdClearColorImage");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdClearColorImage(commandBuffer, image, imageLayout, pColor, rangeCount, pRanges);
}
static void entry_vkCmdClearDepthStencilImage(
    VkCommandBuffer commandBuffer,
    VkImage image,
    VkImageLayout imageLayout,
    const VkClearDepthStencilValue* pDepthStencil,
    uint32_t rangeCount,
    const VkImageSubresourceRange* pRanges)
{
    AEMU_SCOPED_TRACE("vkCmdClearDepthStencilImage");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdClearDepthStencilImage(commandBuffer, image, imageLayout, pDepthStencil, rangeCount, pRanges);
}
static void entry_vkCmdClearAttachments(
    VkCommandBuffer commandBuffer,
    uint32_t attachmentCount,
    const VkClearAttachment* pAttachments,
    uint32_t rectCount,
    const VkClearRect* pRects)
{
    AEMU_SCOPED_TRACE("vkCmdClearAttachments");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdClearAttachments(commandBuffer, attachmentCount, pAttachments, rectCount, pRects);
}
static void entry_vkCmdResolveImage(
    VkCommandBuffer commandBuffer,
    VkImage srcImage,
    VkImageLayout srcImageLayout,
    VkImage dstImage,
    VkImageLayout dstImageLayout,
    uint32_t regionCount,
    const VkImageResolve* pRegions)
{
    AEMU_SCOPED_TRACE("vkCmdResolveImage");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdResolveImage(commandBuffer, srcImage, srcImageLayout, dstImage, dstImageLayout, regionCount, pRegions);
}
static void entry_vkCmdSetEvent(
    VkCommandBuffer commandBuffer,
    VkEvent event,
    VkPipelineStageFlags stageMask)
{
    AEMU_SCOPED_TRACE("vkCmdSetEvent");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdSetEvent(commandBuffer, event, stageMask);
}
static void entry_vkCmdResetEvent(
    VkCommandBuffer commandBuffer,
    VkEvent event,
    VkPipelineStageFlags stageMask)
{
    AEMU_SCOPED_TRACE("vkCmdResetEvent");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdResetEvent(commandBuffer, event, stageMask);
}
static void entry_vkCmdWaitEvents(
    VkCommandBuffer commandBuffer,
    uint32_t eventCount,
    const VkEvent* pEvents,
    VkPipelineStageFlags srcStageMask,
    VkPipelineStageFlags dstStageMask,
    uint32_t memoryBarrierCount,
    const VkMemoryBarrier* pMemoryBarriers,
    uint32_t bufferMemoryBarrierCount,
    const VkBufferMemoryBarrier* pBufferMemoryBarriers,
    uint32_t imageMemoryBarrierCount,
    const VkImageMemoryBarrier* pImageMemoryBarriers)
{
    AEMU_SCOPED_TRACE("vkCmdWaitEvents");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdWaitEvents(commandBuffer, eventCount, pEvents, srcStageMask, dstStageMask, memoryBarrierCount, pMemoryBarriers, bufferMemoryBarrierCount, pBufferMemoryBarriers, imageMemoryBarrierCount, pImageMemoryBarriers);
}
static void entry_vkCmdPipelineBarrier(
    VkCommandBuffer commandBuffer,
    VkPipelineStageFlags srcStageMask,
    VkPipelineStageFlags dstStageMask,
    VkDependencyFlags dependencyFlags,
    uint32_t memoryBarrierCount,
    const VkMemoryBarrier* pMemoryBarriers,
    uint32_t bufferMemoryBarrierCount,
    const VkBufferMemoryBarrier* pBufferMemoryBarriers,
    uint32_t imageMemoryBarrierCount,
    const VkImageMemoryBarrier* pImageMemoryBarriers)
{
    AEMU_SCOPED_TRACE("vkCmdPipelineBarrier");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdPipelineBarrier(commandBuffer, srcStageMask, dstStageMask, dependencyFlags, memoryBarrierCount, pMemoryBarriers, bufferMemoryBarrierCount, pBufferMemoryBarriers, imageMemoryBarrierCount, pImageMemoryBarriers);
}
static void entry_vkCmdBeginQuery(
    VkCommandBuffer commandBuffer,
    VkQueryPool queryPool,
    uint32_t query,
    VkQueryControlFlags flags)
{
    AEMU_SCOPED_TRACE("vkCmdBeginQuery");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdBeginQuery(commandBuffer, queryPool, query, flags);
}
static void entry_vkCmdEndQuery(
    VkCommandBuffer commandBuffer,
    VkQueryPool queryPool,
    uint32_t query)
{
    AEMU_SCOPED_TRACE("vkCmdEndQuery");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdEndQuery(commandBuffer, queryPool, query);
}
static void entry_vkCmdResetQueryPool(
    VkCommandBuffer commandBuffer,
    VkQueryPool queryPool,
    uint32_t firstQuery,
    uint32_t queryCount)
{
    AEMU_SCOPED_TRACE("vkCmdResetQueryPool");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdResetQueryPool(commandBuffer, queryPool, firstQuery, queryCount);
}
static void entry_vkCmdWriteTimestamp(
    VkCommandBuffer commandBuffer,
    VkPipelineStageFlagBits pipelineStage,
    VkQueryPool queryPool,
    uint32_t query)
{
    AEMU_SCOPED_TRACE("vkCmdWriteTimestamp");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdWriteTimestamp(commandBuffer, pipelineStage, queryPool, query);
}
static void entry_vkCmdCopyQueryPoolResults(
    VkCommandBuffer commandBuffer,
    VkQueryPool queryPool,
    uint32_t firstQuery,
    uint32_t queryCount,
    VkBuffer dstBuffer,
    VkDeviceSize dstOffset,
    VkDeviceSize stride,
    VkQueryResultFlags flags)
{
    AEMU_SCOPED_TRACE("vkCmdCopyQueryPoolResults");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdCopyQueryPoolResults(commandBuffer, queryPool, firstQuery, queryCount, dstBuffer, dstOffset, stride, flags);
}
static void entry_vkCmdPushConstants(
    VkCommandBuffer commandBuffer,
    VkPipelineLayout layout,
    VkShaderStageFlags stageFlags,
    uint32_t offset,
    uint32_t size,
    const void* pValues)
{
    AEMU_SCOPED_TRACE("vkCmdPushConstants");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdPushConstants(commandBuffer, layout, stageFlags, offset, size, pValues);
}
static void entry_vkCmdBeginRenderPass(
    VkCommandBuffer commandBuffer,
    const VkRenderPassBeginInfo* pRenderPassBegin,
    VkSubpassContents contents)
{
    AEMU_SCOPED_TRACE("vkCmdBeginRenderPass");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdBeginRenderPass(commandBuffer, pRenderPassBegin, contents);
}
static void entry_vkCmdNextSubpass(
    VkCommandBuffer commandBuffer,
    VkSubpassContents contents)
{
    AEMU_SCOPED_TRACE("vkCmdNextSubpass");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdNextSubpass(commandBuffer, contents);
}
static void entry_vkCmdEndRenderPass(
    VkCommandBuffer commandBuffer)
{
    AEMU_SCOPED_TRACE("vkCmdEndRenderPass");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdEndRenderPass(commandBuffer);
}
static void entry_vkCmdExecuteCommands(
    VkCommandBuffer commandBuffer,
    uint32_t commandBufferCount,
    const VkCommandBuffer* pCommandBuffers)
{
    AEMU_SCOPED_TRACE("vkCmdExecuteCommands");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdExecuteCommands(commandBuffer, commandBufferCount, pCommandBuffers);
}
#endif
#ifdef VK_VERSION_1_1
static VkResult entry_vkEnumerateInstanceVersion(
    uint32_t* pApiVersion)
{
    AEMU_SCOPED_TRACE("vkEnumerateInstanceVersion");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkEnumerateInstanceVersion_VkResult_return = (VkResult)0;
    vkEnumerateInstanceVersion_VkResult_return = vkEnc->vkEnumerateInstanceVersion(pApiVersion);
    return vkEnumerateInstanceVersion_VkResult_return;
}
static VkResult entry_vkBindBufferMemory2(
    VkDevice device,
    uint32_t bindInfoCount,
    const VkBindBufferMemoryInfo* pBindInfos)
{
    AEMU_SCOPED_TRACE("vkBindBufferMemory2");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkBindBufferMemory2_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkBindBufferMemory2_VkResult_return = resources->on_vkBindBufferMemory2(vkEnc, VK_SUCCESS, device, bindInfoCount, pBindInfos);
    return vkBindBufferMemory2_VkResult_return;
}
static VkResult dynCheck_entry_vkBindBufferMemory2(
    VkDevice device,
    uint32_t bindInfoCount,
    const VkBindBufferMemoryInfo* pBindInfos)
{
    auto resources = ResourceTracker::get();
    if (resources->getApiVersionFromDevice(device) < VK_API_VERSION_1_1)
    {
        sOnInvalidDynamicallyCheckedCall("vkBindBufferMemory2", "VK_VERSION_1_1");
    }
    AEMU_SCOPED_TRACE("vkBindBufferMemory2");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkBindBufferMemory2_VkResult_return = (VkResult)0;
    vkBindBufferMemory2_VkResult_return = resources->on_vkBindBufferMemory2(vkEnc, VK_SUCCESS, device, bindInfoCount, pBindInfos);
    return vkBindBufferMemory2_VkResult_return;
}
static VkResult entry_vkBindImageMemory2(
    VkDevice device,
    uint32_t bindInfoCount,
    const VkBindImageMemoryInfo* pBindInfos)
{
    AEMU_SCOPED_TRACE("vkBindImageMemory2");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkBindImageMemory2_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkBindImageMemory2_VkResult_return = resources->on_vkBindImageMemory2(vkEnc, VK_SUCCESS, device, bindInfoCount, pBindInfos);
    return vkBindImageMemory2_VkResult_return;
}
static VkResult dynCheck_entry_vkBindImageMemory2(
    VkDevice device,
    uint32_t bindInfoCount,
    const VkBindImageMemoryInfo* pBindInfos)
{
    auto resources = ResourceTracker::get();
    if (resources->getApiVersionFromDevice(device) < VK_API_VERSION_1_1)
    {
        sOnInvalidDynamicallyCheckedCall("vkBindImageMemory2", "VK_VERSION_1_1");
    }
    AEMU_SCOPED_TRACE("vkBindImageMemory2");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkBindImageMemory2_VkResult_return = (VkResult)0;
    vkBindImageMemory2_VkResult_return = resources->on_vkBindImageMemory2(vkEnc, VK_SUCCESS, device, bindInfoCount, pBindInfos);
    return vkBindImageMemory2_VkResult_return;
}
static void entry_vkGetDeviceGroupPeerMemoryFeatures(
    VkDevice device,
    uint32_t heapIndex,
    uint32_t localDeviceIndex,
    uint32_t remoteDeviceIndex,
    VkPeerMemoryFeatureFlags* pPeerMemoryFeatures)
{
    AEMU_SCOPED_TRACE("vkGetDeviceGroupPeerMemoryFeatures");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetDeviceGroupPeerMemoryFeatures(device, heapIndex, localDeviceIndex, remoteDeviceIndex, pPeerMemoryFeatures);
}
static void dynCheck_entry_vkGetDeviceGroupPeerMemoryFeatures(
    VkDevice device,
    uint32_t heapIndex,
    uint32_t localDeviceIndex,
    uint32_t remoteDeviceIndex,
    VkPeerMemoryFeatureFlags* pPeerMemoryFeatures)
{
    auto resources = ResourceTracker::get();
    if (resources->getApiVersionFromDevice(device) < VK_API_VERSION_1_1)
    {
        sOnInvalidDynamicallyCheckedCall("vkGetDeviceGroupPeerMemoryFeatures", "VK_VERSION_1_1");
    }
    AEMU_SCOPED_TRACE("vkGetDeviceGroupPeerMemoryFeatures");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetDeviceGroupPeerMemoryFeatures(device, heapIndex, localDeviceIndex, remoteDeviceIndex, pPeerMemoryFeatures);
}
static void entry_vkCmdSetDeviceMask(
    VkCommandBuffer commandBuffer,
    uint32_t deviceMask)
{
    AEMU_SCOPED_TRACE("vkCmdSetDeviceMask");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdSetDeviceMask(commandBuffer, deviceMask);
}
static void entry_vkCmdDispatchBase(
    VkCommandBuffer commandBuffer,
    uint32_t baseGroupX,
    uint32_t baseGroupY,
    uint32_t baseGroupZ,
    uint32_t groupCountX,
    uint32_t groupCountY,
    uint32_t groupCountZ)
{
    AEMU_SCOPED_TRACE("vkCmdDispatchBase");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdDispatchBase(commandBuffer, baseGroupX, baseGroupY, baseGroupZ, groupCountX, groupCountY, groupCountZ);
}
static VkResult entry_vkEnumeratePhysicalDeviceGroups(
    VkInstance instance,
    uint32_t* pPhysicalDeviceGroupCount,
    VkPhysicalDeviceGroupProperties* pPhysicalDeviceGroupProperties)
{
    AEMU_SCOPED_TRACE("vkEnumeratePhysicalDeviceGroups");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkEnumeratePhysicalDeviceGroups_VkResult_return = (VkResult)0;
    vkEnumeratePhysicalDeviceGroups_VkResult_return = vkEnc->vkEnumeratePhysicalDeviceGroups(instance, pPhysicalDeviceGroupCount, pPhysicalDeviceGroupProperties);
    return vkEnumeratePhysicalDeviceGroups_VkResult_return;
}
static void entry_vkGetImageMemoryRequirements2(
    VkDevice device,
    const VkImageMemoryRequirementsInfo2* pInfo,
    VkMemoryRequirements2* pMemoryRequirements)
{
    AEMU_SCOPED_TRACE("vkGetImageMemoryRequirements2");
    auto vkEnc = HostConnection::get()->vkEncoder();
    auto resources = ResourceTracker::get();
    resources->on_vkGetImageMemoryRequirements2(vkEnc, device, pInfo, pMemoryRequirements);
}
static void dynCheck_entry_vkGetImageMemoryRequirements2(
    VkDevice device,
    const VkImageMemoryRequirementsInfo2* pInfo,
    VkMemoryRequirements2* pMemoryRequirements)
{
    auto resources = ResourceTracker::get();
    if (resources->getApiVersionFromDevice(device) < VK_API_VERSION_1_1)
    {
        sOnInvalidDynamicallyCheckedCall("vkGetImageMemoryRequirements2", "VK_VERSION_1_1");
    }
    AEMU_SCOPED_TRACE("vkGetImageMemoryRequirements2");
    auto vkEnc = HostConnection::get()->vkEncoder();
    resources->on_vkGetImageMemoryRequirements2(vkEnc, device, pInfo, pMemoryRequirements);
}
static void entry_vkGetBufferMemoryRequirements2(
    VkDevice device,
    const VkBufferMemoryRequirementsInfo2* pInfo,
    VkMemoryRequirements2* pMemoryRequirements)
{
    AEMU_SCOPED_TRACE("vkGetBufferMemoryRequirements2");
    auto vkEnc = HostConnection::get()->vkEncoder();
    auto resources = ResourceTracker::get();
    resources->on_vkGetBufferMemoryRequirements2(vkEnc, device, pInfo, pMemoryRequirements);
}
static void dynCheck_entry_vkGetBufferMemoryRequirements2(
    VkDevice device,
    const VkBufferMemoryRequirementsInfo2* pInfo,
    VkMemoryRequirements2* pMemoryRequirements)
{
    auto resources = ResourceTracker::get();
    if (resources->getApiVersionFromDevice(device) < VK_API_VERSION_1_1)
    {
        sOnInvalidDynamicallyCheckedCall("vkGetBufferMemoryRequirements2", "VK_VERSION_1_1");
    }
    AEMU_SCOPED_TRACE("vkGetBufferMemoryRequirements2");
    auto vkEnc = HostConnection::get()->vkEncoder();
    resources->on_vkGetBufferMemoryRequirements2(vkEnc, device, pInfo, pMemoryRequirements);
}
static void entry_vkGetImageSparseMemoryRequirements2(
    VkDevice device,
    const VkImageSparseMemoryRequirementsInfo2* pInfo,
    uint32_t* pSparseMemoryRequirementCount,
    VkSparseImageMemoryRequirements2* pSparseMemoryRequirements)
{
    AEMU_SCOPED_TRACE("vkGetImageSparseMemoryRequirements2");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetImageSparseMemoryRequirements2(device, pInfo, pSparseMemoryRequirementCount, pSparseMemoryRequirements);
}
static void dynCheck_entry_vkGetImageSparseMemoryRequirements2(
    VkDevice device,
    const VkImageSparseMemoryRequirementsInfo2* pInfo,
    uint32_t* pSparseMemoryRequirementCount,
    VkSparseImageMemoryRequirements2* pSparseMemoryRequirements)
{
    auto resources = ResourceTracker::get();
    if (resources->getApiVersionFromDevice(device) < VK_API_VERSION_1_1)
    {
        sOnInvalidDynamicallyCheckedCall("vkGetImageSparseMemoryRequirements2", "VK_VERSION_1_1");
    }
    AEMU_SCOPED_TRACE("vkGetImageSparseMemoryRequirements2");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetImageSparseMemoryRequirements2(device, pInfo, pSparseMemoryRequirementCount, pSparseMemoryRequirements);
}
static void entry_vkGetPhysicalDeviceFeatures2(
    VkPhysicalDevice physicalDevice,
    VkPhysicalDeviceFeatures2* pFeatures)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceFeatures2");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceFeatures2(physicalDevice, pFeatures);
}
static void entry_vkGetPhysicalDeviceProperties2(
    VkPhysicalDevice physicalDevice,
    VkPhysicalDeviceProperties2* pProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceProperties2");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceProperties2(physicalDevice, pProperties);
}
static void entry_vkGetPhysicalDeviceFormatProperties2(
    VkPhysicalDevice physicalDevice,
    VkFormat format,
    VkFormatProperties2* pFormatProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceFormatProperties2");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceFormatProperties2(physicalDevice, format, pFormatProperties);
}
static VkResult entry_vkGetPhysicalDeviceImageFormatProperties2(
    VkPhysicalDevice physicalDevice,
    const VkPhysicalDeviceImageFormatInfo2* pImageFormatInfo,
    VkImageFormatProperties2* pImageFormatProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceImageFormatProperties2");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetPhysicalDeviceImageFormatProperties2_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkGetPhysicalDeviceImageFormatProperties2_VkResult_return = resources->on_vkGetPhysicalDeviceImageFormatProperties2(vkEnc, VK_SUCCESS, physicalDevice, pImageFormatInfo, pImageFormatProperties);
    return vkGetPhysicalDeviceImageFormatProperties2_VkResult_return;
}
static void entry_vkGetPhysicalDeviceQueueFamilyProperties2(
    VkPhysicalDevice physicalDevice,
    uint32_t* pQueueFamilyPropertyCount,
    VkQueueFamilyProperties2* pQueueFamilyProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceQueueFamilyProperties2");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceQueueFamilyProperties2(physicalDevice, pQueueFamilyPropertyCount, pQueueFamilyProperties);
}
static void entry_vkGetPhysicalDeviceMemoryProperties2(
    VkPhysicalDevice physicalDevice,
    VkPhysicalDeviceMemoryProperties2* pMemoryProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceMemoryProperties2");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceMemoryProperties2(physicalDevice, pMemoryProperties);
}
static void entry_vkGetPhysicalDeviceSparseImageFormatProperties2(
    VkPhysicalDevice physicalDevice,
    const VkPhysicalDeviceSparseImageFormatInfo2* pFormatInfo,
    uint32_t* pPropertyCount,
    VkSparseImageFormatProperties2* pProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceSparseImageFormatProperties2");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceSparseImageFormatProperties2(physicalDevice, pFormatInfo, pPropertyCount, pProperties);
}
static void entry_vkTrimCommandPool(
    VkDevice device,
    VkCommandPool commandPool,
    VkCommandPoolTrimFlags flags)
{
    AEMU_SCOPED_TRACE("vkTrimCommandPool");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkTrimCommandPool(device, commandPool, flags);
}
static void dynCheck_entry_vkTrimCommandPool(
    VkDevice device,
    VkCommandPool commandPool,
    VkCommandPoolTrimFlags flags)
{
    auto resources = ResourceTracker::get();
    if (resources->getApiVersionFromDevice(device) < VK_API_VERSION_1_1)
    {
        sOnInvalidDynamicallyCheckedCall("vkTrimCommandPool", "VK_VERSION_1_1");
    }
    AEMU_SCOPED_TRACE("vkTrimCommandPool");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkTrimCommandPool(device, commandPool, flags);
}
static void entry_vkGetDeviceQueue2(
    VkDevice device,
    const VkDeviceQueueInfo2* pQueueInfo,
    VkQueue* pQueue)
{
    AEMU_SCOPED_TRACE("vkGetDeviceQueue2");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetDeviceQueue2(device, pQueueInfo, pQueue);
}
static void dynCheck_entry_vkGetDeviceQueue2(
    VkDevice device,
    const VkDeviceQueueInfo2* pQueueInfo,
    VkQueue* pQueue)
{
    auto resources = ResourceTracker::get();
    if (resources->getApiVersionFromDevice(device) < VK_API_VERSION_1_1)
    {
        sOnInvalidDynamicallyCheckedCall("vkGetDeviceQueue2", "VK_VERSION_1_1");
    }
    AEMU_SCOPED_TRACE("vkGetDeviceQueue2");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetDeviceQueue2(device, pQueueInfo, pQueue);
}
static VkResult entry_vkCreateSamplerYcbcrConversion(
    VkDevice device,
    const VkSamplerYcbcrConversionCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkSamplerYcbcrConversion* pYcbcrConversion)
{
    AEMU_SCOPED_TRACE("vkCreateSamplerYcbcrConversion");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateSamplerYcbcrConversion_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkCreateSamplerYcbcrConversion_VkResult_return = resources->on_vkCreateSamplerYcbcrConversion(vkEnc, VK_SUCCESS, device, pCreateInfo, pAllocator, pYcbcrConversion);
    return vkCreateSamplerYcbcrConversion_VkResult_return;
}
static VkResult dynCheck_entry_vkCreateSamplerYcbcrConversion(
    VkDevice device,
    const VkSamplerYcbcrConversionCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkSamplerYcbcrConversion* pYcbcrConversion)
{
    auto resources = ResourceTracker::get();
    if (resources->getApiVersionFromDevice(device) < VK_API_VERSION_1_1)
    {
        sOnInvalidDynamicallyCheckedCall("vkCreateSamplerYcbcrConversion", "VK_VERSION_1_1");
    }
    AEMU_SCOPED_TRACE("vkCreateSamplerYcbcrConversion");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateSamplerYcbcrConversion_VkResult_return = (VkResult)0;
    vkCreateSamplerYcbcrConversion_VkResult_return = resources->on_vkCreateSamplerYcbcrConversion(vkEnc, VK_SUCCESS, device, pCreateInfo, pAllocator, pYcbcrConversion);
    return vkCreateSamplerYcbcrConversion_VkResult_return;
}
static void entry_vkDestroySamplerYcbcrConversion(
    VkDevice device,
    VkSamplerYcbcrConversion ycbcrConversion,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroySamplerYcbcrConversion");
    auto vkEnc = HostConnection::get()->vkEncoder();
    auto resources = ResourceTracker::get();
    resources->on_vkDestroySamplerYcbcrConversion(vkEnc, device, ycbcrConversion, pAllocator);
}
static void dynCheck_entry_vkDestroySamplerYcbcrConversion(
    VkDevice device,
    VkSamplerYcbcrConversion ycbcrConversion,
    const VkAllocationCallbacks* pAllocator)
{
    auto resources = ResourceTracker::get();
    if (resources->getApiVersionFromDevice(device) < VK_API_VERSION_1_1)
    {
        sOnInvalidDynamicallyCheckedCall("vkDestroySamplerYcbcrConversion", "VK_VERSION_1_1");
    }
    AEMU_SCOPED_TRACE("vkDestroySamplerYcbcrConversion");
    auto vkEnc = HostConnection::get()->vkEncoder();
    resources->on_vkDestroySamplerYcbcrConversion(vkEnc, device, ycbcrConversion, pAllocator);
}
static VkResult entry_vkCreateDescriptorUpdateTemplate(
    VkDevice device,
    const VkDescriptorUpdateTemplateCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkDescriptorUpdateTemplate* pDescriptorUpdateTemplate)
{
    AEMU_SCOPED_TRACE("vkCreateDescriptorUpdateTemplate");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateDescriptorUpdateTemplate_VkResult_return = (VkResult)0;
    vkCreateDescriptorUpdateTemplate_VkResult_return = vkEnc->vkCreateDescriptorUpdateTemplate(device, pCreateInfo, pAllocator, pDescriptorUpdateTemplate);
    return vkCreateDescriptorUpdateTemplate_VkResult_return;
}
static VkResult dynCheck_entry_vkCreateDescriptorUpdateTemplate(
    VkDevice device,
    const VkDescriptorUpdateTemplateCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkDescriptorUpdateTemplate* pDescriptorUpdateTemplate)
{
    auto resources = ResourceTracker::get();
    if (resources->getApiVersionFromDevice(device) < VK_API_VERSION_1_1)
    {
        sOnInvalidDynamicallyCheckedCall("vkCreateDescriptorUpdateTemplate", "VK_VERSION_1_1");
    }
    AEMU_SCOPED_TRACE("vkCreateDescriptorUpdateTemplate");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateDescriptorUpdateTemplate_VkResult_return = (VkResult)0;
    vkCreateDescriptorUpdateTemplate_VkResult_return = vkEnc->vkCreateDescriptorUpdateTemplate(device, pCreateInfo, pAllocator, pDescriptorUpdateTemplate);
    return vkCreateDescriptorUpdateTemplate_VkResult_return;
}
static void entry_vkDestroyDescriptorUpdateTemplate(
    VkDevice device,
    VkDescriptorUpdateTemplate descriptorUpdateTemplate,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyDescriptorUpdateTemplate");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyDescriptorUpdateTemplate(device, descriptorUpdateTemplate, pAllocator);
}
static void dynCheck_entry_vkDestroyDescriptorUpdateTemplate(
    VkDevice device,
    VkDescriptorUpdateTemplate descriptorUpdateTemplate,
    const VkAllocationCallbacks* pAllocator)
{
    auto resources = ResourceTracker::get();
    if (resources->getApiVersionFromDevice(device) < VK_API_VERSION_1_1)
    {
        sOnInvalidDynamicallyCheckedCall("vkDestroyDescriptorUpdateTemplate", "VK_VERSION_1_1");
    }
    AEMU_SCOPED_TRACE("vkDestroyDescriptorUpdateTemplate");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyDescriptorUpdateTemplate(device, descriptorUpdateTemplate, pAllocator);
}
static void entry_vkUpdateDescriptorSetWithTemplate(
    VkDevice device,
    VkDescriptorSet descriptorSet,
    VkDescriptorUpdateTemplate descriptorUpdateTemplate,
    const void* pData)
{
    AEMU_SCOPED_TRACE("vkUpdateDescriptorSetWithTemplate");
    auto vkEnc = HostConnection::get()->vkEncoder();
    auto resources = ResourceTracker::get();
    resources->on_vkUpdateDescriptorSetWithTemplate(vkEnc, device, descriptorSet, descriptorUpdateTemplate, pData);
}
static void dynCheck_entry_vkUpdateDescriptorSetWithTemplate(
    VkDevice device,
    VkDescriptorSet descriptorSet,
    VkDescriptorUpdateTemplate descriptorUpdateTemplate,
    const void* pData)
{
    auto resources = ResourceTracker::get();
    if (resources->getApiVersionFromDevice(device) < VK_API_VERSION_1_1)
    {
        sOnInvalidDynamicallyCheckedCall("vkUpdateDescriptorSetWithTemplate", "VK_VERSION_1_1");
    }
    AEMU_SCOPED_TRACE("vkUpdateDescriptorSetWithTemplate");
    auto vkEnc = HostConnection::get()->vkEncoder();
    resources->on_vkUpdateDescriptorSetWithTemplate(vkEnc, device, descriptorSet, descriptorUpdateTemplate, pData);
}
static void entry_vkGetPhysicalDeviceExternalBufferProperties(
    VkPhysicalDevice physicalDevice,
    const VkPhysicalDeviceExternalBufferInfo* pExternalBufferInfo,
    VkExternalBufferProperties* pExternalBufferProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceExternalBufferProperties");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceExternalBufferProperties(physicalDevice, pExternalBufferInfo, pExternalBufferProperties);
}
static void entry_vkGetPhysicalDeviceExternalFenceProperties(
    VkPhysicalDevice physicalDevice,
    const VkPhysicalDeviceExternalFenceInfo* pExternalFenceInfo,
    VkExternalFenceProperties* pExternalFenceProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceExternalFenceProperties");
    auto vkEnc = HostConnection::get()->vkEncoder();
    auto resources = ResourceTracker::get();
    resources->on_vkGetPhysicalDeviceExternalFenceProperties(vkEnc, physicalDevice, pExternalFenceInfo, pExternalFenceProperties);
}
static void entry_vkGetPhysicalDeviceExternalSemaphoreProperties(
    VkPhysicalDevice physicalDevice,
    const VkPhysicalDeviceExternalSemaphoreInfo* pExternalSemaphoreInfo,
    VkExternalSemaphoreProperties* pExternalSemaphoreProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceExternalSemaphoreProperties");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceExternalSemaphoreProperties(physicalDevice, pExternalSemaphoreInfo, pExternalSemaphoreProperties);
}
static void entry_vkGetDescriptorSetLayoutSupport(
    VkDevice device,
    const VkDescriptorSetLayoutCreateInfo* pCreateInfo,
    VkDescriptorSetLayoutSupport* pSupport)
{
    AEMU_SCOPED_TRACE("vkGetDescriptorSetLayoutSupport");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetDescriptorSetLayoutSupport(device, pCreateInfo, pSupport);
}
static void dynCheck_entry_vkGetDescriptorSetLayoutSupport(
    VkDevice device,
    const VkDescriptorSetLayoutCreateInfo* pCreateInfo,
    VkDescriptorSetLayoutSupport* pSupport)
{
    auto resources = ResourceTracker::get();
    if (resources->getApiVersionFromDevice(device) < VK_API_VERSION_1_1)
    {
        sOnInvalidDynamicallyCheckedCall("vkGetDescriptorSetLayoutSupport", "VK_VERSION_1_1");
    }
    AEMU_SCOPED_TRACE("vkGetDescriptorSetLayoutSupport");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetDescriptorSetLayoutSupport(device, pCreateInfo, pSupport);
}
#endif
#ifdef VK_KHR_surface
static void entry_vkDestroySurfaceKHR(
    VkInstance instance,
    VkSurfaceKHR surface,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroySurfaceKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroySurfaceKHR(instance, surface, pAllocator);
}
static VkResult entry_vkGetPhysicalDeviceSurfaceSupportKHR(
    VkPhysicalDevice physicalDevice,
    uint32_t queueFamilyIndex,
    VkSurfaceKHR surface,
    VkBool32* pSupported)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceSurfaceSupportKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetPhysicalDeviceSurfaceSupportKHR_VkResult_return = (VkResult)0;
    vkGetPhysicalDeviceSurfaceSupportKHR_VkResult_return = vkEnc->vkGetPhysicalDeviceSurfaceSupportKHR(physicalDevice, queueFamilyIndex, surface, pSupported);
    return vkGetPhysicalDeviceSurfaceSupportKHR_VkResult_return;
}
static VkResult entry_vkGetPhysicalDeviceSurfaceCapabilitiesKHR(
    VkPhysicalDevice physicalDevice,
    VkSurfaceKHR surface,
    VkSurfaceCapabilitiesKHR* pSurfaceCapabilities)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceSurfaceCapabilitiesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetPhysicalDeviceSurfaceCapabilitiesKHR_VkResult_return = (VkResult)0;
    vkGetPhysicalDeviceSurfaceCapabilitiesKHR_VkResult_return = vkEnc->vkGetPhysicalDeviceSurfaceCapabilitiesKHR(physicalDevice, surface, pSurfaceCapabilities);
    return vkGetPhysicalDeviceSurfaceCapabilitiesKHR_VkResult_return;
}
static VkResult entry_vkGetPhysicalDeviceSurfaceFormatsKHR(
    VkPhysicalDevice physicalDevice,
    VkSurfaceKHR surface,
    uint32_t* pSurfaceFormatCount,
    VkSurfaceFormatKHR* pSurfaceFormats)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceSurfaceFormatsKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetPhysicalDeviceSurfaceFormatsKHR_VkResult_return = (VkResult)0;
    vkGetPhysicalDeviceSurfaceFormatsKHR_VkResult_return = vkEnc->vkGetPhysicalDeviceSurfaceFormatsKHR(physicalDevice, surface, pSurfaceFormatCount, pSurfaceFormats);
    return vkGetPhysicalDeviceSurfaceFormatsKHR_VkResult_return;
}
static VkResult entry_vkGetPhysicalDeviceSurfacePresentModesKHR(
    VkPhysicalDevice physicalDevice,
    VkSurfaceKHR surface,
    uint32_t* pPresentModeCount,
    VkPresentModeKHR* pPresentModes)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceSurfacePresentModesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetPhysicalDeviceSurfacePresentModesKHR_VkResult_return = (VkResult)0;
    vkGetPhysicalDeviceSurfacePresentModesKHR_VkResult_return = vkEnc->vkGetPhysicalDeviceSurfacePresentModesKHR(physicalDevice, surface, pPresentModeCount, pPresentModes);
    return vkGetPhysicalDeviceSurfacePresentModesKHR_VkResult_return;
}
#endif
#ifdef VK_KHR_swapchain
static VkResult entry_vkCreateSwapchainKHR(
    VkDevice device,
    const VkSwapchainCreateInfoKHR* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkSwapchainKHR* pSwapchain)
{
    AEMU_SCOPED_TRACE("vkCreateSwapchainKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateSwapchainKHR_VkResult_return = (VkResult)0;
    vkCreateSwapchainKHR_VkResult_return = vkEnc->vkCreateSwapchainKHR(device, pCreateInfo, pAllocator, pSwapchain);
    return vkCreateSwapchainKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkCreateSwapchainKHR(
    VkDevice device,
    const VkSwapchainCreateInfoKHR* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkSwapchainKHR* pSwapchain)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_swapchain"))
    {
        sOnInvalidDynamicallyCheckedCall("vkCreateSwapchainKHR", "VK_KHR_swapchain");
    }
    AEMU_SCOPED_TRACE("vkCreateSwapchainKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateSwapchainKHR_VkResult_return = (VkResult)0;
    vkCreateSwapchainKHR_VkResult_return = vkEnc->vkCreateSwapchainKHR(device, pCreateInfo, pAllocator, pSwapchain);
    return vkCreateSwapchainKHR_VkResult_return;
}
static void entry_vkDestroySwapchainKHR(
    VkDevice device,
    VkSwapchainKHR swapchain,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroySwapchainKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroySwapchainKHR(device, swapchain, pAllocator);
}
static void dynCheck_entry_vkDestroySwapchainKHR(
    VkDevice device,
    VkSwapchainKHR swapchain,
    const VkAllocationCallbacks* pAllocator)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_swapchain"))
    {
        sOnInvalidDynamicallyCheckedCall("vkDestroySwapchainKHR", "VK_KHR_swapchain");
    }
    AEMU_SCOPED_TRACE("vkDestroySwapchainKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroySwapchainKHR(device, swapchain, pAllocator);
}
static VkResult entry_vkGetSwapchainImagesKHR(
    VkDevice device,
    VkSwapchainKHR swapchain,
    uint32_t* pSwapchainImageCount,
    VkImage* pSwapchainImages)
{
    AEMU_SCOPED_TRACE("vkGetSwapchainImagesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetSwapchainImagesKHR_VkResult_return = (VkResult)0;
    vkGetSwapchainImagesKHR_VkResult_return = vkEnc->vkGetSwapchainImagesKHR(device, swapchain, pSwapchainImageCount, pSwapchainImages);
    return vkGetSwapchainImagesKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkGetSwapchainImagesKHR(
    VkDevice device,
    VkSwapchainKHR swapchain,
    uint32_t* pSwapchainImageCount,
    VkImage* pSwapchainImages)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_swapchain"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetSwapchainImagesKHR", "VK_KHR_swapchain");
    }
    AEMU_SCOPED_TRACE("vkGetSwapchainImagesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetSwapchainImagesKHR_VkResult_return = (VkResult)0;
    vkGetSwapchainImagesKHR_VkResult_return = vkEnc->vkGetSwapchainImagesKHR(device, swapchain, pSwapchainImageCount, pSwapchainImages);
    return vkGetSwapchainImagesKHR_VkResult_return;
}
static VkResult entry_vkAcquireNextImageKHR(
    VkDevice device,
    VkSwapchainKHR swapchain,
    uint64_t timeout,
    VkSemaphore semaphore,
    VkFence fence,
    uint32_t* pImageIndex)
{
    AEMU_SCOPED_TRACE("vkAcquireNextImageKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkAcquireNextImageKHR_VkResult_return = (VkResult)0;
    vkAcquireNextImageKHR_VkResult_return = vkEnc->vkAcquireNextImageKHR(device, swapchain, timeout, semaphore, fence, pImageIndex);
    return vkAcquireNextImageKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkAcquireNextImageKHR(
    VkDevice device,
    VkSwapchainKHR swapchain,
    uint64_t timeout,
    VkSemaphore semaphore,
    VkFence fence,
    uint32_t* pImageIndex)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_swapchain"))
    {
        sOnInvalidDynamicallyCheckedCall("vkAcquireNextImageKHR", "VK_KHR_swapchain");
    }
    AEMU_SCOPED_TRACE("vkAcquireNextImageKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkAcquireNextImageKHR_VkResult_return = (VkResult)0;
    vkAcquireNextImageKHR_VkResult_return = vkEnc->vkAcquireNextImageKHR(device, swapchain, timeout, semaphore, fence, pImageIndex);
    return vkAcquireNextImageKHR_VkResult_return;
}
static VkResult entry_vkQueuePresentKHR(
    VkQueue queue,
    const VkPresentInfoKHR* pPresentInfo)
{
    AEMU_SCOPED_TRACE("vkQueuePresentKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkQueuePresentKHR_VkResult_return = (VkResult)0;
    vkQueuePresentKHR_VkResult_return = vkEnc->vkQueuePresentKHR(queue, pPresentInfo);
    return vkQueuePresentKHR_VkResult_return;
}
static VkResult entry_vkGetDeviceGroupPresentCapabilitiesKHR(
    VkDevice device,
    VkDeviceGroupPresentCapabilitiesKHR* pDeviceGroupPresentCapabilities)
{
    AEMU_SCOPED_TRACE("vkGetDeviceGroupPresentCapabilitiesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetDeviceGroupPresentCapabilitiesKHR_VkResult_return = (VkResult)0;
    vkGetDeviceGroupPresentCapabilitiesKHR_VkResult_return = vkEnc->vkGetDeviceGroupPresentCapabilitiesKHR(device, pDeviceGroupPresentCapabilities);
    return vkGetDeviceGroupPresentCapabilitiesKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkGetDeviceGroupPresentCapabilitiesKHR(
    VkDevice device,
    VkDeviceGroupPresentCapabilitiesKHR* pDeviceGroupPresentCapabilities)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_swapchain"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetDeviceGroupPresentCapabilitiesKHR", "VK_KHR_swapchain");
    }
    AEMU_SCOPED_TRACE("vkGetDeviceGroupPresentCapabilitiesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetDeviceGroupPresentCapabilitiesKHR_VkResult_return = (VkResult)0;
    vkGetDeviceGroupPresentCapabilitiesKHR_VkResult_return = vkEnc->vkGetDeviceGroupPresentCapabilitiesKHR(device, pDeviceGroupPresentCapabilities);
    return vkGetDeviceGroupPresentCapabilitiesKHR_VkResult_return;
}
static VkResult entry_vkGetDeviceGroupSurfacePresentModesKHR(
    VkDevice device,
    VkSurfaceKHR surface,
    VkDeviceGroupPresentModeFlagsKHR* pModes)
{
    AEMU_SCOPED_TRACE("vkGetDeviceGroupSurfacePresentModesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetDeviceGroupSurfacePresentModesKHR_VkResult_return = (VkResult)0;
    vkGetDeviceGroupSurfacePresentModesKHR_VkResult_return = vkEnc->vkGetDeviceGroupSurfacePresentModesKHR(device, surface, pModes);
    return vkGetDeviceGroupSurfacePresentModesKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkGetDeviceGroupSurfacePresentModesKHR(
    VkDevice device,
    VkSurfaceKHR surface,
    VkDeviceGroupPresentModeFlagsKHR* pModes)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_swapchain"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetDeviceGroupSurfacePresentModesKHR", "VK_KHR_swapchain");
    }
    AEMU_SCOPED_TRACE("vkGetDeviceGroupSurfacePresentModesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetDeviceGroupSurfacePresentModesKHR_VkResult_return = (VkResult)0;
    vkGetDeviceGroupSurfacePresentModesKHR_VkResult_return = vkEnc->vkGetDeviceGroupSurfacePresentModesKHR(device, surface, pModes);
    return vkGetDeviceGroupSurfacePresentModesKHR_VkResult_return;
}
static VkResult entry_vkGetPhysicalDevicePresentRectanglesKHR(
    VkPhysicalDevice physicalDevice,
    VkSurfaceKHR surface,
    uint32_t* pRectCount,
    VkRect2D* pRects)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDevicePresentRectanglesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetPhysicalDevicePresentRectanglesKHR_VkResult_return = (VkResult)0;
    vkGetPhysicalDevicePresentRectanglesKHR_VkResult_return = vkEnc->vkGetPhysicalDevicePresentRectanglesKHR(physicalDevice, surface, pRectCount, pRects);
    return vkGetPhysicalDevicePresentRectanglesKHR_VkResult_return;
}
static VkResult entry_vkAcquireNextImage2KHR(
    VkDevice device,
    const VkAcquireNextImageInfoKHR* pAcquireInfo,
    uint32_t* pImageIndex)
{
    AEMU_SCOPED_TRACE("vkAcquireNextImage2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkAcquireNextImage2KHR_VkResult_return = (VkResult)0;
    vkAcquireNextImage2KHR_VkResult_return = vkEnc->vkAcquireNextImage2KHR(device, pAcquireInfo, pImageIndex);
    return vkAcquireNextImage2KHR_VkResult_return;
}
static VkResult dynCheck_entry_vkAcquireNextImage2KHR(
    VkDevice device,
    const VkAcquireNextImageInfoKHR* pAcquireInfo,
    uint32_t* pImageIndex)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_swapchain"))
    {
        sOnInvalidDynamicallyCheckedCall("vkAcquireNextImage2KHR", "VK_KHR_swapchain");
    }
    AEMU_SCOPED_TRACE("vkAcquireNextImage2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkAcquireNextImage2KHR_VkResult_return = (VkResult)0;
    vkAcquireNextImage2KHR_VkResult_return = vkEnc->vkAcquireNextImage2KHR(device, pAcquireInfo, pImageIndex);
    return vkAcquireNextImage2KHR_VkResult_return;
}
#endif
#ifdef VK_KHR_display
static VkResult entry_vkGetPhysicalDeviceDisplayPropertiesKHR(
    VkPhysicalDevice physicalDevice,
    uint32_t* pPropertyCount,
    VkDisplayPropertiesKHR* pProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceDisplayPropertiesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetPhysicalDeviceDisplayPropertiesKHR_VkResult_return = (VkResult)0;
    vkGetPhysicalDeviceDisplayPropertiesKHR_VkResult_return = vkEnc->vkGetPhysicalDeviceDisplayPropertiesKHR(physicalDevice, pPropertyCount, pProperties);
    return vkGetPhysicalDeviceDisplayPropertiesKHR_VkResult_return;
}
static VkResult entry_vkGetPhysicalDeviceDisplayPlanePropertiesKHR(
    VkPhysicalDevice physicalDevice,
    uint32_t* pPropertyCount,
    VkDisplayPlanePropertiesKHR* pProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceDisplayPlanePropertiesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetPhysicalDeviceDisplayPlanePropertiesKHR_VkResult_return = (VkResult)0;
    vkGetPhysicalDeviceDisplayPlanePropertiesKHR_VkResult_return = vkEnc->vkGetPhysicalDeviceDisplayPlanePropertiesKHR(physicalDevice, pPropertyCount, pProperties);
    return vkGetPhysicalDeviceDisplayPlanePropertiesKHR_VkResult_return;
}
static VkResult entry_vkGetDisplayPlaneSupportedDisplaysKHR(
    VkPhysicalDevice physicalDevice,
    uint32_t planeIndex,
    uint32_t* pDisplayCount,
    VkDisplayKHR* pDisplays)
{
    AEMU_SCOPED_TRACE("vkGetDisplayPlaneSupportedDisplaysKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetDisplayPlaneSupportedDisplaysKHR_VkResult_return = (VkResult)0;
    vkGetDisplayPlaneSupportedDisplaysKHR_VkResult_return = vkEnc->vkGetDisplayPlaneSupportedDisplaysKHR(physicalDevice, planeIndex, pDisplayCount, pDisplays);
    return vkGetDisplayPlaneSupportedDisplaysKHR_VkResult_return;
}
static VkResult entry_vkGetDisplayModePropertiesKHR(
    VkPhysicalDevice physicalDevice,
    VkDisplayKHR display,
    uint32_t* pPropertyCount,
    VkDisplayModePropertiesKHR* pProperties)
{
    AEMU_SCOPED_TRACE("vkGetDisplayModePropertiesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetDisplayModePropertiesKHR_VkResult_return = (VkResult)0;
    vkGetDisplayModePropertiesKHR_VkResult_return = vkEnc->vkGetDisplayModePropertiesKHR(physicalDevice, display, pPropertyCount, pProperties);
    return vkGetDisplayModePropertiesKHR_VkResult_return;
}
static VkResult entry_vkCreateDisplayModeKHR(
    VkPhysicalDevice physicalDevice,
    VkDisplayKHR display,
    const VkDisplayModeCreateInfoKHR* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkDisplayModeKHR* pMode)
{
    AEMU_SCOPED_TRACE("vkCreateDisplayModeKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateDisplayModeKHR_VkResult_return = (VkResult)0;
    vkCreateDisplayModeKHR_VkResult_return = vkEnc->vkCreateDisplayModeKHR(physicalDevice, display, pCreateInfo, pAllocator, pMode);
    return vkCreateDisplayModeKHR_VkResult_return;
}
static VkResult entry_vkGetDisplayPlaneCapabilitiesKHR(
    VkPhysicalDevice physicalDevice,
    VkDisplayModeKHR mode,
    uint32_t planeIndex,
    VkDisplayPlaneCapabilitiesKHR* pCapabilities)
{
    AEMU_SCOPED_TRACE("vkGetDisplayPlaneCapabilitiesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetDisplayPlaneCapabilitiesKHR_VkResult_return = (VkResult)0;
    vkGetDisplayPlaneCapabilitiesKHR_VkResult_return = vkEnc->vkGetDisplayPlaneCapabilitiesKHR(physicalDevice, mode, planeIndex, pCapabilities);
    return vkGetDisplayPlaneCapabilitiesKHR_VkResult_return;
}
static VkResult entry_vkCreateDisplayPlaneSurfaceKHR(
    VkInstance instance,
    const VkDisplaySurfaceCreateInfoKHR* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkSurfaceKHR* pSurface)
{
    AEMU_SCOPED_TRACE("vkCreateDisplayPlaneSurfaceKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateDisplayPlaneSurfaceKHR_VkResult_return = (VkResult)0;
    vkCreateDisplayPlaneSurfaceKHR_VkResult_return = vkEnc->vkCreateDisplayPlaneSurfaceKHR(instance, pCreateInfo, pAllocator, pSurface);
    return vkCreateDisplayPlaneSurfaceKHR_VkResult_return;
}
#endif
#ifdef VK_KHR_display_swapchain
static VkResult entry_vkCreateSharedSwapchainsKHR(
    VkDevice device,
    uint32_t swapchainCount,
    const VkSwapchainCreateInfoKHR* pCreateInfos,
    const VkAllocationCallbacks* pAllocator,
    VkSwapchainKHR* pSwapchains)
{
    AEMU_SCOPED_TRACE("vkCreateSharedSwapchainsKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateSharedSwapchainsKHR_VkResult_return = (VkResult)0;
    vkCreateSharedSwapchainsKHR_VkResult_return = vkEnc->vkCreateSharedSwapchainsKHR(device, swapchainCount, pCreateInfos, pAllocator, pSwapchains);
    return vkCreateSharedSwapchainsKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkCreateSharedSwapchainsKHR(
    VkDevice device,
    uint32_t swapchainCount,
    const VkSwapchainCreateInfoKHR* pCreateInfos,
    const VkAllocationCallbacks* pAllocator,
    VkSwapchainKHR* pSwapchains)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_display_swapchain"))
    {
        sOnInvalidDynamicallyCheckedCall("vkCreateSharedSwapchainsKHR", "VK_KHR_display_swapchain");
    }
    AEMU_SCOPED_TRACE("vkCreateSharedSwapchainsKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateSharedSwapchainsKHR_VkResult_return = (VkResult)0;
    vkCreateSharedSwapchainsKHR_VkResult_return = vkEnc->vkCreateSharedSwapchainsKHR(device, swapchainCount, pCreateInfos, pAllocator, pSwapchains);
    return vkCreateSharedSwapchainsKHR_VkResult_return;
}
#endif
#ifdef VK_KHR_xlib_surface
static VkResult entry_vkCreateXlibSurfaceKHR(
    VkInstance instance,
    const VkXlibSurfaceCreateInfoKHR* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkSurfaceKHR* pSurface)
{
    AEMU_SCOPED_TRACE("vkCreateXlibSurfaceKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateXlibSurfaceKHR_VkResult_return = (VkResult)0;
    vkCreateXlibSurfaceKHR_VkResult_return = vkEnc->vkCreateXlibSurfaceKHR(instance, pCreateInfo, pAllocator, pSurface);
    return vkCreateXlibSurfaceKHR_VkResult_return;
}
static VkBool32 entry_vkGetPhysicalDeviceXlibPresentationSupportKHR(
    VkPhysicalDevice physicalDevice,
    uint32_t queueFamilyIndex,
    Display* dpy,
    VisualID visualID)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceXlibPresentationSupportKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkBool32 vkGetPhysicalDeviceXlibPresentationSupportKHR_VkBool32_return = (VkBool32)0;
    vkGetPhysicalDeviceXlibPresentationSupportKHR_VkBool32_return = vkEnc->vkGetPhysicalDeviceXlibPresentationSupportKHR(physicalDevice, queueFamilyIndex, dpy, visualID);
    return vkGetPhysicalDeviceXlibPresentationSupportKHR_VkBool32_return;
}
#endif
#ifdef VK_KHR_xcb_surface
static VkResult entry_vkCreateXcbSurfaceKHR(
    VkInstance instance,
    const VkXcbSurfaceCreateInfoKHR* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkSurfaceKHR* pSurface)
{
    AEMU_SCOPED_TRACE("vkCreateXcbSurfaceKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateXcbSurfaceKHR_VkResult_return = (VkResult)0;
    vkCreateXcbSurfaceKHR_VkResult_return = vkEnc->vkCreateXcbSurfaceKHR(instance, pCreateInfo, pAllocator, pSurface);
    return vkCreateXcbSurfaceKHR_VkResult_return;
}
static VkBool32 entry_vkGetPhysicalDeviceXcbPresentationSupportKHR(
    VkPhysicalDevice physicalDevice,
    uint32_t queueFamilyIndex,
    xcb_connection_t* connection,
    xcb_visualid_t visual_id)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceXcbPresentationSupportKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkBool32 vkGetPhysicalDeviceXcbPresentationSupportKHR_VkBool32_return = (VkBool32)0;
    vkGetPhysicalDeviceXcbPresentationSupportKHR_VkBool32_return = vkEnc->vkGetPhysicalDeviceXcbPresentationSupportKHR(physicalDevice, queueFamilyIndex, connection, visual_id);
    return vkGetPhysicalDeviceXcbPresentationSupportKHR_VkBool32_return;
}
#endif
#ifdef VK_KHR_wayland_surface
static VkResult entry_vkCreateWaylandSurfaceKHR(
    VkInstance instance,
    const VkWaylandSurfaceCreateInfoKHR* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkSurfaceKHR* pSurface)
{
    AEMU_SCOPED_TRACE("vkCreateWaylandSurfaceKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateWaylandSurfaceKHR_VkResult_return = (VkResult)0;
    vkCreateWaylandSurfaceKHR_VkResult_return = vkEnc->vkCreateWaylandSurfaceKHR(instance, pCreateInfo, pAllocator, pSurface);
    return vkCreateWaylandSurfaceKHR_VkResult_return;
}
static VkBool32 entry_vkGetPhysicalDeviceWaylandPresentationSupportKHR(
    VkPhysicalDevice physicalDevice,
    uint32_t queueFamilyIndex,
    wl_display* display)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceWaylandPresentationSupportKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkBool32 vkGetPhysicalDeviceWaylandPresentationSupportKHR_VkBool32_return = (VkBool32)0;
    vkGetPhysicalDeviceWaylandPresentationSupportKHR_VkBool32_return = vkEnc->vkGetPhysicalDeviceWaylandPresentationSupportKHR(physicalDevice, queueFamilyIndex, display);
    return vkGetPhysicalDeviceWaylandPresentationSupportKHR_VkBool32_return;
}
#endif
#ifdef VK_KHR_mir_surface
static VkResult entry_vkCreateMirSurfaceKHR(
    VkInstance instance,
    const VkMirSurfaceCreateInfoKHR* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkSurfaceKHR* pSurface)
{
    AEMU_SCOPED_TRACE("vkCreateMirSurfaceKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateMirSurfaceKHR_VkResult_return = (VkResult)0;
    vkCreateMirSurfaceKHR_VkResult_return = vkEnc->vkCreateMirSurfaceKHR(instance, pCreateInfo, pAllocator, pSurface);
    return vkCreateMirSurfaceKHR_VkResult_return;
}
static VkBool32 entry_vkGetPhysicalDeviceMirPresentationSupportKHR(
    VkPhysicalDevice physicalDevice,
    uint32_t queueFamilyIndex,
    MirConnection* connection)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceMirPresentationSupportKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkBool32 vkGetPhysicalDeviceMirPresentationSupportKHR_VkBool32_return = (VkBool32)0;
    vkGetPhysicalDeviceMirPresentationSupportKHR_VkBool32_return = vkEnc->vkGetPhysicalDeviceMirPresentationSupportKHR(physicalDevice, queueFamilyIndex, connection);
    return vkGetPhysicalDeviceMirPresentationSupportKHR_VkBool32_return;
}
#endif
#ifdef VK_KHR_android_surface
static VkResult entry_vkCreateAndroidSurfaceKHR(
    VkInstance instance,
    const VkAndroidSurfaceCreateInfoKHR* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkSurfaceKHR* pSurface)
{
    AEMU_SCOPED_TRACE("vkCreateAndroidSurfaceKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateAndroidSurfaceKHR_VkResult_return = (VkResult)0;
    vkCreateAndroidSurfaceKHR_VkResult_return = vkEnc->vkCreateAndroidSurfaceKHR(instance, pCreateInfo, pAllocator, pSurface);
    return vkCreateAndroidSurfaceKHR_VkResult_return;
}
#endif
#ifdef VK_KHR_win32_surface
static VkResult entry_vkCreateWin32SurfaceKHR(
    VkInstance instance,
    const VkWin32SurfaceCreateInfoKHR* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkSurfaceKHR* pSurface)
{
    AEMU_SCOPED_TRACE("vkCreateWin32SurfaceKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateWin32SurfaceKHR_VkResult_return = (VkResult)0;
    vkCreateWin32SurfaceKHR_VkResult_return = vkEnc->vkCreateWin32SurfaceKHR(instance, pCreateInfo, pAllocator, pSurface);
    return vkCreateWin32SurfaceKHR_VkResult_return;
}
static VkBool32 entry_vkGetPhysicalDeviceWin32PresentationSupportKHR(
    VkPhysicalDevice physicalDevice,
    uint32_t queueFamilyIndex)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceWin32PresentationSupportKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkBool32 vkGetPhysicalDeviceWin32PresentationSupportKHR_VkBool32_return = (VkBool32)0;
    vkGetPhysicalDeviceWin32PresentationSupportKHR_VkBool32_return = vkEnc->vkGetPhysicalDeviceWin32PresentationSupportKHR(physicalDevice, queueFamilyIndex);
    return vkGetPhysicalDeviceWin32PresentationSupportKHR_VkBool32_return;
}
#endif
#ifdef VK_KHR_sampler_mirror_clamp_to_edge
#endif
#ifdef VK_KHR_multiview
#endif
#ifdef VK_KHR_get_physical_device_properties2
static void entry_vkGetPhysicalDeviceFeatures2KHR(
    VkPhysicalDevice physicalDevice,
    VkPhysicalDeviceFeatures2* pFeatures)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceFeatures2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceFeatures2KHR(physicalDevice, pFeatures);
}
static void entry_vkGetPhysicalDeviceProperties2KHR(
    VkPhysicalDevice physicalDevice,
    VkPhysicalDeviceProperties2* pProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceProperties2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceProperties2KHR(physicalDevice, pProperties);
}
static void entry_vkGetPhysicalDeviceFormatProperties2KHR(
    VkPhysicalDevice physicalDevice,
    VkFormat format,
    VkFormatProperties2* pFormatProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceFormatProperties2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceFormatProperties2KHR(physicalDevice, format, pFormatProperties);
}
static VkResult entry_vkGetPhysicalDeviceImageFormatProperties2KHR(
    VkPhysicalDevice physicalDevice,
    const VkPhysicalDeviceImageFormatInfo2* pImageFormatInfo,
    VkImageFormatProperties2* pImageFormatProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceImageFormatProperties2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetPhysicalDeviceImageFormatProperties2KHR_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkGetPhysicalDeviceImageFormatProperties2KHR_VkResult_return = resources->on_vkGetPhysicalDeviceImageFormatProperties2KHR(vkEnc, VK_SUCCESS, physicalDevice, pImageFormatInfo, pImageFormatProperties);
    return vkGetPhysicalDeviceImageFormatProperties2KHR_VkResult_return;
}
static void entry_vkGetPhysicalDeviceQueueFamilyProperties2KHR(
    VkPhysicalDevice physicalDevice,
    uint32_t* pQueueFamilyPropertyCount,
    VkQueueFamilyProperties2* pQueueFamilyProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceQueueFamilyProperties2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceQueueFamilyProperties2KHR(physicalDevice, pQueueFamilyPropertyCount, pQueueFamilyProperties);
}
static void entry_vkGetPhysicalDeviceMemoryProperties2KHR(
    VkPhysicalDevice physicalDevice,
    VkPhysicalDeviceMemoryProperties2* pMemoryProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceMemoryProperties2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceMemoryProperties2KHR(physicalDevice, pMemoryProperties);
}
static void entry_vkGetPhysicalDeviceSparseImageFormatProperties2KHR(
    VkPhysicalDevice physicalDevice,
    const VkPhysicalDeviceSparseImageFormatInfo2* pFormatInfo,
    uint32_t* pPropertyCount,
    VkSparseImageFormatProperties2* pProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceSparseImageFormatProperties2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceSparseImageFormatProperties2KHR(physicalDevice, pFormatInfo, pPropertyCount, pProperties);
}
#endif
#ifdef VK_KHR_device_group
static void entry_vkGetDeviceGroupPeerMemoryFeaturesKHR(
    VkDevice device,
    uint32_t heapIndex,
    uint32_t localDeviceIndex,
    uint32_t remoteDeviceIndex,
    VkPeerMemoryFeatureFlags* pPeerMemoryFeatures)
{
    AEMU_SCOPED_TRACE("vkGetDeviceGroupPeerMemoryFeaturesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetDeviceGroupPeerMemoryFeaturesKHR(device, heapIndex, localDeviceIndex, remoteDeviceIndex, pPeerMemoryFeatures);
}
static void dynCheck_entry_vkGetDeviceGroupPeerMemoryFeaturesKHR(
    VkDevice device,
    uint32_t heapIndex,
    uint32_t localDeviceIndex,
    uint32_t remoteDeviceIndex,
    VkPeerMemoryFeatureFlags* pPeerMemoryFeatures)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_device_group"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetDeviceGroupPeerMemoryFeaturesKHR", "VK_KHR_device_group");
    }
    AEMU_SCOPED_TRACE("vkGetDeviceGroupPeerMemoryFeaturesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetDeviceGroupPeerMemoryFeaturesKHR(device, heapIndex, localDeviceIndex, remoteDeviceIndex, pPeerMemoryFeatures);
}
static void entry_vkCmdSetDeviceMaskKHR(
    VkCommandBuffer commandBuffer,
    uint32_t deviceMask)
{
    AEMU_SCOPED_TRACE("vkCmdSetDeviceMaskKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdSetDeviceMaskKHR(commandBuffer, deviceMask);
}
static void entry_vkCmdDispatchBaseKHR(
    VkCommandBuffer commandBuffer,
    uint32_t baseGroupX,
    uint32_t baseGroupY,
    uint32_t baseGroupZ,
    uint32_t groupCountX,
    uint32_t groupCountY,
    uint32_t groupCountZ)
{
    AEMU_SCOPED_TRACE("vkCmdDispatchBaseKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdDispatchBaseKHR(commandBuffer, baseGroupX, baseGroupY, baseGroupZ, groupCountX, groupCountY, groupCountZ);
}
#endif
#ifdef VK_KHR_shader_draw_parameters
#endif
#ifdef VK_KHR_maintenance1
static void entry_vkTrimCommandPoolKHR(
    VkDevice device,
    VkCommandPool commandPool,
    VkCommandPoolTrimFlags flags)
{
    AEMU_SCOPED_TRACE("vkTrimCommandPoolKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkTrimCommandPoolKHR(device, commandPool, flags);
}
static void dynCheck_entry_vkTrimCommandPoolKHR(
    VkDevice device,
    VkCommandPool commandPool,
    VkCommandPoolTrimFlags flags)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_maintenance1"))
    {
        sOnInvalidDynamicallyCheckedCall("vkTrimCommandPoolKHR", "VK_KHR_maintenance1");
    }
    AEMU_SCOPED_TRACE("vkTrimCommandPoolKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkTrimCommandPoolKHR(device, commandPool, flags);
}
#endif
#ifdef VK_KHR_device_group_creation
static VkResult entry_vkEnumeratePhysicalDeviceGroupsKHR(
    VkInstance instance,
    uint32_t* pPhysicalDeviceGroupCount,
    VkPhysicalDeviceGroupProperties* pPhysicalDeviceGroupProperties)
{
    AEMU_SCOPED_TRACE("vkEnumeratePhysicalDeviceGroupsKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkEnumeratePhysicalDeviceGroupsKHR_VkResult_return = (VkResult)0;
    vkEnumeratePhysicalDeviceGroupsKHR_VkResult_return = vkEnc->vkEnumeratePhysicalDeviceGroupsKHR(instance, pPhysicalDeviceGroupCount, pPhysicalDeviceGroupProperties);
    return vkEnumeratePhysicalDeviceGroupsKHR_VkResult_return;
}
#endif
#ifdef VK_KHR_external_memory_capabilities
static void entry_vkGetPhysicalDeviceExternalBufferPropertiesKHR(
    VkPhysicalDevice physicalDevice,
    const VkPhysicalDeviceExternalBufferInfo* pExternalBufferInfo,
    VkExternalBufferProperties* pExternalBufferProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceExternalBufferPropertiesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceExternalBufferPropertiesKHR(physicalDevice, pExternalBufferInfo, pExternalBufferProperties);
}
#endif
#ifdef VK_KHR_external_memory
#endif
#ifdef VK_KHR_external_memory_win32
static VkResult entry_vkGetMemoryWin32HandleKHR(
    VkDevice device,
    const VkMemoryGetWin32HandleInfoKHR* pGetWin32HandleInfo,
    HANDLE* pHandle)
{
    AEMU_SCOPED_TRACE("vkGetMemoryWin32HandleKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetMemoryWin32HandleKHR_VkResult_return = (VkResult)0;
    vkGetMemoryWin32HandleKHR_VkResult_return = vkEnc->vkGetMemoryWin32HandleKHR(device, pGetWin32HandleInfo, pHandle);
    return vkGetMemoryWin32HandleKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkGetMemoryWin32HandleKHR(
    VkDevice device,
    const VkMemoryGetWin32HandleInfoKHR* pGetWin32HandleInfo,
    HANDLE* pHandle)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_external_memory_win32"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetMemoryWin32HandleKHR", "VK_KHR_external_memory_win32");
    }
    AEMU_SCOPED_TRACE("vkGetMemoryWin32HandleKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetMemoryWin32HandleKHR_VkResult_return = (VkResult)0;
    vkGetMemoryWin32HandleKHR_VkResult_return = vkEnc->vkGetMemoryWin32HandleKHR(device, pGetWin32HandleInfo, pHandle);
    return vkGetMemoryWin32HandleKHR_VkResult_return;
}
static VkResult entry_vkGetMemoryWin32HandlePropertiesKHR(
    VkDevice device,
    VkExternalMemoryHandleTypeFlagBits handleType,
    HANDLE handle,
    VkMemoryWin32HandlePropertiesKHR* pMemoryWin32HandleProperties)
{
    AEMU_SCOPED_TRACE("vkGetMemoryWin32HandlePropertiesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetMemoryWin32HandlePropertiesKHR_VkResult_return = (VkResult)0;
    vkGetMemoryWin32HandlePropertiesKHR_VkResult_return = vkEnc->vkGetMemoryWin32HandlePropertiesKHR(device, handleType, handle, pMemoryWin32HandleProperties);
    return vkGetMemoryWin32HandlePropertiesKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkGetMemoryWin32HandlePropertiesKHR(
    VkDevice device,
    VkExternalMemoryHandleTypeFlagBits handleType,
    HANDLE handle,
    VkMemoryWin32HandlePropertiesKHR* pMemoryWin32HandleProperties)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_external_memory_win32"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetMemoryWin32HandlePropertiesKHR", "VK_KHR_external_memory_win32");
    }
    AEMU_SCOPED_TRACE("vkGetMemoryWin32HandlePropertiesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetMemoryWin32HandlePropertiesKHR_VkResult_return = (VkResult)0;
    vkGetMemoryWin32HandlePropertiesKHR_VkResult_return = vkEnc->vkGetMemoryWin32HandlePropertiesKHR(device, handleType, handle, pMemoryWin32HandleProperties);
    return vkGetMemoryWin32HandlePropertiesKHR_VkResult_return;
}
#endif
#ifdef VK_KHR_external_memory_fd
static VkResult entry_vkGetMemoryFdKHR(
    VkDevice device,
    const VkMemoryGetFdInfoKHR* pGetFdInfo,
    int* pFd)
{
    AEMU_SCOPED_TRACE("vkGetMemoryFdKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetMemoryFdKHR_VkResult_return = (VkResult)0;
    vkGetMemoryFdKHR_VkResult_return = vkEnc->vkGetMemoryFdKHR(device, pGetFdInfo, pFd);
    return vkGetMemoryFdKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkGetMemoryFdKHR(
    VkDevice device,
    const VkMemoryGetFdInfoKHR* pGetFdInfo,
    int* pFd)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_external_memory_fd"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetMemoryFdKHR", "VK_KHR_external_memory_fd");
    }
    AEMU_SCOPED_TRACE("vkGetMemoryFdKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetMemoryFdKHR_VkResult_return = (VkResult)0;
    vkGetMemoryFdKHR_VkResult_return = vkEnc->vkGetMemoryFdKHR(device, pGetFdInfo, pFd);
    return vkGetMemoryFdKHR_VkResult_return;
}
static VkResult entry_vkGetMemoryFdPropertiesKHR(
    VkDevice device,
    VkExternalMemoryHandleTypeFlagBits handleType,
    int fd,
    VkMemoryFdPropertiesKHR* pMemoryFdProperties)
{
    AEMU_SCOPED_TRACE("vkGetMemoryFdPropertiesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetMemoryFdPropertiesKHR_VkResult_return = (VkResult)0;
    vkGetMemoryFdPropertiesKHR_VkResult_return = vkEnc->vkGetMemoryFdPropertiesKHR(device, handleType, fd, pMemoryFdProperties);
    return vkGetMemoryFdPropertiesKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkGetMemoryFdPropertiesKHR(
    VkDevice device,
    VkExternalMemoryHandleTypeFlagBits handleType,
    int fd,
    VkMemoryFdPropertiesKHR* pMemoryFdProperties)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_external_memory_fd"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetMemoryFdPropertiesKHR", "VK_KHR_external_memory_fd");
    }
    AEMU_SCOPED_TRACE("vkGetMemoryFdPropertiesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetMemoryFdPropertiesKHR_VkResult_return = (VkResult)0;
    vkGetMemoryFdPropertiesKHR_VkResult_return = vkEnc->vkGetMemoryFdPropertiesKHR(device, handleType, fd, pMemoryFdProperties);
    return vkGetMemoryFdPropertiesKHR_VkResult_return;
}
#endif
#ifdef VK_KHR_win32_keyed_mutex
#endif
#ifdef VK_KHR_external_semaphore_capabilities
static void entry_vkGetPhysicalDeviceExternalSemaphorePropertiesKHR(
    VkPhysicalDevice physicalDevice,
    const VkPhysicalDeviceExternalSemaphoreInfo* pExternalSemaphoreInfo,
    VkExternalSemaphoreProperties* pExternalSemaphoreProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceExternalSemaphorePropertiesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceExternalSemaphorePropertiesKHR(physicalDevice, pExternalSemaphoreInfo, pExternalSemaphoreProperties);
}
#endif
#ifdef VK_KHR_external_semaphore
#endif
#ifdef VK_KHR_external_semaphore_win32
static VkResult entry_vkImportSemaphoreWin32HandleKHR(
    VkDevice device,
    const VkImportSemaphoreWin32HandleInfoKHR* pImportSemaphoreWin32HandleInfo)
{
    AEMU_SCOPED_TRACE("vkImportSemaphoreWin32HandleKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkImportSemaphoreWin32HandleKHR_VkResult_return = (VkResult)0;
    vkImportSemaphoreWin32HandleKHR_VkResult_return = vkEnc->vkImportSemaphoreWin32HandleKHR(device, pImportSemaphoreWin32HandleInfo);
    return vkImportSemaphoreWin32HandleKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkImportSemaphoreWin32HandleKHR(
    VkDevice device,
    const VkImportSemaphoreWin32HandleInfoKHR* pImportSemaphoreWin32HandleInfo)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_external_semaphore_win32"))
    {
        sOnInvalidDynamicallyCheckedCall("vkImportSemaphoreWin32HandleKHR", "VK_KHR_external_semaphore_win32");
    }
    AEMU_SCOPED_TRACE("vkImportSemaphoreWin32HandleKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkImportSemaphoreWin32HandleKHR_VkResult_return = (VkResult)0;
    vkImportSemaphoreWin32HandleKHR_VkResult_return = vkEnc->vkImportSemaphoreWin32HandleKHR(device, pImportSemaphoreWin32HandleInfo);
    return vkImportSemaphoreWin32HandleKHR_VkResult_return;
}
static VkResult entry_vkGetSemaphoreWin32HandleKHR(
    VkDevice device,
    const VkSemaphoreGetWin32HandleInfoKHR* pGetWin32HandleInfo,
    HANDLE* pHandle)
{
    AEMU_SCOPED_TRACE("vkGetSemaphoreWin32HandleKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetSemaphoreWin32HandleKHR_VkResult_return = (VkResult)0;
    vkGetSemaphoreWin32HandleKHR_VkResult_return = vkEnc->vkGetSemaphoreWin32HandleKHR(device, pGetWin32HandleInfo, pHandle);
    return vkGetSemaphoreWin32HandleKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkGetSemaphoreWin32HandleKHR(
    VkDevice device,
    const VkSemaphoreGetWin32HandleInfoKHR* pGetWin32HandleInfo,
    HANDLE* pHandle)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_external_semaphore_win32"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetSemaphoreWin32HandleKHR", "VK_KHR_external_semaphore_win32");
    }
    AEMU_SCOPED_TRACE("vkGetSemaphoreWin32HandleKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetSemaphoreWin32HandleKHR_VkResult_return = (VkResult)0;
    vkGetSemaphoreWin32HandleKHR_VkResult_return = vkEnc->vkGetSemaphoreWin32HandleKHR(device, pGetWin32HandleInfo, pHandle);
    return vkGetSemaphoreWin32HandleKHR_VkResult_return;
}
#endif
#ifdef VK_KHR_external_semaphore_fd
static VkResult entry_vkImportSemaphoreFdKHR(
    VkDevice device,
    const VkImportSemaphoreFdInfoKHR* pImportSemaphoreFdInfo)
{
    AEMU_SCOPED_TRACE("vkImportSemaphoreFdKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkImportSemaphoreFdKHR_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkImportSemaphoreFdKHR_VkResult_return = resources->on_vkImportSemaphoreFdKHR(vkEnc, VK_SUCCESS, device, pImportSemaphoreFdInfo);
    return vkImportSemaphoreFdKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkImportSemaphoreFdKHR(
    VkDevice device,
    const VkImportSemaphoreFdInfoKHR* pImportSemaphoreFdInfo)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_external_semaphore_fd"))
    {
        sOnInvalidDynamicallyCheckedCall("vkImportSemaphoreFdKHR", "VK_KHR_external_semaphore_fd");
    }
    AEMU_SCOPED_TRACE("vkImportSemaphoreFdKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkImportSemaphoreFdKHR_VkResult_return = (VkResult)0;
    vkImportSemaphoreFdKHR_VkResult_return = resources->on_vkImportSemaphoreFdKHR(vkEnc, VK_SUCCESS, device, pImportSemaphoreFdInfo);
    return vkImportSemaphoreFdKHR_VkResult_return;
}
static VkResult entry_vkGetSemaphoreFdKHR(
    VkDevice device,
    const VkSemaphoreGetFdInfoKHR* pGetFdInfo,
    int* pFd)
{
    AEMU_SCOPED_TRACE("vkGetSemaphoreFdKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetSemaphoreFdKHR_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkGetSemaphoreFdKHR_VkResult_return = resources->on_vkGetSemaphoreFdKHR(vkEnc, VK_SUCCESS, device, pGetFdInfo, pFd);
    return vkGetSemaphoreFdKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkGetSemaphoreFdKHR(
    VkDevice device,
    const VkSemaphoreGetFdInfoKHR* pGetFdInfo,
    int* pFd)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_external_semaphore_fd"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetSemaphoreFdKHR", "VK_KHR_external_semaphore_fd");
    }
    AEMU_SCOPED_TRACE("vkGetSemaphoreFdKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetSemaphoreFdKHR_VkResult_return = (VkResult)0;
    vkGetSemaphoreFdKHR_VkResult_return = resources->on_vkGetSemaphoreFdKHR(vkEnc, VK_SUCCESS, device, pGetFdInfo, pFd);
    return vkGetSemaphoreFdKHR_VkResult_return;
}
#endif
#ifdef VK_KHR_push_descriptor
static void entry_vkCmdPushDescriptorSetKHR(
    VkCommandBuffer commandBuffer,
    VkPipelineBindPoint pipelineBindPoint,
    VkPipelineLayout layout,
    uint32_t set,
    uint32_t descriptorWriteCount,
    const VkWriteDescriptorSet* pDescriptorWrites)
{
    AEMU_SCOPED_TRACE("vkCmdPushDescriptorSetKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdPushDescriptorSetKHR(commandBuffer, pipelineBindPoint, layout, set, descriptorWriteCount, pDescriptorWrites);
}
static void entry_vkCmdPushDescriptorSetWithTemplateKHR(
    VkCommandBuffer commandBuffer,
    VkDescriptorUpdateTemplate descriptorUpdateTemplate,
    VkPipelineLayout layout,
    uint32_t set,
    const void* pData)
{
    AEMU_SCOPED_TRACE("vkCmdPushDescriptorSetWithTemplateKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdPushDescriptorSetWithTemplateKHR(commandBuffer, descriptorUpdateTemplate, layout, set, pData);
}
#endif
#ifdef VK_KHR_16bit_storage
#endif
#ifdef VK_KHR_incremental_present
#endif
#ifdef VK_KHR_descriptor_update_template
static VkResult entry_vkCreateDescriptorUpdateTemplateKHR(
    VkDevice device,
    const VkDescriptorUpdateTemplateCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkDescriptorUpdateTemplate* pDescriptorUpdateTemplate)
{
    AEMU_SCOPED_TRACE("vkCreateDescriptorUpdateTemplateKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateDescriptorUpdateTemplateKHR_VkResult_return = (VkResult)0;
    vkCreateDescriptorUpdateTemplateKHR_VkResult_return = vkEnc->vkCreateDescriptorUpdateTemplateKHR(device, pCreateInfo, pAllocator, pDescriptorUpdateTemplate);
    return vkCreateDescriptorUpdateTemplateKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkCreateDescriptorUpdateTemplateKHR(
    VkDevice device,
    const VkDescriptorUpdateTemplateCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkDescriptorUpdateTemplate* pDescriptorUpdateTemplate)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_descriptor_update_template"))
    {
        sOnInvalidDynamicallyCheckedCall("vkCreateDescriptorUpdateTemplateKHR", "VK_KHR_descriptor_update_template");
    }
    AEMU_SCOPED_TRACE("vkCreateDescriptorUpdateTemplateKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateDescriptorUpdateTemplateKHR_VkResult_return = (VkResult)0;
    vkCreateDescriptorUpdateTemplateKHR_VkResult_return = vkEnc->vkCreateDescriptorUpdateTemplateKHR(device, pCreateInfo, pAllocator, pDescriptorUpdateTemplate);
    return vkCreateDescriptorUpdateTemplateKHR_VkResult_return;
}
static void entry_vkDestroyDescriptorUpdateTemplateKHR(
    VkDevice device,
    VkDescriptorUpdateTemplate descriptorUpdateTemplate,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyDescriptorUpdateTemplateKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyDescriptorUpdateTemplateKHR(device, descriptorUpdateTemplate, pAllocator);
}
static void dynCheck_entry_vkDestroyDescriptorUpdateTemplateKHR(
    VkDevice device,
    VkDescriptorUpdateTemplate descriptorUpdateTemplate,
    const VkAllocationCallbacks* pAllocator)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_descriptor_update_template"))
    {
        sOnInvalidDynamicallyCheckedCall("vkDestroyDescriptorUpdateTemplateKHR", "VK_KHR_descriptor_update_template");
    }
    AEMU_SCOPED_TRACE("vkDestroyDescriptorUpdateTemplateKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyDescriptorUpdateTemplateKHR(device, descriptorUpdateTemplate, pAllocator);
}
static void entry_vkUpdateDescriptorSetWithTemplateKHR(
    VkDevice device,
    VkDescriptorSet descriptorSet,
    VkDescriptorUpdateTemplate descriptorUpdateTemplate,
    const void* pData)
{
    AEMU_SCOPED_TRACE("vkUpdateDescriptorSetWithTemplateKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkUpdateDescriptorSetWithTemplateKHR(device, descriptorSet, descriptorUpdateTemplate, pData);
}
static void dynCheck_entry_vkUpdateDescriptorSetWithTemplateKHR(
    VkDevice device,
    VkDescriptorSet descriptorSet,
    VkDescriptorUpdateTemplate descriptorUpdateTemplate,
    const void* pData)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_descriptor_update_template"))
    {
        sOnInvalidDynamicallyCheckedCall("vkUpdateDescriptorSetWithTemplateKHR", "VK_KHR_descriptor_update_template");
    }
    AEMU_SCOPED_TRACE("vkUpdateDescriptorSetWithTemplateKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkUpdateDescriptorSetWithTemplateKHR(device, descriptorSet, descriptorUpdateTemplate, pData);
}
#endif
#ifdef VK_KHR_create_renderpass2
static VkResult entry_vkCreateRenderPass2KHR(
    VkDevice device,
    const VkRenderPassCreateInfo2KHR* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkRenderPass* pRenderPass)
{
    AEMU_SCOPED_TRACE("vkCreateRenderPass2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateRenderPass2KHR_VkResult_return = (VkResult)0;
    vkCreateRenderPass2KHR_VkResult_return = vkEnc->vkCreateRenderPass2KHR(device, pCreateInfo, pAllocator, pRenderPass);
    return vkCreateRenderPass2KHR_VkResult_return;
}
static VkResult dynCheck_entry_vkCreateRenderPass2KHR(
    VkDevice device,
    const VkRenderPassCreateInfo2KHR* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkRenderPass* pRenderPass)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_create_renderpass2"))
    {
        sOnInvalidDynamicallyCheckedCall("vkCreateRenderPass2KHR", "VK_KHR_create_renderpass2");
    }
    AEMU_SCOPED_TRACE("vkCreateRenderPass2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateRenderPass2KHR_VkResult_return = (VkResult)0;
    vkCreateRenderPass2KHR_VkResult_return = vkEnc->vkCreateRenderPass2KHR(device, pCreateInfo, pAllocator, pRenderPass);
    return vkCreateRenderPass2KHR_VkResult_return;
}
static void entry_vkCmdBeginRenderPass2KHR(
    VkCommandBuffer commandBuffer,
    const VkRenderPassBeginInfo* pRenderPassBegin,
    const VkSubpassBeginInfoKHR* pSubpassBeginInfo)
{
    AEMU_SCOPED_TRACE("vkCmdBeginRenderPass2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdBeginRenderPass2KHR(commandBuffer, pRenderPassBegin, pSubpassBeginInfo);
}
static void entry_vkCmdNextSubpass2KHR(
    VkCommandBuffer commandBuffer,
    const VkSubpassBeginInfoKHR* pSubpassBeginInfo,
    const VkSubpassEndInfoKHR* pSubpassEndInfo)
{
    AEMU_SCOPED_TRACE("vkCmdNextSubpass2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdNextSubpass2KHR(commandBuffer, pSubpassBeginInfo, pSubpassEndInfo);
}
static void entry_vkCmdEndRenderPass2KHR(
    VkCommandBuffer commandBuffer,
    const VkSubpassEndInfoKHR* pSubpassEndInfo)
{
    AEMU_SCOPED_TRACE("vkCmdEndRenderPass2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdEndRenderPass2KHR(commandBuffer, pSubpassEndInfo);
}
#endif
#ifdef VK_KHR_shared_presentable_image
static VkResult entry_vkGetSwapchainStatusKHR(
    VkDevice device,
    VkSwapchainKHR swapchain)
{
    AEMU_SCOPED_TRACE("vkGetSwapchainStatusKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetSwapchainStatusKHR_VkResult_return = (VkResult)0;
    vkGetSwapchainStatusKHR_VkResult_return = vkEnc->vkGetSwapchainStatusKHR(device, swapchain);
    return vkGetSwapchainStatusKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkGetSwapchainStatusKHR(
    VkDevice device,
    VkSwapchainKHR swapchain)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_shared_presentable_image"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetSwapchainStatusKHR", "VK_KHR_shared_presentable_image");
    }
    AEMU_SCOPED_TRACE("vkGetSwapchainStatusKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetSwapchainStatusKHR_VkResult_return = (VkResult)0;
    vkGetSwapchainStatusKHR_VkResult_return = vkEnc->vkGetSwapchainStatusKHR(device, swapchain);
    return vkGetSwapchainStatusKHR_VkResult_return;
}
#endif
#ifdef VK_KHR_external_fence_capabilities
static void entry_vkGetPhysicalDeviceExternalFencePropertiesKHR(
    VkPhysicalDevice physicalDevice,
    const VkPhysicalDeviceExternalFenceInfo* pExternalFenceInfo,
    VkExternalFenceProperties* pExternalFenceProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceExternalFencePropertiesKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    auto resources = ResourceTracker::get();
    resources->on_vkGetPhysicalDeviceExternalFencePropertiesKHR(vkEnc, physicalDevice, pExternalFenceInfo, pExternalFenceProperties);
}
#endif
#ifdef VK_KHR_external_fence
#endif
#ifdef VK_KHR_external_fence_win32
static VkResult entry_vkImportFenceWin32HandleKHR(
    VkDevice device,
    const VkImportFenceWin32HandleInfoKHR* pImportFenceWin32HandleInfo)
{
    AEMU_SCOPED_TRACE("vkImportFenceWin32HandleKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkImportFenceWin32HandleKHR_VkResult_return = (VkResult)0;
    vkImportFenceWin32HandleKHR_VkResult_return = vkEnc->vkImportFenceWin32HandleKHR(device, pImportFenceWin32HandleInfo);
    return vkImportFenceWin32HandleKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkImportFenceWin32HandleKHR(
    VkDevice device,
    const VkImportFenceWin32HandleInfoKHR* pImportFenceWin32HandleInfo)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_external_fence_win32"))
    {
        sOnInvalidDynamicallyCheckedCall("vkImportFenceWin32HandleKHR", "VK_KHR_external_fence_win32");
    }
    AEMU_SCOPED_TRACE("vkImportFenceWin32HandleKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkImportFenceWin32HandleKHR_VkResult_return = (VkResult)0;
    vkImportFenceWin32HandleKHR_VkResult_return = vkEnc->vkImportFenceWin32HandleKHR(device, pImportFenceWin32HandleInfo);
    return vkImportFenceWin32HandleKHR_VkResult_return;
}
static VkResult entry_vkGetFenceWin32HandleKHR(
    VkDevice device,
    const VkFenceGetWin32HandleInfoKHR* pGetWin32HandleInfo,
    HANDLE* pHandle)
{
    AEMU_SCOPED_TRACE("vkGetFenceWin32HandleKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetFenceWin32HandleKHR_VkResult_return = (VkResult)0;
    vkGetFenceWin32HandleKHR_VkResult_return = vkEnc->vkGetFenceWin32HandleKHR(device, pGetWin32HandleInfo, pHandle);
    return vkGetFenceWin32HandleKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkGetFenceWin32HandleKHR(
    VkDevice device,
    const VkFenceGetWin32HandleInfoKHR* pGetWin32HandleInfo,
    HANDLE* pHandle)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_external_fence_win32"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetFenceWin32HandleKHR", "VK_KHR_external_fence_win32");
    }
    AEMU_SCOPED_TRACE("vkGetFenceWin32HandleKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetFenceWin32HandleKHR_VkResult_return = (VkResult)0;
    vkGetFenceWin32HandleKHR_VkResult_return = vkEnc->vkGetFenceWin32HandleKHR(device, pGetWin32HandleInfo, pHandle);
    return vkGetFenceWin32HandleKHR_VkResult_return;
}
#endif
#ifdef VK_KHR_external_fence_fd
static VkResult entry_vkImportFenceFdKHR(
    VkDevice device,
    const VkImportFenceFdInfoKHR* pImportFenceFdInfo)
{
    AEMU_SCOPED_TRACE("vkImportFenceFdKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkImportFenceFdKHR_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkImportFenceFdKHR_VkResult_return = resources->on_vkImportFenceFdKHR(vkEnc, VK_SUCCESS, device, pImportFenceFdInfo);
    return vkImportFenceFdKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkImportFenceFdKHR(
    VkDevice device,
    const VkImportFenceFdInfoKHR* pImportFenceFdInfo)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_external_fence_fd"))
    {
        sOnInvalidDynamicallyCheckedCall("vkImportFenceFdKHR", "VK_KHR_external_fence_fd");
    }
    AEMU_SCOPED_TRACE("vkImportFenceFdKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkImportFenceFdKHR_VkResult_return = (VkResult)0;
    vkImportFenceFdKHR_VkResult_return = resources->on_vkImportFenceFdKHR(vkEnc, VK_SUCCESS, device, pImportFenceFdInfo);
    return vkImportFenceFdKHR_VkResult_return;
}
static VkResult entry_vkGetFenceFdKHR(
    VkDevice device,
    const VkFenceGetFdInfoKHR* pGetFdInfo,
    int* pFd)
{
    AEMU_SCOPED_TRACE("vkGetFenceFdKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetFenceFdKHR_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkGetFenceFdKHR_VkResult_return = resources->on_vkGetFenceFdKHR(vkEnc, VK_SUCCESS, device, pGetFdInfo, pFd);
    return vkGetFenceFdKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkGetFenceFdKHR(
    VkDevice device,
    const VkFenceGetFdInfoKHR* pGetFdInfo,
    int* pFd)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_external_fence_fd"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetFenceFdKHR", "VK_KHR_external_fence_fd");
    }
    AEMU_SCOPED_TRACE("vkGetFenceFdKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetFenceFdKHR_VkResult_return = (VkResult)0;
    vkGetFenceFdKHR_VkResult_return = resources->on_vkGetFenceFdKHR(vkEnc, VK_SUCCESS, device, pGetFdInfo, pFd);
    return vkGetFenceFdKHR_VkResult_return;
}
#endif
#ifdef VK_KHR_maintenance2
#endif
#ifdef VK_KHR_get_surface_capabilities2
static VkResult entry_vkGetPhysicalDeviceSurfaceCapabilities2KHR(
    VkPhysicalDevice physicalDevice,
    const VkPhysicalDeviceSurfaceInfo2KHR* pSurfaceInfo,
    VkSurfaceCapabilities2KHR* pSurfaceCapabilities)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceSurfaceCapabilities2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetPhysicalDeviceSurfaceCapabilities2KHR_VkResult_return = (VkResult)0;
    vkGetPhysicalDeviceSurfaceCapabilities2KHR_VkResult_return = vkEnc->vkGetPhysicalDeviceSurfaceCapabilities2KHR(physicalDevice, pSurfaceInfo, pSurfaceCapabilities);
    return vkGetPhysicalDeviceSurfaceCapabilities2KHR_VkResult_return;
}
static VkResult entry_vkGetPhysicalDeviceSurfaceFormats2KHR(
    VkPhysicalDevice physicalDevice,
    const VkPhysicalDeviceSurfaceInfo2KHR* pSurfaceInfo,
    uint32_t* pSurfaceFormatCount,
    VkSurfaceFormat2KHR* pSurfaceFormats)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceSurfaceFormats2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetPhysicalDeviceSurfaceFormats2KHR_VkResult_return = (VkResult)0;
    vkGetPhysicalDeviceSurfaceFormats2KHR_VkResult_return = vkEnc->vkGetPhysicalDeviceSurfaceFormats2KHR(physicalDevice, pSurfaceInfo, pSurfaceFormatCount, pSurfaceFormats);
    return vkGetPhysicalDeviceSurfaceFormats2KHR_VkResult_return;
}
#endif
#ifdef VK_KHR_variable_pointers
#endif
#ifdef VK_KHR_get_display_properties2
static VkResult entry_vkGetPhysicalDeviceDisplayProperties2KHR(
    VkPhysicalDevice physicalDevice,
    uint32_t* pPropertyCount,
    VkDisplayProperties2KHR* pProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceDisplayProperties2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetPhysicalDeviceDisplayProperties2KHR_VkResult_return = (VkResult)0;
    vkGetPhysicalDeviceDisplayProperties2KHR_VkResult_return = vkEnc->vkGetPhysicalDeviceDisplayProperties2KHR(physicalDevice, pPropertyCount, pProperties);
    return vkGetPhysicalDeviceDisplayProperties2KHR_VkResult_return;
}
static VkResult entry_vkGetPhysicalDeviceDisplayPlaneProperties2KHR(
    VkPhysicalDevice physicalDevice,
    uint32_t* pPropertyCount,
    VkDisplayPlaneProperties2KHR* pProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceDisplayPlaneProperties2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetPhysicalDeviceDisplayPlaneProperties2KHR_VkResult_return = (VkResult)0;
    vkGetPhysicalDeviceDisplayPlaneProperties2KHR_VkResult_return = vkEnc->vkGetPhysicalDeviceDisplayPlaneProperties2KHR(physicalDevice, pPropertyCount, pProperties);
    return vkGetPhysicalDeviceDisplayPlaneProperties2KHR_VkResult_return;
}
static VkResult entry_vkGetDisplayModeProperties2KHR(
    VkPhysicalDevice physicalDevice,
    VkDisplayKHR display,
    uint32_t* pPropertyCount,
    VkDisplayModeProperties2KHR* pProperties)
{
    AEMU_SCOPED_TRACE("vkGetDisplayModeProperties2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetDisplayModeProperties2KHR_VkResult_return = (VkResult)0;
    vkGetDisplayModeProperties2KHR_VkResult_return = vkEnc->vkGetDisplayModeProperties2KHR(physicalDevice, display, pPropertyCount, pProperties);
    return vkGetDisplayModeProperties2KHR_VkResult_return;
}
static VkResult entry_vkGetDisplayPlaneCapabilities2KHR(
    VkPhysicalDevice physicalDevice,
    const VkDisplayPlaneInfo2KHR* pDisplayPlaneInfo,
    VkDisplayPlaneCapabilities2KHR* pCapabilities)
{
    AEMU_SCOPED_TRACE("vkGetDisplayPlaneCapabilities2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetDisplayPlaneCapabilities2KHR_VkResult_return = (VkResult)0;
    vkGetDisplayPlaneCapabilities2KHR_VkResult_return = vkEnc->vkGetDisplayPlaneCapabilities2KHR(physicalDevice, pDisplayPlaneInfo, pCapabilities);
    return vkGetDisplayPlaneCapabilities2KHR_VkResult_return;
}
#endif
#ifdef VK_KHR_dedicated_allocation
#endif
#ifdef VK_KHR_storage_buffer_storage_class
#endif
#ifdef VK_KHR_relaxed_block_layout
#endif
#ifdef VK_KHR_get_memory_requirements2
static void entry_vkGetImageMemoryRequirements2KHR(
    VkDevice device,
    const VkImageMemoryRequirementsInfo2* pInfo,
    VkMemoryRequirements2* pMemoryRequirements)
{
    AEMU_SCOPED_TRACE("vkGetImageMemoryRequirements2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    auto resources = ResourceTracker::get();
    resources->on_vkGetImageMemoryRequirements2KHR(vkEnc, device, pInfo, pMemoryRequirements);
}
static void dynCheck_entry_vkGetImageMemoryRequirements2KHR(
    VkDevice device,
    const VkImageMemoryRequirementsInfo2* pInfo,
    VkMemoryRequirements2* pMemoryRequirements)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_get_memory_requirements2"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetImageMemoryRequirements2KHR", "VK_KHR_get_memory_requirements2");
    }
    AEMU_SCOPED_TRACE("vkGetImageMemoryRequirements2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    resources->on_vkGetImageMemoryRequirements2KHR(vkEnc, device, pInfo, pMemoryRequirements);
}
static void entry_vkGetBufferMemoryRequirements2KHR(
    VkDevice device,
    const VkBufferMemoryRequirementsInfo2* pInfo,
    VkMemoryRequirements2* pMemoryRequirements)
{
    AEMU_SCOPED_TRACE("vkGetBufferMemoryRequirements2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    auto resources = ResourceTracker::get();
    resources->on_vkGetBufferMemoryRequirements2KHR(vkEnc, device, pInfo, pMemoryRequirements);
}
static void dynCheck_entry_vkGetBufferMemoryRequirements2KHR(
    VkDevice device,
    const VkBufferMemoryRequirementsInfo2* pInfo,
    VkMemoryRequirements2* pMemoryRequirements)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_get_memory_requirements2"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetBufferMemoryRequirements2KHR", "VK_KHR_get_memory_requirements2");
    }
    AEMU_SCOPED_TRACE("vkGetBufferMemoryRequirements2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    resources->on_vkGetBufferMemoryRequirements2KHR(vkEnc, device, pInfo, pMemoryRequirements);
}
static void entry_vkGetImageSparseMemoryRequirements2KHR(
    VkDevice device,
    const VkImageSparseMemoryRequirementsInfo2* pInfo,
    uint32_t* pSparseMemoryRequirementCount,
    VkSparseImageMemoryRequirements2* pSparseMemoryRequirements)
{
    AEMU_SCOPED_TRACE("vkGetImageSparseMemoryRequirements2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetImageSparseMemoryRequirements2KHR(device, pInfo, pSparseMemoryRequirementCount, pSparseMemoryRequirements);
}
static void dynCheck_entry_vkGetImageSparseMemoryRequirements2KHR(
    VkDevice device,
    const VkImageSparseMemoryRequirementsInfo2* pInfo,
    uint32_t* pSparseMemoryRequirementCount,
    VkSparseImageMemoryRequirements2* pSparseMemoryRequirements)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_get_memory_requirements2"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetImageSparseMemoryRequirements2KHR", "VK_KHR_get_memory_requirements2");
    }
    AEMU_SCOPED_TRACE("vkGetImageSparseMemoryRequirements2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetImageSparseMemoryRequirements2KHR(device, pInfo, pSparseMemoryRequirementCount, pSparseMemoryRequirements);
}
#endif
#ifdef VK_KHR_image_format_list
#endif
#ifdef VK_KHR_sampler_ycbcr_conversion
static VkResult entry_vkCreateSamplerYcbcrConversionKHR(
    VkDevice device,
    const VkSamplerYcbcrConversionCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkSamplerYcbcrConversion* pYcbcrConversion)
{
    AEMU_SCOPED_TRACE("vkCreateSamplerYcbcrConversionKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateSamplerYcbcrConversionKHR_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkCreateSamplerYcbcrConversionKHR_VkResult_return = resources->on_vkCreateSamplerYcbcrConversionKHR(vkEnc, VK_SUCCESS, device, pCreateInfo, pAllocator, pYcbcrConversion);
    return vkCreateSamplerYcbcrConversionKHR_VkResult_return;
}
static VkResult dynCheck_entry_vkCreateSamplerYcbcrConversionKHR(
    VkDevice device,
    const VkSamplerYcbcrConversionCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkSamplerYcbcrConversion* pYcbcrConversion)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_sampler_ycbcr_conversion"))
    {
        sOnInvalidDynamicallyCheckedCall("vkCreateSamplerYcbcrConversionKHR", "VK_KHR_sampler_ycbcr_conversion");
    }
    AEMU_SCOPED_TRACE("vkCreateSamplerYcbcrConversionKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateSamplerYcbcrConversionKHR_VkResult_return = (VkResult)0;
    vkCreateSamplerYcbcrConversionKHR_VkResult_return = resources->on_vkCreateSamplerYcbcrConversionKHR(vkEnc, VK_SUCCESS, device, pCreateInfo, pAllocator, pYcbcrConversion);
    return vkCreateSamplerYcbcrConversionKHR_VkResult_return;
}
static void entry_vkDestroySamplerYcbcrConversionKHR(
    VkDevice device,
    VkSamplerYcbcrConversion ycbcrConversion,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroySamplerYcbcrConversionKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    auto resources = ResourceTracker::get();
    resources->on_vkDestroySamplerYcbcrConversionKHR(vkEnc, device, ycbcrConversion, pAllocator);
}
static void dynCheck_entry_vkDestroySamplerYcbcrConversionKHR(
    VkDevice device,
    VkSamplerYcbcrConversion ycbcrConversion,
    const VkAllocationCallbacks* pAllocator)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_sampler_ycbcr_conversion"))
    {
        sOnInvalidDynamicallyCheckedCall("vkDestroySamplerYcbcrConversionKHR", "VK_KHR_sampler_ycbcr_conversion");
    }
    AEMU_SCOPED_TRACE("vkDestroySamplerYcbcrConversionKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    resources->on_vkDestroySamplerYcbcrConversionKHR(vkEnc, device, ycbcrConversion, pAllocator);
}
#endif
#ifdef VK_KHR_bind_memory2
static VkResult entry_vkBindBufferMemory2KHR(
    VkDevice device,
    uint32_t bindInfoCount,
    const VkBindBufferMemoryInfo* pBindInfos)
{
    AEMU_SCOPED_TRACE("vkBindBufferMemory2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkBindBufferMemory2KHR_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkBindBufferMemory2KHR_VkResult_return = resources->on_vkBindBufferMemory2KHR(vkEnc, VK_SUCCESS, device, bindInfoCount, pBindInfos);
    return vkBindBufferMemory2KHR_VkResult_return;
}
static VkResult dynCheck_entry_vkBindBufferMemory2KHR(
    VkDevice device,
    uint32_t bindInfoCount,
    const VkBindBufferMemoryInfo* pBindInfos)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_bind_memory2"))
    {
        sOnInvalidDynamicallyCheckedCall("vkBindBufferMemory2KHR", "VK_KHR_bind_memory2");
    }
    AEMU_SCOPED_TRACE("vkBindBufferMemory2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkBindBufferMemory2KHR_VkResult_return = (VkResult)0;
    vkBindBufferMemory2KHR_VkResult_return = resources->on_vkBindBufferMemory2KHR(vkEnc, VK_SUCCESS, device, bindInfoCount, pBindInfos);
    return vkBindBufferMemory2KHR_VkResult_return;
}
static VkResult entry_vkBindImageMemory2KHR(
    VkDevice device,
    uint32_t bindInfoCount,
    const VkBindImageMemoryInfo* pBindInfos)
{
    AEMU_SCOPED_TRACE("vkBindImageMemory2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkBindImageMemory2KHR_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkBindImageMemory2KHR_VkResult_return = resources->on_vkBindImageMemory2KHR(vkEnc, VK_SUCCESS, device, bindInfoCount, pBindInfos);
    return vkBindImageMemory2KHR_VkResult_return;
}
static VkResult dynCheck_entry_vkBindImageMemory2KHR(
    VkDevice device,
    uint32_t bindInfoCount,
    const VkBindImageMemoryInfo* pBindInfos)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_bind_memory2"))
    {
        sOnInvalidDynamicallyCheckedCall("vkBindImageMemory2KHR", "VK_KHR_bind_memory2");
    }
    AEMU_SCOPED_TRACE("vkBindImageMemory2KHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkBindImageMemory2KHR_VkResult_return = (VkResult)0;
    vkBindImageMemory2KHR_VkResult_return = resources->on_vkBindImageMemory2KHR(vkEnc, VK_SUCCESS, device, bindInfoCount, pBindInfos);
    return vkBindImageMemory2KHR_VkResult_return;
}
#endif
#ifdef VK_KHR_maintenance3
static void entry_vkGetDescriptorSetLayoutSupportKHR(
    VkDevice device,
    const VkDescriptorSetLayoutCreateInfo* pCreateInfo,
    VkDescriptorSetLayoutSupport* pSupport)
{
    AEMU_SCOPED_TRACE("vkGetDescriptorSetLayoutSupportKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetDescriptorSetLayoutSupportKHR(device, pCreateInfo, pSupport);
}
static void dynCheck_entry_vkGetDescriptorSetLayoutSupportKHR(
    VkDevice device,
    const VkDescriptorSetLayoutCreateInfo* pCreateInfo,
    VkDescriptorSetLayoutSupport* pSupport)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_KHR_maintenance3"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetDescriptorSetLayoutSupportKHR", "VK_KHR_maintenance3");
    }
    AEMU_SCOPED_TRACE("vkGetDescriptorSetLayoutSupportKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetDescriptorSetLayoutSupportKHR(device, pCreateInfo, pSupport);
}
#endif
#ifdef VK_KHR_draw_indirect_count
static void entry_vkCmdDrawIndirectCountKHR(
    VkCommandBuffer commandBuffer,
    VkBuffer buffer,
    VkDeviceSize offset,
    VkBuffer countBuffer,
    VkDeviceSize countBufferOffset,
    uint32_t maxDrawCount,
    uint32_t stride)
{
    AEMU_SCOPED_TRACE("vkCmdDrawIndirectCountKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdDrawIndirectCountKHR(commandBuffer, buffer, offset, countBuffer, countBufferOffset, maxDrawCount, stride);
}
static void entry_vkCmdDrawIndexedIndirectCountKHR(
    VkCommandBuffer commandBuffer,
    VkBuffer buffer,
    VkDeviceSize offset,
    VkBuffer countBuffer,
    VkDeviceSize countBufferOffset,
    uint32_t maxDrawCount,
    uint32_t stride)
{
    AEMU_SCOPED_TRACE("vkCmdDrawIndexedIndirectCountKHR");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdDrawIndexedIndirectCountKHR(commandBuffer, buffer, offset, countBuffer, countBufferOffset, maxDrawCount, stride);
}
#endif
#ifdef VK_KHR_8bit_storage
#endif
#ifdef VK_ANDROID_native_buffer
static VkResult entry_vkGetSwapchainGrallocUsageANDROID(
    VkDevice device,
    VkFormat format,
    VkImageUsageFlags imageUsage,
    int* grallocUsage)
{
    AEMU_SCOPED_TRACE("vkGetSwapchainGrallocUsageANDROID");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetSwapchainGrallocUsageANDROID_VkResult_return = (VkResult)0;
    vkGetSwapchainGrallocUsageANDROID_VkResult_return = vkEnc->vkGetSwapchainGrallocUsageANDROID(device, format, imageUsage, grallocUsage);
    return vkGetSwapchainGrallocUsageANDROID_VkResult_return;
}
static VkResult dynCheck_entry_vkGetSwapchainGrallocUsageANDROID(
    VkDevice device,
    VkFormat format,
    VkImageUsageFlags imageUsage,
    int* grallocUsage)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_ANDROID_native_buffer"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetSwapchainGrallocUsageANDROID", "VK_ANDROID_native_buffer");
    }
    AEMU_SCOPED_TRACE("vkGetSwapchainGrallocUsageANDROID");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetSwapchainGrallocUsageANDROID_VkResult_return = (VkResult)0;
    vkGetSwapchainGrallocUsageANDROID_VkResult_return = vkEnc->vkGetSwapchainGrallocUsageANDROID(device, format, imageUsage, grallocUsage);
    return vkGetSwapchainGrallocUsageANDROID_VkResult_return;
}
static VkResult entry_vkAcquireImageANDROID(
    VkDevice device,
    VkImage image,
    int nativeFenceFd,
    VkSemaphore semaphore,
    VkFence fence)
{
    AEMU_SCOPED_TRACE("vkAcquireImageANDROID");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkAcquireImageANDROID_VkResult_return = (VkResult)0;
    vkAcquireImageANDROID_VkResult_return = vkEnc->vkAcquireImageANDROID(device, image, nativeFenceFd, semaphore, fence);
    return vkAcquireImageANDROID_VkResult_return;
}
static VkResult dynCheck_entry_vkAcquireImageANDROID(
    VkDevice device,
    VkImage image,
    int nativeFenceFd,
    VkSemaphore semaphore,
    VkFence fence)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_ANDROID_native_buffer"))
    {
        sOnInvalidDynamicallyCheckedCall("vkAcquireImageANDROID", "VK_ANDROID_native_buffer");
    }
    AEMU_SCOPED_TRACE("vkAcquireImageANDROID");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkAcquireImageANDROID_VkResult_return = (VkResult)0;
    vkAcquireImageANDROID_VkResult_return = vkEnc->vkAcquireImageANDROID(device, image, nativeFenceFd, semaphore, fence);
    return vkAcquireImageANDROID_VkResult_return;
}
static VkResult entry_vkQueueSignalReleaseImageANDROID(
    VkQueue queue,
    uint32_t waitSemaphoreCount,
    const VkSemaphore* pWaitSemaphores,
    VkImage image,
    int* pNativeFenceFd)
{
    AEMU_SCOPED_TRACE("vkQueueSignalReleaseImageANDROID");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkQueueSignalReleaseImageANDROID_VkResult_return = (VkResult)0;
    vkQueueSignalReleaseImageANDROID_VkResult_return = vkEnc->vkQueueSignalReleaseImageANDROID(queue, waitSemaphoreCount, pWaitSemaphores, image, pNativeFenceFd);
    return vkQueueSignalReleaseImageANDROID_VkResult_return;
}
#endif
#ifdef VK_EXT_debug_report
static VkResult entry_vkCreateDebugReportCallbackEXT(
    VkInstance instance,
    const VkDebugReportCallbackCreateInfoEXT* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkDebugReportCallbackEXT* pCallback)
{
    AEMU_SCOPED_TRACE("vkCreateDebugReportCallbackEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateDebugReportCallbackEXT_VkResult_return = (VkResult)0;
    vkCreateDebugReportCallbackEXT_VkResult_return = vkEnc->vkCreateDebugReportCallbackEXT(instance, pCreateInfo, pAllocator, pCallback);
    return vkCreateDebugReportCallbackEXT_VkResult_return;
}
static void entry_vkDestroyDebugReportCallbackEXT(
    VkInstance instance,
    VkDebugReportCallbackEXT callback,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyDebugReportCallbackEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyDebugReportCallbackEXT(instance, callback, pAllocator);
}
static void entry_vkDebugReportMessageEXT(
    VkInstance instance,
    VkDebugReportFlagsEXT flags,
    VkDebugReportObjectTypeEXT objectType,
    uint64_t object,
    size_t location,
    int32_t messageCode,
    const char* pLayerPrefix,
    const char* pMessage)
{
    AEMU_SCOPED_TRACE("vkDebugReportMessageEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDebugReportMessageEXT(instance, flags, objectType, object, location, messageCode, pLayerPrefix, pMessage);
}
#endif
#ifdef VK_NV_glsl_shader
#endif
#ifdef VK_EXT_depth_range_unrestricted
#endif
#ifdef VK_IMG_filter_cubic
#endif
#ifdef VK_AMD_rasterization_order
#endif
#ifdef VK_AMD_shader_trinary_minmax
#endif
#ifdef VK_AMD_shader_explicit_vertex_parameter
#endif
#ifdef VK_EXT_debug_marker
static VkResult entry_vkDebugMarkerSetObjectTagEXT(
    VkDevice device,
    const VkDebugMarkerObjectTagInfoEXT* pTagInfo)
{
    AEMU_SCOPED_TRACE("vkDebugMarkerSetObjectTagEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkDebugMarkerSetObjectTagEXT_VkResult_return = (VkResult)0;
    vkDebugMarkerSetObjectTagEXT_VkResult_return = vkEnc->vkDebugMarkerSetObjectTagEXT(device, pTagInfo);
    return vkDebugMarkerSetObjectTagEXT_VkResult_return;
}
static VkResult dynCheck_entry_vkDebugMarkerSetObjectTagEXT(
    VkDevice device,
    const VkDebugMarkerObjectTagInfoEXT* pTagInfo)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_EXT_debug_marker"))
    {
        sOnInvalidDynamicallyCheckedCall("vkDebugMarkerSetObjectTagEXT", "VK_EXT_debug_marker");
    }
    AEMU_SCOPED_TRACE("vkDebugMarkerSetObjectTagEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkDebugMarkerSetObjectTagEXT_VkResult_return = (VkResult)0;
    vkDebugMarkerSetObjectTagEXT_VkResult_return = vkEnc->vkDebugMarkerSetObjectTagEXT(device, pTagInfo);
    return vkDebugMarkerSetObjectTagEXT_VkResult_return;
}
static VkResult entry_vkDebugMarkerSetObjectNameEXT(
    VkDevice device,
    const VkDebugMarkerObjectNameInfoEXT* pNameInfo)
{
    AEMU_SCOPED_TRACE("vkDebugMarkerSetObjectNameEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkDebugMarkerSetObjectNameEXT_VkResult_return = (VkResult)0;
    vkDebugMarkerSetObjectNameEXT_VkResult_return = vkEnc->vkDebugMarkerSetObjectNameEXT(device, pNameInfo);
    return vkDebugMarkerSetObjectNameEXT_VkResult_return;
}
static VkResult dynCheck_entry_vkDebugMarkerSetObjectNameEXT(
    VkDevice device,
    const VkDebugMarkerObjectNameInfoEXT* pNameInfo)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_EXT_debug_marker"))
    {
        sOnInvalidDynamicallyCheckedCall("vkDebugMarkerSetObjectNameEXT", "VK_EXT_debug_marker");
    }
    AEMU_SCOPED_TRACE("vkDebugMarkerSetObjectNameEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkDebugMarkerSetObjectNameEXT_VkResult_return = (VkResult)0;
    vkDebugMarkerSetObjectNameEXT_VkResult_return = vkEnc->vkDebugMarkerSetObjectNameEXT(device, pNameInfo);
    return vkDebugMarkerSetObjectNameEXT_VkResult_return;
}
static void entry_vkCmdDebugMarkerBeginEXT(
    VkCommandBuffer commandBuffer,
    const VkDebugMarkerMarkerInfoEXT* pMarkerInfo)
{
    AEMU_SCOPED_TRACE("vkCmdDebugMarkerBeginEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdDebugMarkerBeginEXT(commandBuffer, pMarkerInfo);
}
static void entry_vkCmdDebugMarkerEndEXT(
    VkCommandBuffer commandBuffer)
{
    AEMU_SCOPED_TRACE("vkCmdDebugMarkerEndEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdDebugMarkerEndEXT(commandBuffer);
}
static void entry_vkCmdDebugMarkerInsertEXT(
    VkCommandBuffer commandBuffer,
    const VkDebugMarkerMarkerInfoEXT* pMarkerInfo)
{
    AEMU_SCOPED_TRACE("vkCmdDebugMarkerInsertEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdDebugMarkerInsertEXT(commandBuffer, pMarkerInfo);
}
#endif
#ifdef VK_AMD_gcn_shader
#endif
#ifdef VK_NV_dedicated_allocation
#endif
#ifdef VK_AMD_draw_indirect_count
static void entry_vkCmdDrawIndirectCountAMD(
    VkCommandBuffer commandBuffer,
    VkBuffer buffer,
    VkDeviceSize offset,
    VkBuffer countBuffer,
    VkDeviceSize countBufferOffset,
    uint32_t maxDrawCount,
    uint32_t stride)
{
    AEMU_SCOPED_TRACE("vkCmdDrawIndirectCountAMD");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdDrawIndirectCountAMD(commandBuffer, buffer, offset, countBuffer, countBufferOffset, maxDrawCount, stride);
}
static void entry_vkCmdDrawIndexedIndirectCountAMD(
    VkCommandBuffer commandBuffer,
    VkBuffer buffer,
    VkDeviceSize offset,
    VkBuffer countBuffer,
    VkDeviceSize countBufferOffset,
    uint32_t maxDrawCount,
    uint32_t stride)
{
    AEMU_SCOPED_TRACE("vkCmdDrawIndexedIndirectCountAMD");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdDrawIndexedIndirectCountAMD(commandBuffer, buffer, offset, countBuffer, countBufferOffset, maxDrawCount, stride);
}
#endif
#ifdef VK_AMD_negative_viewport_height
#endif
#ifdef VK_AMD_gpu_shader_half_float
#endif
#ifdef VK_AMD_shader_ballot
#endif
#ifdef VK_AMD_texture_gather_bias_lod
#endif
#ifdef VK_AMD_shader_info
static VkResult entry_vkGetShaderInfoAMD(
    VkDevice device,
    VkPipeline pipeline,
    VkShaderStageFlagBits shaderStage,
    VkShaderInfoTypeAMD infoType,
    size_t* pInfoSize,
    void* pInfo)
{
    AEMU_SCOPED_TRACE("vkGetShaderInfoAMD");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetShaderInfoAMD_VkResult_return = (VkResult)0;
    vkGetShaderInfoAMD_VkResult_return = vkEnc->vkGetShaderInfoAMD(device, pipeline, shaderStage, infoType, pInfoSize, pInfo);
    return vkGetShaderInfoAMD_VkResult_return;
}
static VkResult dynCheck_entry_vkGetShaderInfoAMD(
    VkDevice device,
    VkPipeline pipeline,
    VkShaderStageFlagBits shaderStage,
    VkShaderInfoTypeAMD infoType,
    size_t* pInfoSize,
    void* pInfo)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_AMD_shader_info"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetShaderInfoAMD", "VK_AMD_shader_info");
    }
    AEMU_SCOPED_TRACE("vkGetShaderInfoAMD");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetShaderInfoAMD_VkResult_return = (VkResult)0;
    vkGetShaderInfoAMD_VkResult_return = vkEnc->vkGetShaderInfoAMD(device, pipeline, shaderStage, infoType, pInfoSize, pInfo);
    return vkGetShaderInfoAMD_VkResult_return;
}
#endif
#ifdef VK_AMD_shader_image_load_store_lod
#endif
#ifdef VK_IMG_format_pvrtc
#endif
#ifdef VK_NV_external_memory_capabilities
static VkResult entry_vkGetPhysicalDeviceExternalImageFormatPropertiesNV(
    VkPhysicalDevice physicalDevice,
    VkFormat format,
    VkImageType type,
    VkImageTiling tiling,
    VkImageUsageFlags usage,
    VkImageCreateFlags flags,
    VkExternalMemoryHandleTypeFlagsNV externalHandleType,
    VkExternalImageFormatPropertiesNV* pExternalImageFormatProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceExternalImageFormatPropertiesNV");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetPhysicalDeviceExternalImageFormatPropertiesNV_VkResult_return = (VkResult)0;
    vkGetPhysicalDeviceExternalImageFormatPropertiesNV_VkResult_return = vkEnc->vkGetPhysicalDeviceExternalImageFormatPropertiesNV(physicalDevice, format, type, tiling, usage, flags, externalHandleType, pExternalImageFormatProperties);
    return vkGetPhysicalDeviceExternalImageFormatPropertiesNV_VkResult_return;
}
#endif
#ifdef VK_NV_external_memory
#endif
#ifdef VK_NV_external_memory_win32
static VkResult entry_vkGetMemoryWin32HandleNV(
    VkDevice device,
    VkDeviceMemory memory,
    VkExternalMemoryHandleTypeFlagsNV handleType,
    HANDLE* pHandle)
{
    AEMU_SCOPED_TRACE("vkGetMemoryWin32HandleNV");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetMemoryWin32HandleNV_VkResult_return = (VkResult)0;
    vkGetMemoryWin32HandleNV_VkResult_return = vkEnc->vkGetMemoryWin32HandleNV(device, memory, handleType, pHandle);
    return vkGetMemoryWin32HandleNV_VkResult_return;
}
static VkResult dynCheck_entry_vkGetMemoryWin32HandleNV(
    VkDevice device,
    VkDeviceMemory memory,
    VkExternalMemoryHandleTypeFlagsNV handleType,
    HANDLE* pHandle)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_NV_external_memory_win32"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetMemoryWin32HandleNV", "VK_NV_external_memory_win32");
    }
    AEMU_SCOPED_TRACE("vkGetMemoryWin32HandleNV");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetMemoryWin32HandleNV_VkResult_return = (VkResult)0;
    vkGetMemoryWin32HandleNV_VkResult_return = vkEnc->vkGetMemoryWin32HandleNV(device, memory, handleType, pHandle);
    return vkGetMemoryWin32HandleNV_VkResult_return;
}
#endif
#ifdef VK_NV_win32_keyed_mutex
#endif
#ifdef VK_EXT_validation_flags
#endif
#ifdef VK_NN_vi_surface
static VkResult entry_vkCreateViSurfaceNN(
    VkInstance instance,
    const VkViSurfaceCreateInfoNN* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkSurfaceKHR* pSurface)
{
    AEMU_SCOPED_TRACE("vkCreateViSurfaceNN");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateViSurfaceNN_VkResult_return = (VkResult)0;
    vkCreateViSurfaceNN_VkResult_return = vkEnc->vkCreateViSurfaceNN(instance, pCreateInfo, pAllocator, pSurface);
    return vkCreateViSurfaceNN_VkResult_return;
}
#endif
#ifdef VK_EXT_shader_subgroup_ballot
#endif
#ifdef VK_EXT_shader_subgroup_vote
#endif
#ifdef VK_EXT_conditional_rendering
static void entry_vkCmdBeginConditionalRenderingEXT(
    VkCommandBuffer commandBuffer,
    const VkConditionalRenderingBeginInfoEXT* pConditionalRenderingBegin)
{
    AEMU_SCOPED_TRACE("vkCmdBeginConditionalRenderingEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdBeginConditionalRenderingEXT(commandBuffer, pConditionalRenderingBegin);
}
static void entry_vkCmdEndConditionalRenderingEXT(
    VkCommandBuffer commandBuffer)
{
    AEMU_SCOPED_TRACE("vkCmdEndConditionalRenderingEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdEndConditionalRenderingEXT(commandBuffer);
}
#endif
#ifdef VK_NVX_device_generated_commands
static void entry_vkCmdProcessCommandsNVX(
    VkCommandBuffer commandBuffer,
    const VkCmdProcessCommandsInfoNVX* pProcessCommandsInfo)
{
    AEMU_SCOPED_TRACE("vkCmdProcessCommandsNVX");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdProcessCommandsNVX(commandBuffer, pProcessCommandsInfo);
}
static void entry_vkCmdReserveSpaceForCommandsNVX(
    VkCommandBuffer commandBuffer,
    const VkCmdReserveSpaceForCommandsInfoNVX* pReserveSpaceInfo)
{
    AEMU_SCOPED_TRACE("vkCmdReserveSpaceForCommandsNVX");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdReserveSpaceForCommandsNVX(commandBuffer, pReserveSpaceInfo);
}
static VkResult entry_vkCreateIndirectCommandsLayoutNVX(
    VkDevice device,
    const VkIndirectCommandsLayoutCreateInfoNVX* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkIndirectCommandsLayoutNVX* pIndirectCommandsLayout)
{
    AEMU_SCOPED_TRACE("vkCreateIndirectCommandsLayoutNVX");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateIndirectCommandsLayoutNVX_VkResult_return = (VkResult)0;
    vkCreateIndirectCommandsLayoutNVX_VkResult_return = vkEnc->vkCreateIndirectCommandsLayoutNVX(device, pCreateInfo, pAllocator, pIndirectCommandsLayout);
    return vkCreateIndirectCommandsLayoutNVX_VkResult_return;
}
static VkResult dynCheck_entry_vkCreateIndirectCommandsLayoutNVX(
    VkDevice device,
    const VkIndirectCommandsLayoutCreateInfoNVX* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkIndirectCommandsLayoutNVX* pIndirectCommandsLayout)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_NVX_device_generated_commands"))
    {
        sOnInvalidDynamicallyCheckedCall("vkCreateIndirectCommandsLayoutNVX", "VK_NVX_device_generated_commands");
    }
    AEMU_SCOPED_TRACE("vkCreateIndirectCommandsLayoutNVX");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateIndirectCommandsLayoutNVX_VkResult_return = (VkResult)0;
    vkCreateIndirectCommandsLayoutNVX_VkResult_return = vkEnc->vkCreateIndirectCommandsLayoutNVX(device, pCreateInfo, pAllocator, pIndirectCommandsLayout);
    return vkCreateIndirectCommandsLayoutNVX_VkResult_return;
}
static void entry_vkDestroyIndirectCommandsLayoutNVX(
    VkDevice device,
    VkIndirectCommandsLayoutNVX indirectCommandsLayout,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyIndirectCommandsLayoutNVX");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyIndirectCommandsLayoutNVX(device, indirectCommandsLayout, pAllocator);
}
static void dynCheck_entry_vkDestroyIndirectCommandsLayoutNVX(
    VkDevice device,
    VkIndirectCommandsLayoutNVX indirectCommandsLayout,
    const VkAllocationCallbacks* pAllocator)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_NVX_device_generated_commands"))
    {
        sOnInvalidDynamicallyCheckedCall("vkDestroyIndirectCommandsLayoutNVX", "VK_NVX_device_generated_commands");
    }
    AEMU_SCOPED_TRACE("vkDestroyIndirectCommandsLayoutNVX");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyIndirectCommandsLayoutNVX(device, indirectCommandsLayout, pAllocator);
}
static VkResult entry_vkCreateObjectTableNVX(
    VkDevice device,
    const VkObjectTableCreateInfoNVX* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkObjectTableNVX* pObjectTable)
{
    AEMU_SCOPED_TRACE("vkCreateObjectTableNVX");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateObjectTableNVX_VkResult_return = (VkResult)0;
    vkCreateObjectTableNVX_VkResult_return = vkEnc->vkCreateObjectTableNVX(device, pCreateInfo, pAllocator, pObjectTable);
    return vkCreateObjectTableNVX_VkResult_return;
}
static VkResult dynCheck_entry_vkCreateObjectTableNVX(
    VkDevice device,
    const VkObjectTableCreateInfoNVX* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkObjectTableNVX* pObjectTable)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_NVX_device_generated_commands"))
    {
        sOnInvalidDynamicallyCheckedCall("vkCreateObjectTableNVX", "VK_NVX_device_generated_commands");
    }
    AEMU_SCOPED_TRACE("vkCreateObjectTableNVX");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateObjectTableNVX_VkResult_return = (VkResult)0;
    vkCreateObjectTableNVX_VkResult_return = vkEnc->vkCreateObjectTableNVX(device, pCreateInfo, pAllocator, pObjectTable);
    return vkCreateObjectTableNVX_VkResult_return;
}
static void entry_vkDestroyObjectTableNVX(
    VkDevice device,
    VkObjectTableNVX objectTable,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyObjectTableNVX");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyObjectTableNVX(device, objectTable, pAllocator);
}
static void dynCheck_entry_vkDestroyObjectTableNVX(
    VkDevice device,
    VkObjectTableNVX objectTable,
    const VkAllocationCallbacks* pAllocator)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_NVX_device_generated_commands"))
    {
        sOnInvalidDynamicallyCheckedCall("vkDestroyObjectTableNVX", "VK_NVX_device_generated_commands");
    }
    AEMU_SCOPED_TRACE("vkDestroyObjectTableNVX");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyObjectTableNVX(device, objectTable, pAllocator);
}
static VkResult entry_vkRegisterObjectsNVX(
    VkDevice device,
    VkObjectTableNVX objectTable,
    uint32_t objectCount,
    const VkObjectTableEntryNVX* const* ppObjectTableEntries,
    const uint32_t* pObjectIndices)
{
    AEMU_SCOPED_TRACE("vkRegisterObjectsNVX");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkRegisterObjectsNVX_VkResult_return = (VkResult)0;
    vkRegisterObjectsNVX_VkResult_return = vkEnc->vkRegisterObjectsNVX(device, objectTable, objectCount, ppObjectTableEntries, pObjectIndices);
    return vkRegisterObjectsNVX_VkResult_return;
}
static VkResult dynCheck_entry_vkRegisterObjectsNVX(
    VkDevice device,
    VkObjectTableNVX objectTable,
    uint32_t objectCount,
    const VkObjectTableEntryNVX* const* ppObjectTableEntries,
    const uint32_t* pObjectIndices)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_NVX_device_generated_commands"))
    {
        sOnInvalidDynamicallyCheckedCall("vkRegisterObjectsNVX", "VK_NVX_device_generated_commands");
    }
    AEMU_SCOPED_TRACE("vkRegisterObjectsNVX");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkRegisterObjectsNVX_VkResult_return = (VkResult)0;
    vkRegisterObjectsNVX_VkResult_return = vkEnc->vkRegisterObjectsNVX(device, objectTable, objectCount, ppObjectTableEntries, pObjectIndices);
    return vkRegisterObjectsNVX_VkResult_return;
}
static VkResult entry_vkUnregisterObjectsNVX(
    VkDevice device,
    VkObjectTableNVX objectTable,
    uint32_t objectCount,
    const VkObjectEntryTypeNVX* pObjectEntryTypes,
    const uint32_t* pObjectIndices)
{
    AEMU_SCOPED_TRACE("vkUnregisterObjectsNVX");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkUnregisterObjectsNVX_VkResult_return = (VkResult)0;
    vkUnregisterObjectsNVX_VkResult_return = vkEnc->vkUnregisterObjectsNVX(device, objectTable, objectCount, pObjectEntryTypes, pObjectIndices);
    return vkUnregisterObjectsNVX_VkResult_return;
}
static VkResult dynCheck_entry_vkUnregisterObjectsNVX(
    VkDevice device,
    VkObjectTableNVX objectTable,
    uint32_t objectCount,
    const VkObjectEntryTypeNVX* pObjectEntryTypes,
    const uint32_t* pObjectIndices)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_NVX_device_generated_commands"))
    {
        sOnInvalidDynamicallyCheckedCall("vkUnregisterObjectsNVX", "VK_NVX_device_generated_commands");
    }
    AEMU_SCOPED_TRACE("vkUnregisterObjectsNVX");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkUnregisterObjectsNVX_VkResult_return = (VkResult)0;
    vkUnregisterObjectsNVX_VkResult_return = vkEnc->vkUnregisterObjectsNVX(device, objectTable, objectCount, pObjectEntryTypes, pObjectIndices);
    return vkUnregisterObjectsNVX_VkResult_return;
}
static void entry_vkGetPhysicalDeviceGeneratedCommandsPropertiesNVX(
    VkPhysicalDevice physicalDevice,
    VkDeviceGeneratedCommandsFeaturesNVX* pFeatures,
    VkDeviceGeneratedCommandsLimitsNVX* pLimits)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceGeneratedCommandsPropertiesNVX");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceGeneratedCommandsPropertiesNVX(physicalDevice, pFeatures, pLimits);
}
#endif
#ifdef VK_NV_clip_space_w_scaling
static void entry_vkCmdSetViewportWScalingNV(
    VkCommandBuffer commandBuffer,
    uint32_t firstViewport,
    uint32_t viewportCount,
    const VkViewportWScalingNV* pViewportWScalings)
{
    AEMU_SCOPED_TRACE("vkCmdSetViewportWScalingNV");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdSetViewportWScalingNV(commandBuffer, firstViewport, viewportCount, pViewportWScalings);
}
#endif
#ifdef VK_EXT_direct_mode_display
static VkResult entry_vkReleaseDisplayEXT(
    VkPhysicalDevice physicalDevice,
    VkDisplayKHR display)
{
    AEMU_SCOPED_TRACE("vkReleaseDisplayEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkReleaseDisplayEXT_VkResult_return = (VkResult)0;
    vkReleaseDisplayEXT_VkResult_return = vkEnc->vkReleaseDisplayEXT(physicalDevice, display);
    return vkReleaseDisplayEXT_VkResult_return;
}
#endif
#ifdef VK_EXT_acquire_xlib_display
static VkResult entry_vkAcquireXlibDisplayEXT(
    VkPhysicalDevice physicalDevice,
    Display* dpy,
    VkDisplayKHR display)
{
    AEMU_SCOPED_TRACE("vkAcquireXlibDisplayEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkAcquireXlibDisplayEXT_VkResult_return = (VkResult)0;
    vkAcquireXlibDisplayEXT_VkResult_return = vkEnc->vkAcquireXlibDisplayEXT(physicalDevice, dpy, display);
    return vkAcquireXlibDisplayEXT_VkResult_return;
}
static VkResult entry_vkGetRandROutputDisplayEXT(
    VkPhysicalDevice physicalDevice,
    Display* dpy,
    RROutput rrOutput,
    VkDisplayKHR* pDisplay)
{
    AEMU_SCOPED_TRACE("vkGetRandROutputDisplayEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetRandROutputDisplayEXT_VkResult_return = (VkResult)0;
    vkGetRandROutputDisplayEXT_VkResult_return = vkEnc->vkGetRandROutputDisplayEXT(physicalDevice, dpy, rrOutput, pDisplay);
    return vkGetRandROutputDisplayEXT_VkResult_return;
}
#endif
#ifdef VK_EXT_display_surface_counter
static VkResult entry_vkGetPhysicalDeviceSurfaceCapabilities2EXT(
    VkPhysicalDevice physicalDevice,
    VkSurfaceKHR surface,
    VkSurfaceCapabilities2EXT* pSurfaceCapabilities)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceSurfaceCapabilities2EXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetPhysicalDeviceSurfaceCapabilities2EXT_VkResult_return = (VkResult)0;
    vkGetPhysicalDeviceSurfaceCapabilities2EXT_VkResult_return = vkEnc->vkGetPhysicalDeviceSurfaceCapabilities2EXT(physicalDevice, surface, pSurfaceCapabilities);
    return vkGetPhysicalDeviceSurfaceCapabilities2EXT_VkResult_return;
}
#endif
#ifdef VK_EXT_display_control
static VkResult entry_vkDisplayPowerControlEXT(
    VkDevice device,
    VkDisplayKHR display,
    const VkDisplayPowerInfoEXT* pDisplayPowerInfo)
{
    AEMU_SCOPED_TRACE("vkDisplayPowerControlEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkDisplayPowerControlEXT_VkResult_return = (VkResult)0;
    vkDisplayPowerControlEXT_VkResult_return = vkEnc->vkDisplayPowerControlEXT(device, display, pDisplayPowerInfo);
    return vkDisplayPowerControlEXT_VkResult_return;
}
static VkResult dynCheck_entry_vkDisplayPowerControlEXT(
    VkDevice device,
    VkDisplayKHR display,
    const VkDisplayPowerInfoEXT* pDisplayPowerInfo)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_EXT_display_control"))
    {
        sOnInvalidDynamicallyCheckedCall("vkDisplayPowerControlEXT", "VK_EXT_display_control");
    }
    AEMU_SCOPED_TRACE("vkDisplayPowerControlEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkDisplayPowerControlEXT_VkResult_return = (VkResult)0;
    vkDisplayPowerControlEXT_VkResult_return = vkEnc->vkDisplayPowerControlEXT(device, display, pDisplayPowerInfo);
    return vkDisplayPowerControlEXT_VkResult_return;
}
static VkResult entry_vkRegisterDeviceEventEXT(
    VkDevice device,
    const VkDeviceEventInfoEXT* pDeviceEventInfo,
    const VkAllocationCallbacks* pAllocator,
    VkFence* pFence)
{
    AEMU_SCOPED_TRACE("vkRegisterDeviceEventEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkRegisterDeviceEventEXT_VkResult_return = (VkResult)0;
    vkRegisterDeviceEventEXT_VkResult_return = vkEnc->vkRegisterDeviceEventEXT(device, pDeviceEventInfo, pAllocator, pFence);
    return vkRegisterDeviceEventEXT_VkResult_return;
}
static VkResult dynCheck_entry_vkRegisterDeviceEventEXT(
    VkDevice device,
    const VkDeviceEventInfoEXT* pDeviceEventInfo,
    const VkAllocationCallbacks* pAllocator,
    VkFence* pFence)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_EXT_display_control"))
    {
        sOnInvalidDynamicallyCheckedCall("vkRegisterDeviceEventEXT", "VK_EXT_display_control");
    }
    AEMU_SCOPED_TRACE("vkRegisterDeviceEventEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkRegisterDeviceEventEXT_VkResult_return = (VkResult)0;
    vkRegisterDeviceEventEXT_VkResult_return = vkEnc->vkRegisterDeviceEventEXT(device, pDeviceEventInfo, pAllocator, pFence);
    return vkRegisterDeviceEventEXT_VkResult_return;
}
static VkResult entry_vkRegisterDisplayEventEXT(
    VkDevice device,
    VkDisplayKHR display,
    const VkDisplayEventInfoEXT* pDisplayEventInfo,
    const VkAllocationCallbacks* pAllocator,
    VkFence* pFence)
{
    AEMU_SCOPED_TRACE("vkRegisterDisplayEventEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkRegisterDisplayEventEXT_VkResult_return = (VkResult)0;
    vkRegisterDisplayEventEXT_VkResult_return = vkEnc->vkRegisterDisplayEventEXT(device, display, pDisplayEventInfo, pAllocator, pFence);
    return vkRegisterDisplayEventEXT_VkResult_return;
}
static VkResult dynCheck_entry_vkRegisterDisplayEventEXT(
    VkDevice device,
    VkDisplayKHR display,
    const VkDisplayEventInfoEXT* pDisplayEventInfo,
    const VkAllocationCallbacks* pAllocator,
    VkFence* pFence)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_EXT_display_control"))
    {
        sOnInvalidDynamicallyCheckedCall("vkRegisterDisplayEventEXT", "VK_EXT_display_control");
    }
    AEMU_SCOPED_TRACE("vkRegisterDisplayEventEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkRegisterDisplayEventEXT_VkResult_return = (VkResult)0;
    vkRegisterDisplayEventEXT_VkResult_return = vkEnc->vkRegisterDisplayEventEXT(device, display, pDisplayEventInfo, pAllocator, pFence);
    return vkRegisterDisplayEventEXT_VkResult_return;
}
static VkResult entry_vkGetSwapchainCounterEXT(
    VkDevice device,
    VkSwapchainKHR swapchain,
    VkSurfaceCounterFlagBitsEXT counter,
    uint64_t* pCounterValue)
{
    AEMU_SCOPED_TRACE("vkGetSwapchainCounterEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetSwapchainCounterEXT_VkResult_return = (VkResult)0;
    vkGetSwapchainCounterEXT_VkResult_return = vkEnc->vkGetSwapchainCounterEXT(device, swapchain, counter, pCounterValue);
    return vkGetSwapchainCounterEXT_VkResult_return;
}
static VkResult dynCheck_entry_vkGetSwapchainCounterEXT(
    VkDevice device,
    VkSwapchainKHR swapchain,
    VkSurfaceCounterFlagBitsEXT counter,
    uint64_t* pCounterValue)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_EXT_display_control"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetSwapchainCounterEXT", "VK_EXT_display_control");
    }
    AEMU_SCOPED_TRACE("vkGetSwapchainCounterEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetSwapchainCounterEXT_VkResult_return = (VkResult)0;
    vkGetSwapchainCounterEXT_VkResult_return = vkEnc->vkGetSwapchainCounterEXT(device, swapchain, counter, pCounterValue);
    return vkGetSwapchainCounterEXT_VkResult_return;
}
#endif
#ifdef VK_GOOGLE_display_timing
static VkResult entry_vkGetRefreshCycleDurationGOOGLE(
    VkDevice device,
    VkSwapchainKHR swapchain,
    VkRefreshCycleDurationGOOGLE* pDisplayTimingProperties)
{
    AEMU_SCOPED_TRACE("vkGetRefreshCycleDurationGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetRefreshCycleDurationGOOGLE_VkResult_return = (VkResult)0;
    vkGetRefreshCycleDurationGOOGLE_VkResult_return = vkEnc->vkGetRefreshCycleDurationGOOGLE(device, swapchain, pDisplayTimingProperties);
    return vkGetRefreshCycleDurationGOOGLE_VkResult_return;
}
static VkResult dynCheck_entry_vkGetRefreshCycleDurationGOOGLE(
    VkDevice device,
    VkSwapchainKHR swapchain,
    VkRefreshCycleDurationGOOGLE* pDisplayTimingProperties)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_GOOGLE_display_timing"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetRefreshCycleDurationGOOGLE", "VK_GOOGLE_display_timing");
    }
    AEMU_SCOPED_TRACE("vkGetRefreshCycleDurationGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetRefreshCycleDurationGOOGLE_VkResult_return = (VkResult)0;
    vkGetRefreshCycleDurationGOOGLE_VkResult_return = vkEnc->vkGetRefreshCycleDurationGOOGLE(device, swapchain, pDisplayTimingProperties);
    return vkGetRefreshCycleDurationGOOGLE_VkResult_return;
}
static VkResult entry_vkGetPastPresentationTimingGOOGLE(
    VkDevice device,
    VkSwapchainKHR swapchain,
    uint32_t* pPresentationTimingCount,
    VkPastPresentationTimingGOOGLE* pPresentationTimings)
{
    AEMU_SCOPED_TRACE("vkGetPastPresentationTimingGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetPastPresentationTimingGOOGLE_VkResult_return = (VkResult)0;
    vkGetPastPresentationTimingGOOGLE_VkResult_return = vkEnc->vkGetPastPresentationTimingGOOGLE(device, swapchain, pPresentationTimingCount, pPresentationTimings);
    return vkGetPastPresentationTimingGOOGLE_VkResult_return;
}
static VkResult dynCheck_entry_vkGetPastPresentationTimingGOOGLE(
    VkDevice device,
    VkSwapchainKHR swapchain,
    uint32_t* pPresentationTimingCount,
    VkPastPresentationTimingGOOGLE* pPresentationTimings)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_GOOGLE_display_timing"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetPastPresentationTimingGOOGLE", "VK_GOOGLE_display_timing");
    }
    AEMU_SCOPED_TRACE("vkGetPastPresentationTimingGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetPastPresentationTimingGOOGLE_VkResult_return = (VkResult)0;
    vkGetPastPresentationTimingGOOGLE_VkResult_return = vkEnc->vkGetPastPresentationTimingGOOGLE(device, swapchain, pPresentationTimingCount, pPresentationTimings);
    return vkGetPastPresentationTimingGOOGLE_VkResult_return;
}
#endif
#ifdef VK_NV_sample_mask_override_coverage
#endif
#ifdef VK_NV_geometry_shader_passthrough
#endif
#ifdef VK_NV_viewport_array2
#endif
#ifdef VK_NVX_multiview_per_view_attributes
#endif
#ifdef VK_NV_viewport_swizzle
#endif
#ifdef VK_EXT_discard_rectangles
static void entry_vkCmdSetDiscardRectangleEXT(
    VkCommandBuffer commandBuffer,
    uint32_t firstDiscardRectangle,
    uint32_t discardRectangleCount,
    const VkRect2D* pDiscardRectangles)
{
    AEMU_SCOPED_TRACE("vkCmdSetDiscardRectangleEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdSetDiscardRectangleEXT(commandBuffer, firstDiscardRectangle, discardRectangleCount, pDiscardRectangles);
}
#endif
#ifdef VK_EXT_conservative_rasterization
#endif
#ifdef VK_EXT_swapchain_colorspace
#endif
#ifdef VK_EXT_hdr_metadata
static void entry_vkSetHdrMetadataEXT(
    VkDevice device,
    uint32_t swapchainCount,
    const VkSwapchainKHR* pSwapchains,
    const VkHdrMetadataEXT* pMetadata)
{
    AEMU_SCOPED_TRACE("vkSetHdrMetadataEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkSetHdrMetadataEXT(device, swapchainCount, pSwapchains, pMetadata);
}
static void dynCheck_entry_vkSetHdrMetadataEXT(
    VkDevice device,
    uint32_t swapchainCount,
    const VkSwapchainKHR* pSwapchains,
    const VkHdrMetadataEXT* pMetadata)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_EXT_hdr_metadata"))
    {
        sOnInvalidDynamicallyCheckedCall("vkSetHdrMetadataEXT", "VK_EXT_hdr_metadata");
    }
    AEMU_SCOPED_TRACE("vkSetHdrMetadataEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkSetHdrMetadataEXT(device, swapchainCount, pSwapchains, pMetadata);
}
#endif
#ifdef VK_MVK_ios_surface
static VkResult entry_vkCreateIOSSurfaceMVK(
    VkInstance instance,
    const VkIOSSurfaceCreateInfoMVK* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkSurfaceKHR* pSurface)
{
    AEMU_SCOPED_TRACE("vkCreateIOSSurfaceMVK");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateIOSSurfaceMVK_VkResult_return = (VkResult)0;
    vkCreateIOSSurfaceMVK_VkResult_return = vkEnc->vkCreateIOSSurfaceMVK(instance, pCreateInfo, pAllocator, pSurface);
    return vkCreateIOSSurfaceMVK_VkResult_return;
}
#endif
#ifdef VK_MVK_macos_surface
static VkResult entry_vkCreateMacOSSurfaceMVK(
    VkInstance instance,
    const VkMacOSSurfaceCreateInfoMVK* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkSurfaceKHR* pSurface)
{
    AEMU_SCOPED_TRACE("vkCreateMacOSSurfaceMVK");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateMacOSSurfaceMVK_VkResult_return = (VkResult)0;
    vkCreateMacOSSurfaceMVK_VkResult_return = vkEnc->vkCreateMacOSSurfaceMVK(instance, pCreateInfo, pAllocator, pSurface);
    return vkCreateMacOSSurfaceMVK_VkResult_return;
}
#endif
#ifdef VK_EXT_external_memory_dma_buf
#endif
#ifdef VK_EXT_queue_family_foreign
#endif
#ifdef VK_EXT_debug_utils
static VkResult entry_vkSetDebugUtilsObjectNameEXT(
    VkDevice device,
    const VkDebugUtilsObjectNameInfoEXT* pNameInfo)
{
    AEMU_SCOPED_TRACE("vkSetDebugUtilsObjectNameEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkSetDebugUtilsObjectNameEXT_VkResult_return = (VkResult)0;
    vkSetDebugUtilsObjectNameEXT_VkResult_return = vkEnc->vkSetDebugUtilsObjectNameEXT(device, pNameInfo);
    return vkSetDebugUtilsObjectNameEXT_VkResult_return;
}
static VkResult dynCheck_entry_vkSetDebugUtilsObjectNameEXT(
    VkDevice device,
    const VkDebugUtilsObjectNameInfoEXT* pNameInfo)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_EXT_debug_utils"))
    {
        sOnInvalidDynamicallyCheckedCall("vkSetDebugUtilsObjectNameEXT", "VK_EXT_debug_utils");
    }
    AEMU_SCOPED_TRACE("vkSetDebugUtilsObjectNameEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkSetDebugUtilsObjectNameEXT_VkResult_return = (VkResult)0;
    vkSetDebugUtilsObjectNameEXT_VkResult_return = vkEnc->vkSetDebugUtilsObjectNameEXT(device, pNameInfo);
    return vkSetDebugUtilsObjectNameEXT_VkResult_return;
}
static VkResult entry_vkSetDebugUtilsObjectTagEXT(
    VkDevice device,
    const VkDebugUtilsObjectTagInfoEXT* pTagInfo)
{
    AEMU_SCOPED_TRACE("vkSetDebugUtilsObjectTagEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkSetDebugUtilsObjectTagEXT_VkResult_return = (VkResult)0;
    vkSetDebugUtilsObjectTagEXT_VkResult_return = vkEnc->vkSetDebugUtilsObjectTagEXT(device, pTagInfo);
    return vkSetDebugUtilsObjectTagEXT_VkResult_return;
}
static VkResult dynCheck_entry_vkSetDebugUtilsObjectTagEXT(
    VkDevice device,
    const VkDebugUtilsObjectTagInfoEXT* pTagInfo)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_EXT_debug_utils"))
    {
        sOnInvalidDynamicallyCheckedCall("vkSetDebugUtilsObjectTagEXT", "VK_EXT_debug_utils");
    }
    AEMU_SCOPED_TRACE("vkSetDebugUtilsObjectTagEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkSetDebugUtilsObjectTagEXT_VkResult_return = (VkResult)0;
    vkSetDebugUtilsObjectTagEXT_VkResult_return = vkEnc->vkSetDebugUtilsObjectTagEXT(device, pTagInfo);
    return vkSetDebugUtilsObjectTagEXT_VkResult_return;
}
static void entry_vkQueueBeginDebugUtilsLabelEXT(
    VkQueue queue,
    const VkDebugUtilsLabelEXT* pLabelInfo)
{
    AEMU_SCOPED_TRACE("vkQueueBeginDebugUtilsLabelEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkQueueBeginDebugUtilsLabelEXT(queue, pLabelInfo);
}
static void entry_vkQueueEndDebugUtilsLabelEXT(
    VkQueue queue)
{
    AEMU_SCOPED_TRACE("vkQueueEndDebugUtilsLabelEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkQueueEndDebugUtilsLabelEXT(queue);
}
static void entry_vkQueueInsertDebugUtilsLabelEXT(
    VkQueue queue,
    const VkDebugUtilsLabelEXT* pLabelInfo)
{
    AEMU_SCOPED_TRACE("vkQueueInsertDebugUtilsLabelEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkQueueInsertDebugUtilsLabelEXT(queue, pLabelInfo);
}
static void entry_vkCmdBeginDebugUtilsLabelEXT(
    VkCommandBuffer commandBuffer,
    const VkDebugUtilsLabelEXT* pLabelInfo)
{
    AEMU_SCOPED_TRACE("vkCmdBeginDebugUtilsLabelEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdBeginDebugUtilsLabelEXT(commandBuffer, pLabelInfo);
}
static void entry_vkCmdEndDebugUtilsLabelEXT(
    VkCommandBuffer commandBuffer)
{
    AEMU_SCOPED_TRACE("vkCmdEndDebugUtilsLabelEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdEndDebugUtilsLabelEXT(commandBuffer);
}
static void entry_vkCmdInsertDebugUtilsLabelEXT(
    VkCommandBuffer commandBuffer,
    const VkDebugUtilsLabelEXT* pLabelInfo)
{
    AEMU_SCOPED_TRACE("vkCmdInsertDebugUtilsLabelEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdInsertDebugUtilsLabelEXT(commandBuffer, pLabelInfo);
}
static VkResult entry_vkCreateDebugUtilsMessengerEXT(
    VkInstance instance,
    const VkDebugUtilsMessengerCreateInfoEXT* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkDebugUtilsMessengerEXT* pMessenger)
{
    AEMU_SCOPED_TRACE("vkCreateDebugUtilsMessengerEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateDebugUtilsMessengerEXT_VkResult_return = (VkResult)0;
    vkCreateDebugUtilsMessengerEXT_VkResult_return = vkEnc->vkCreateDebugUtilsMessengerEXT(instance, pCreateInfo, pAllocator, pMessenger);
    return vkCreateDebugUtilsMessengerEXT_VkResult_return;
}
static void entry_vkDestroyDebugUtilsMessengerEXT(
    VkInstance instance,
    VkDebugUtilsMessengerEXT messenger,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyDebugUtilsMessengerEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyDebugUtilsMessengerEXT(instance, messenger, pAllocator);
}
static void entry_vkSubmitDebugUtilsMessageEXT(
    VkInstance instance,
    VkDebugUtilsMessageSeverityFlagBitsEXT messageSeverity,
    VkDebugUtilsMessageTypeFlagsEXT messageTypes,
    const VkDebugUtilsMessengerCallbackDataEXT* pCallbackData)
{
    AEMU_SCOPED_TRACE("vkSubmitDebugUtilsMessageEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkSubmitDebugUtilsMessageEXT(instance, messageSeverity, messageTypes, pCallbackData);
}
#endif
#ifdef VK_ANDROID_external_memory_android_hardware_buffer
static VkResult entry_vkGetAndroidHardwareBufferPropertiesANDROID(
    VkDevice device,
    const AHardwareBuffer* buffer,
    VkAndroidHardwareBufferPropertiesANDROID* pProperties)
{
    AEMU_SCOPED_TRACE("vkGetAndroidHardwareBufferPropertiesANDROID");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetAndroidHardwareBufferPropertiesANDROID_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkGetAndroidHardwareBufferPropertiesANDROID_VkResult_return = resources->on_vkGetAndroidHardwareBufferPropertiesANDROID(vkEnc, VK_SUCCESS, device, buffer, pProperties);
    return vkGetAndroidHardwareBufferPropertiesANDROID_VkResult_return;
}
static VkResult dynCheck_entry_vkGetAndroidHardwareBufferPropertiesANDROID(
    VkDevice device,
    const AHardwareBuffer* buffer,
    VkAndroidHardwareBufferPropertiesANDROID* pProperties)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_ANDROID_external_memory_android_hardware_buffer"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetAndroidHardwareBufferPropertiesANDROID", "VK_ANDROID_external_memory_android_hardware_buffer");
    }
    AEMU_SCOPED_TRACE("vkGetAndroidHardwareBufferPropertiesANDROID");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetAndroidHardwareBufferPropertiesANDROID_VkResult_return = (VkResult)0;
    vkGetAndroidHardwareBufferPropertiesANDROID_VkResult_return = resources->on_vkGetAndroidHardwareBufferPropertiesANDROID(vkEnc, VK_SUCCESS, device, buffer, pProperties);
    return vkGetAndroidHardwareBufferPropertiesANDROID_VkResult_return;
}
static VkResult entry_vkGetMemoryAndroidHardwareBufferANDROID(
    VkDevice device,
    const VkMemoryGetAndroidHardwareBufferInfoANDROID* pInfo,
    AHardwareBuffer** pBuffer)
{
    AEMU_SCOPED_TRACE("vkGetMemoryAndroidHardwareBufferANDROID");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetMemoryAndroidHardwareBufferANDROID_VkResult_return = (VkResult)0;
    auto resources = ResourceTracker::get();
    vkGetMemoryAndroidHardwareBufferANDROID_VkResult_return = resources->on_vkGetMemoryAndroidHardwareBufferANDROID(vkEnc, VK_SUCCESS, device, pInfo, pBuffer);
    return vkGetMemoryAndroidHardwareBufferANDROID_VkResult_return;
}
static VkResult dynCheck_entry_vkGetMemoryAndroidHardwareBufferANDROID(
    VkDevice device,
    const VkMemoryGetAndroidHardwareBufferInfoANDROID* pInfo,
    AHardwareBuffer** pBuffer)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_ANDROID_external_memory_android_hardware_buffer"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetMemoryAndroidHardwareBufferANDROID", "VK_ANDROID_external_memory_android_hardware_buffer");
    }
    AEMU_SCOPED_TRACE("vkGetMemoryAndroidHardwareBufferANDROID");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetMemoryAndroidHardwareBufferANDROID_VkResult_return = (VkResult)0;
    vkGetMemoryAndroidHardwareBufferANDROID_VkResult_return = resources->on_vkGetMemoryAndroidHardwareBufferANDROID(vkEnc, VK_SUCCESS, device, pInfo, pBuffer);
    return vkGetMemoryAndroidHardwareBufferANDROID_VkResult_return;
}
#endif
#ifdef VK_EXT_sampler_filter_minmax
#endif
#ifdef VK_AMD_gpu_shader_int16
#endif
#ifdef VK_AMD_mixed_attachment_samples
#endif
#ifdef VK_AMD_shader_fragment_mask
#endif
#ifdef VK_EXT_shader_stencil_export
#endif
#ifdef VK_EXT_sample_locations
static void entry_vkCmdSetSampleLocationsEXT(
    VkCommandBuffer commandBuffer,
    const VkSampleLocationsInfoEXT* pSampleLocationsInfo)
{
    AEMU_SCOPED_TRACE("vkCmdSetSampleLocationsEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdSetSampleLocationsEXT(commandBuffer, pSampleLocationsInfo);
}
static void entry_vkGetPhysicalDeviceMultisamplePropertiesEXT(
    VkPhysicalDevice physicalDevice,
    VkSampleCountFlagBits samples,
    VkMultisamplePropertiesEXT* pMultisampleProperties)
{
    AEMU_SCOPED_TRACE("vkGetPhysicalDeviceMultisamplePropertiesEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetPhysicalDeviceMultisamplePropertiesEXT(physicalDevice, samples, pMultisampleProperties);
}
#endif
#ifdef VK_EXT_blend_operation_advanced
#endif
#ifdef VK_NV_fragment_coverage_to_color
#endif
#ifdef VK_NV_framebuffer_mixed_samples
#endif
#ifdef VK_NV_fill_rectangle
#endif
#ifdef VK_EXT_post_depth_coverage
#endif
#ifdef VK_EXT_validation_cache
static VkResult entry_vkCreateValidationCacheEXT(
    VkDevice device,
    const VkValidationCacheCreateInfoEXT* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkValidationCacheEXT* pValidationCache)
{
    AEMU_SCOPED_TRACE("vkCreateValidationCacheEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateValidationCacheEXT_VkResult_return = (VkResult)0;
    vkCreateValidationCacheEXT_VkResult_return = vkEnc->vkCreateValidationCacheEXT(device, pCreateInfo, pAllocator, pValidationCache);
    return vkCreateValidationCacheEXT_VkResult_return;
}
static VkResult dynCheck_entry_vkCreateValidationCacheEXT(
    VkDevice device,
    const VkValidationCacheCreateInfoEXT* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkValidationCacheEXT* pValidationCache)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_EXT_validation_cache"))
    {
        sOnInvalidDynamicallyCheckedCall("vkCreateValidationCacheEXT", "VK_EXT_validation_cache");
    }
    AEMU_SCOPED_TRACE("vkCreateValidationCacheEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateValidationCacheEXT_VkResult_return = (VkResult)0;
    vkCreateValidationCacheEXT_VkResult_return = vkEnc->vkCreateValidationCacheEXT(device, pCreateInfo, pAllocator, pValidationCache);
    return vkCreateValidationCacheEXT_VkResult_return;
}
static void entry_vkDestroyValidationCacheEXT(
    VkDevice device,
    VkValidationCacheEXT validationCache,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkDestroyValidationCacheEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyValidationCacheEXT(device, validationCache, pAllocator);
}
static void dynCheck_entry_vkDestroyValidationCacheEXT(
    VkDevice device,
    VkValidationCacheEXT validationCache,
    const VkAllocationCallbacks* pAllocator)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_EXT_validation_cache"))
    {
        sOnInvalidDynamicallyCheckedCall("vkDestroyValidationCacheEXT", "VK_EXT_validation_cache");
    }
    AEMU_SCOPED_TRACE("vkDestroyValidationCacheEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkDestroyValidationCacheEXT(device, validationCache, pAllocator);
}
static VkResult entry_vkMergeValidationCachesEXT(
    VkDevice device,
    VkValidationCacheEXT dstCache,
    uint32_t srcCacheCount,
    const VkValidationCacheEXT* pSrcCaches)
{
    AEMU_SCOPED_TRACE("vkMergeValidationCachesEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkMergeValidationCachesEXT_VkResult_return = (VkResult)0;
    vkMergeValidationCachesEXT_VkResult_return = vkEnc->vkMergeValidationCachesEXT(device, dstCache, srcCacheCount, pSrcCaches);
    return vkMergeValidationCachesEXT_VkResult_return;
}
static VkResult dynCheck_entry_vkMergeValidationCachesEXT(
    VkDevice device,
    VkValidationCacheEXT dstCache,
    uint32_t srcCacheCount,
    const VkValidationCacheEXT* pSrcCaches)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_EXT_validation_cache"))
    {
        sOnInvalidDynamicallyCheckedCall("vkMergeValidationCachesEXT", "VK_EXT_validation_cache");
    }
    AEMU_SCOPED_TRACE("vkMergeValidationCachesEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkMergeValidationCachesEXT_VkResult_return = (VkResult)0;
    vkMergeValidationCachesEXT_VkResult_return = vkEnc->vkMergeValidationCachesEXT(device, dstCache, srcCacheCount, pSrcCaches);
    return vkMergeValidationCachesEXT_VkResult_return;
}
static VkResult entry_vkGetValidationCacheDataEXT(
    VkDevice device,
    VkValidationCacheEXT validationCache,
    size_t* pDataSize,
    void* pData)
{
    AEMU_SCOPED_TRACE("vkGetValidationCacheDataEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetValidationCacheDataEXT_VkResult_return = (VkResult)0;
    vkGetValidationCacheDataEXT_VkResult_return = vkEnc->vkGetValidationCacheDataEXT(device, validationCache, pDataSize, pData);
    return vkGetValidationCacheDataEXT_VkResult_return;
}
static VkResult dynCheck_entry_vkGetValidationCacheDataEXT(
    VkDevice device,
    VkValidationCacheEXT validationCache,
    size_t* pDataSize,
    void* pData)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_EXT_validation_cache"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetValidationCacheDataEXT", "VK_EXT_validation_cache");
    }
    AEMU_SCOPED_TRACE("vkGetValidationCacheDataEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetValidationCacheDataEXT_VkResult_return = (VkResult)0;
    vkGetValidationCacheDataEXT_VkResult_return = vkEnc->vkGetValidationCacheDataEXT(device, validationCache, pDataSize, pData);
    return vkGetValidationCacheDataEXT_VkResult_return;
}
#endif
#ifdef VK_EXT_descriptor_indexing
#endif
#ifdef VK_EXT_shader_viewport_index_layer
#endif
#ifdef VK_EXT_global_priority
#endif
#ifdef VK_EXT_external_memory_host
static VkResult entry_vkGetMemoryHostPointerPropertiesEXT(
    VkDevice device,
    VkExternalMemoryHandleTypeFlagBits handleType,
    const void* pHostPointer,
    VkMemoryHostPointerPropertiesEXT* pMemoryHostPointerProperties)
{
    AEMU_SCOPED_TRACE("vkGetMemoryHostPointerPropertiesEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetMemoryHostPointerPropertiesEXT_VkResult_return = (VkResult)0;
    vkGetMemoryHostPointerPropertiesEXT_VkResult_return = vkEnc->vkGetMemoryHostPointerPropertiesEXT(device, handleType, pHostPointer, pMemoryHostPointerProperties);
    return vkGetMemoryHostPointerPropertiesEXT_VkResult_return;
}
static VkResult dynCheck_entry_vkGetMemoryHostPointerPropertiesEXT(
    VkDevice device,
    VkExternalMemoryHandleTypeFlagBits handleType,
    const void* pHostPointer,
    VkMemoryHostPointerPropertiesEXT* pMemoryHostPointerProperties)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_EXT_external_memory_host"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetMemoryHostPointerPropertiesEXT", "VK_EXT_external_memory_host");
    }
    AEMU_SCOPED_TRACE("vkGetMemoryHostPointerPropertiesEXT");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetMemoryHostPointerPropertiesEXT_VkResult_return = (VkResult)0;
    vkGetMemoryHostPointerPropertiesEXT_VkResult_return = vkEnc->vkGetMemoryHostPointerPropertiesEXT(device, handleType, pHostPointer, pMemoryHostPointerProperties);
    return vkGetMemoryHostPointerPropertiesEXT_VkResult_return;
}
#endif
#ifdef VK_AMD_buffer_marker
static void entry_vkCmdWriteBufferMarkerAMD(
    VkCommandBuffer commandBuffer,
    VkPipelineStageFlagBits pipelineStage,
    VkBuffer dstBuffer,
    VkDeviceSize dstOffset,
    uint32_t marker)
{
    AEMU_SCOPED_TRACE("vkCmdWriteBufferMarkerAMD");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdWriteBufferMarkerAMD(commandBuffer, pipelineStage, dstBuffer, dstOffset, marker);
}
#endif
#ifdef VK_AMD_shader_core_properties
#endif
#ifdef VK_EXT_vertex_attribute_divisor
#endif
#ifdef VK_NV_shader_subgroup_partitioned
#endif
#ifdef VK_NV_device_diagnostic_checkpoints
static void entry_vkCmdSetCheckpointNV(
    VkCommandBuffer commandBuffer,
    const void* pCheckpointMarker)
{
    AEMU_SCOPED_TRACE("vkCmdSetCheckpointNV");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCmdSetCheckpointNV(commandBuffer, pCheckpointMarker);
}
static void entry_vkGetQueueCheckpointDataNV(
    VkQueue queue,
    uint32_t* pCheckpointDataCount,
    VkCheckpointDataNV* pCheckpointData)
{
    AEMU_SCOPED_TRACE("vkGetQueueCheckpointDataNV");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkGetQueueCheckpointDataNV(queue, pCheckpointDataCount, pCheckpointData);
}
#endif
#ifdef VK_GOOGLE_address_space
static VkResult entry_vkMapMemoryIntoAddressSpaceGOOGLE(
    VkDevice device,
    VkDeviceMemory memory,
    uint64_t* pAddress)
{
    AEMU_SCOPED_TRACE("vkMapMemoryIntoAddressSpaceGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkMapMemoryIntoAddressSpaceGOOGLE_VkResult_return = (VkResult)0;
    vkMapMemoryIntoAddressSpaceGOOGLE_VkResult_return = vkEnc->vkMapMemoryIntoAddressSpaceGOOGLE(device, memory, pAddress);
    return vkMapMemoryIntoAddressSpaceGOOGLE_VkResult_return;
}
static VkResult dynCheck_entry_vkMapMemoryIntoAddressSpaceGOOGLE(
    VkDevice device,
    VkDeviceMemory memory,
    uint64_t* pAddress)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_GOOGLE_address_space"))
    {
        sOnInvalidDynamicallyCheckedCall("vkMapMemoryIntoAddressSpaceGOOGLE", "VK_GOOGLE_address_space");
    }
    AEMU_SCOPED_TRACE("vkMapMemoryIntoAddressSpaceGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkMapMemoryIntoAddressSpaceGOOGLE_VkResult_return = (VkResult)0;
    vkMapMemoryIntoAddressSpaceGOOGLE_VkResult_return = vkEnc->vkMapMemoryIntoAddressSpaceGOOGLE(device, memory, pAddress);
    return vkMapMemoryIntoAddressSpaceGOOGLE_VkResult_return;
}
#endif
#ifdef VK_GOOGLE_color_buffer
static VkResult entry_vkRegisterImageColorBufferGOOGLE(
    VkDevice device,
    VkImage image,
    uint32_t colorBuffer)
{
    AEMU_SCOPED_TRACE("vkRegisterImageColorBufferGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkRegisterImageColorBufferGOOGLE_VkResult_return = (VkResult)0;
    vkRegisterImageColorBufferGOOGLE_VkResult_return = vkEnc->vkRegisterImageColorBufferGOOGLE(device, image, colorBuffer);
    return vkRegisterImageColorBufferGOOGLE_VkResult_return;
}
static VkResult dynCheck_entry_vkRegisterImageColorBufferGOOGLE(
    VkDevice device,
    VkImage image,
    uint32_t colorBuffer)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_GOOGLE_color_buffer"))
    {
        sOnInvalidDynamicallyCheckedCall("vkRegisterImageColorBufferGOOGLE", "VK_GOOGLE_color_buffer");
    }
    AEMU_SCOPED_TRACE("vkRegisterImageColorBufferGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkRegisterImageColorBufferGOOGLE_VkResult_return = (VkResult)0;
    vkRegisterImageColorBufferGOOGLE_VkResult_return = vkEnc->vkRegisterImageColorBufferGOOGLE(device, image, colorBuffer);
    return vkRegisterImageColorBufferGOOGLE_VkResult_return;
}
static VkResult entry_vkRegisterBufferColorBufferGOOGLE(
    VkDevice device,
    VkBuffer buffer,
    uint32_t colorBuffer)
{
    AEMU_SCOPED_TRACE("vkRegisterBufferColorBufferGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkRegisterBufferColorBufferGOOGLE_VkResult_return = (VkResult)0;
    vkRegisterBufferColorBufferGOOGLE_VkResult_return = vkEnc->vkRegisterBufferColorBufferGOOGLE(device, buffer, colorBuffer);
    return vkRegisterBufferColorBufferGOOGLE_VkResult_return;
}
static VkResult dynCheck_entry_vkRegisterBufferColorBufferGOOGLE(
    VkDevice device,
    VkBuffer buffer,
    uint32_t colorBuffer)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_GOOGLE_color_buffer"))
    {
        sOnInvalidDynamicallyCheckedCall("vkRegisterBufferColorBufferGOOGLE", "VK_GOOGLE_color_buffer");
    }
    AEMU_SCOPED_TRACE("vkRegisterBufferColorBufferGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkRegisterBufferColorBufferGOOGLE_VkResult_return = (VkResult)0;
    vkRegisterBufferColorBufferGOOGLE_VkResult_return = vkEnc->vkRegisterBufferColorBufferGOOGLE(device, buffer, colorBuffer);
    return vkRegisterBufferColorBufferGOOGLE_VkResult_return;
}
#endif
#ifdef VK_GOOGLE_sized_descriptor_update_template
static void entry_vkUpdateDescriptorSetWithTemplateSizedGOOGLE(
    VkDevice device,
    VkDescriptorSet descriptorSet,
    VkDescriptorUpdateTemplate descriptorUpdateTemplate,
    uint32_t imageInfoCount,
    uint32_t bufferInfoCount,
    uint32_t bufferViewCount,
    const uint32_t* pImageInfoEntryIndices,
    const uint32_t* pBufferInfoEntryIndices,
    const uint32_t* pBufferViewEntryIndices,
    const VkDescriptorImageInfo* pImageInfos,
    const VkDescriptorBufferInfo* pBufferInfos,
    const VkBufferView* pBufferViews)
{
    AEMU_SCOPED_TRACE("vkUpdateDescriptorSetWithTemplateSizedGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkUpdateDescriptorSetWithTemplateSizedGOOGLE(device, descriptorSet, descriptorUpdateTemplate, imageInfoCount, bufferInfoCount, bufferViewCount, pImageInfoEntryIndices, pBufferInfoEntryIndices, pBufferViewEntryIndices, pImageInfos, pBufferInfos, pBufferViews);
}
static void dynCheck_entry_vkUpdateDescriptorSetWithTemplateSizedGOOGLE(
    VkDevice device,
    VkDescriptorSet descriptorSet,
    VkDescriptorUpdateTemplate descriptorUpdateTemplate,
    uint32_t imageInfoCount,
    uint32_t bufferInfoCount,
    uint32_t bufferViewCount,
    const uint32_t* pImageInfoEntryIndices,
    const uint32_t* pBufferInfoEntryIndices,
    const uint32_t* pBufferViewEntryIndices,
    const VkDescriptorImageInfo* pImageInfos,
    const VkDescriptorBufferInfo* pBufferInfos,
    const VkBufferView* pBufferViews)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_GOOGLE_sized_descriptor_update_template"))
    {
        sOnInvalidDynamicallyCheckedCall("vkUpdateDescriptorSetWithTemplateSizedGOOGLE", "VK_GOOGLE_sized_descriptor_update_template");
    }
    AEMU_SCOPED_TRACE("vkUpdateDescriptorSetWithTemplateSizedGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    vkEnc->vkUpdateDescriptorSetWithTemplateSizedGOOGLE(device, descriptorSet, descriptorUpdateTemplate, imageInfoCount, bufferInfoCount, bufferViewCount, pImageInfoEntryIndices, pBufferInfoEntryIndices, pBufferViewEntryIndices, pImageInfos, pBufferInfos, pBufferViews);
}
#endif
#ifdef VK_GOOGLE_async_command_buffers
static void entry_vkBeginCommandBufferAsyncGOOGLE(
    VkCommandBuffer commandBuffer,
    const VkCommandBufferBeginInfo* pBeginInfo)
{
    AEMU_SCOPED_TRACE("vkBeginCommandBufferAsyncGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkBeginCommandBufferAsyncGOOGLE(commandBuffer, pBeginInfo);
}
static void entry_vkEndCommandBufferAsyncGOOGLE(
    VkCommandBuffer commandBuffer)
{
    AEMU_SCOPED_TRACE("vkEndCommandBufferAsyncGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkEndCommandBufferAsyncGOOGLE(commandBuffer);
}
static void entry_vkResetCommandBufferAsyncGOOGLE(
    VkCommandBuffer commandBuffer,
    VkCommandBufferResetFlags flags)
{
    AEMU_SCOPED_TRACE("vkResetCommandBufferAsyncGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkResetCommandBufferAsyncGOOGLE(commandBuffer, flags);
}
static void entry_vkCommandBufferHostSyncGOOGLE(
    VkCommandBuffer commandBuffer,
    uint32_t needHostSync,
    uint32_t sequenceNumber)
{
    AEMU_SCOPED_TRACE("vkCommandBufferHostSyncGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    ResourceTracker::get()->syncEncodersForCommandBuffer(commandBuffer, vkEnc);
    vkEnc->vkCommandBufferHostSyncGOOGLE(commandBuffer, needHostSync, sequenceNumber);
}
#endif
#ifdef VK_GOOGLE_create_resources_with_requirements
static VkResult entry_vkCreateImageWithRequirementsGOOGLE(
    VkDevice device,
    const VkImageCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkImage* pImage,
    VkMemoryRequirements* pMemoryRequirements)
{
    AEMU_SCOPED_TRACE("vkCreateImageWithRequirementsGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateImageWithRequirementsGOOGLE_VkResult_return = (VkResult)0;
    vkCreateImageWithRequirementsGOOGLE_VkResult_return = vkEnc->vkCreateImageWithRequirementsGOOGLE(device, pCreateInfo, pAllocator, pImage, pMemoryRequirements);
    return vkCreateImageWithRequirementsGOOGLE_VkResult_return;
}
static VkResult dynCheck_entry_vkCreateImageWithRequirementsGOOGLE(
    VkDevice device,
    const VkImageCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkImage* pImage,
    VkMemoryRequirements* pMemoryRequirements)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_GOOGLE_create_resources_with_requirements"))
    {
        sOnInvalidDynamicallyCheckedCall("vkCreateImageWithRequirementsGOOGLE", "VK_GOOGLE_create_resources_with_requirements");
    }
    AEMU_SCOPED_TRACE("vkCreateImageWithRequirementsGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateImageWithRequirementsGOOGLE_VkResult_return = (VkResult)0;
    vkCreateImageWithRequirementsGOOGLE_VkResult_return = vkEnc->vkCreateImageWithRequirementsGOOGLE(device, pCreateInfo, pAllocator, pImage, pMemoryRequirements);
    return vkCreateImageWithRequirementsGOOGLE_VkResult_return;
}
static VkResult entry_vkCreateBufferWithRequirementsGOOGLE(
    VkDevice device,
    const VkBufferCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkBuffer* pBuffer,
    VkMemoryRequirements* pMemoryRequirements)
{
    AEMU_SCOPED_TRACE("vkCreateBufferWithRequirementsGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateBufferWithRequirementsGOOGLE_VkResult_return = (VkResult)0;
    vkCreateBufferWithRequirementsGOOGLE_VkResult_return = vkEnc->vkCreateBufferWithRequirementsGOOGLE(device, pCreateInfo, pAllocator, pBuffer, pMemoryRequirements);
    return vkCreateBufferWithRequirementsGOOGLE_VkResult_return;
}
static VkResult dynCheck_entry_vkCreateBufferWithRequirementsGOOGLE(
    VkDevice device,
    const VkBufferCreateInfo* pCreateInfo,
    const VkAllocationCallbacks* pAllocator,
    VkBuffer* pBuffer,
    VkMemoryRequirements* pMemoryRequirements)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_GOOGLE_create_resources_with_requirements"))
    {
        sOnInvalidDynamicallyCheckedCall("vkCreateBufferWithRequirementsGOOGLE", "VK_GOOGLE_create_resources_with_requirements");
    }
    AEMU_SCOPED_TRACE("vkCreateBufferWithRequirementsGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkCreateBufferWithRequirementsGOOGLE_VkResult_return = (VkResult)0;
    vkCreateBufferWithRequirementsGOOGLE_VkResult_return = vkEnc->vkCreateBufferWithRequirementsGOOGLE(device, pCreateInfo, pAllocator, pBuffer, pMemoryRequirements);
    return vkCreateBufferWithRequirementsGOOGLE_VkResult_return;
}
#endif
#ifdef VK_GOOGLE_address_space_info
static VkResult entry_vkGetMemoryHostAddressInfoGOOGLE(
    VkDevice device,
    VkDeviceMemory memory,
    uint64_t* pAddress,
    uint64_t* pSize,
    uint64_t* pHostmemId)
{
    AEMU_SCOPED_TRACE("vkGetMemoryHostAddressInfoGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetMemoryHostAddressInfoGOOGLE_VkResult_return = (VkResult)0;
    vkGetMemoryHostAddressInfoGOOGLE_VkResult_return = vkEnc->vkGetMemoryHostAddressInfoGOOGLE(device, memory, pAddress, pSize, pHostmemId);
    return vkGetMemoryHostAddressInfoGOOGLE_VkResult_return;
}
static VkResult dynCheck_entry_vkGetMemoryHostAddressInfoGOOGLE(
    VkDevice device,
    VkDeviceMemory memory,
    uint64_t* pAddress,
    uint64_t* pSize,
    uint64_t* pHostmemId)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_GOOGLE_address_space_info"))
    {
        sOnInvalidDynamicallyCheckedCall("vkGetMemoryHostAddressInfoGOOGLE", "VK_GOOGLE_address_space_info");
    }
    AEMU_SCOPED_TRACE("vkGetMemoryHostAddressInfoGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkGetMemoryHostAddressInfoGOOGLE_VkResult_return = (VkResult)0;
    vkGetMemoryHostAddressInfoGOOGLE_VkResult_return = vkEnc->vkGetMemoryHostAddressInfoGOOGLE(device, memory, pAddress, pSize, pHostmemId);
    return vkGetMemoryHostAddressInfoGOOGLE_VkResult_return;
}
#endif
#ifdef VK_GOOGLE_free_memory_sync
static VkResult entry_vkFreeMemorySyncGOOGLE(
    VkDevice device,
    VkDeviceMemory memory,
    const VkAllocationCallbacks* pAllocator)
{
    AEMU_SCOPED_TRACE("vkFreeMemorySyncGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkFreeMemorySyncGOOGLE_VkResult_return = (VkResult)0;
    vkFreeMemorySyncGOOGLE_VkResult_return = vkEnc->vkFreeMemorySyncGOOGLE(device, memory, pAllocator);
    return vkFreeMemorySyncGOOGLE_VkResult_return;
}
static VkResult dynCheck_entry_vkFreeMemorySyncGOOGLE(
    VkDevice device,
    VkDeviceMemory memory,
    const VkAllocationCallbacks* pAllocator)
{
    auto resources = ResourceTracker::get();
    if (!resources->hasDeviceExtension(device, "VK_GOOGLE_free_memory_sync"))
    {
        sOnInvalidDynamicallyCheckedCall("vkFreeMemorySyncGOOGLE", "VK_GOOGLE_free_memory_sync");
    }
    AEMU_SCOPED_TRACE("vkFreeMemorySyncGOOGLE");
    auto vkEnc = HostConnection::get()->vkEncoder();
    VkResult vkFreeMemorySyncGOOGLE_VkResult_return = (VkResult)0;
    vkFreeMemorySyncGOOGLE_VkResult_return = vkEnc->vkFreeMemorySyncGOOGLE(device, memory, pAllocator);
    return vkFreeMemorySyncGOOGLE_VkResult_return;
}
#endif
void* goldfish_vulkan_get_proc_address(const char* name){
#ifdef VK_VERSION_1_0
    if (!strcmp(name, "vkCreateInstance"))
    {
        return (void*)entry_vkCreateInstance;
    }
    if (!strcmp(name, "vkDestroyInstance"))
    {
        return (void*)entry_vkDestroyInstance;
    }
    if (!strcmp(name, "vkEnumeratePhysicalDevices"))
    {
        return (void*)entry_vkEnumeratePhysicalDevices;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceFeatures"))
    {
        return (void*)entry_vkGetPhysicalDeviceFeatures;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceFormatProperties"))
    {
        return (void*)entry_vkGetPhysicalDeviceFormatProperties;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceImageFormatProperties"))
    {
        return (void*)entry_vkGetPhysicalDeviceImageFormatProperties;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceProperties"))
    {
        return (void*)entry_vkGetPhysicalDeviceProperties;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceQueueFamilyProperties"))
    {
        return (void*)entry_vkGetPhysicalDeviceQueueFamilyProperties;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceMemoryProperties"))
    {
        return (void*)entry_vkGetPhysicalDeviceMemoryProperties;
    }
    if (!strcmp(name, "vkGetInstanceProcAddr"))
    {
        return (void*)entry_vkGetInstanceProcAddr;
    }
    if (!strcmp(name, "vkGetDeviceProcAddr"))
    {
        return (void*)entry_vkGetDeviceProcAddr;
    }
    if (!strcmp(name, "vkCreateDevice"))
    {
        return (void*)entry_vkCreateDevice;
    }
    if (!strcmp(name, "vkDestroyDevice"))
    {
        return (void*)entry_vkDestroyDevice;
    }
    if (!strcmp(name, "vkEnumerateInstanceExtensionProperties"))
    {
        return (void*)entry_vkEnumerateInstanceExtensionProperties;
    }
    if (!strcmp(name, "vkEnumerateDeviceExtensionProperties"))
    {
        return (void*)entry_vkEnumerateDeviceExtensionProperties;
    }
    if (!strcmp(name, "vkEnumerateInstanceLayerProperties"))
    {
        return (void*)entry_vkEnumerateInstanceLayerProperties;
    }
    if (!strcmp(name, "vkEnumerateDeviceLayerProperties"))
    {
        return (void*)entry_vkEnumerateDeviceLayerProperties;
    }
    if (!strcmp(name, "vkGetDeviceQueue"))
    {
        return (void*)entry_vkGetDeviceQueue;
    }
    if (!strcmp(name, "vkQueueSubmit"))
    {
        return (void*)entry_vkQueueSubmit;
    }
    if (!strcmp(name, "vkQueueWaitIdle"))
    {
        return (void*)entry_vkQueueWaitIdle;
    }
    if (!strcmp(name, "vkDeviceWaitIdle"))
    {
        return (void*)entry_vkDeviceWaitIdle;
    }
    if (!strcmp(name, "vkAllocateMemory"))
    {
        return (void*)entry_vkAllocateMemory;
    }
    if (!strcmp(name, "vkFreeMemory"))
    {
        return (void*)entry_vkFreeMemory;
    }
    if (!strcmp(name, "vkMapMemory"))
    {
        return (void*)entry_vkMapMemory;
    }
    if (!strcmp(name, "vkUnmapMemory"))
    {
        return (void*)entry_vkUnmapMemory;
    }
    if (!strcmp(name, "vkFlushMappedMemoryRanges"))
    {
        return (void*)entry_vkFlushMappedMemoryRanges;
    }
    if (!strcmp(name, "vkInvalidateMappedMemoryRanges"))
    {
        return (void*)entry_vkInvalidateMappedMemoryRanges;
    }
    if (!strcmp(name, "vkGetDeviceMemoryCommitment"))
    {
        return (void*)entry_vkGetDeviceMemoryCommitment;
    }
    if (!strcmp(name, "vkBindBufferMemory"))
    {
        return (void*)entry_vkBindBufferMemory;
    }
    if (!strcmp(name, "vkBindImageMemory"))
    {
        return (void*)entry_vkBindImageMemory;
    }
    if (!strcmp(name, "vkGetBufferMemoryRequirements"))
    {
        return (void*)entry_vkGetBufferMemoryRequirements;
    }
    if (!strcmp(name, "vkGetImageMemoryRequirements"))
    {
        return (void*)entry_vkGetImageMemoryRequirements;
    }
    if (!strcmp(name, "vkGetImageSparseMemoryRequirements"))
    {
        return (void*)entry_vkGetImageSparseMemoryRequirements;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSparseImageFormatProperties"))
    {
        return (void*)entry_vkGetPhysicalDeviceSparseImageFormatProperties;
    }
    if (!strcmp(name, "vkQueueBindSparse"))
    {
        return (void*)entry_vkQueueBindSparse;
    }
    if (!strcmp(name, "vkCreateFence"))
    {
        return (void*)entry_vkCreateFence;
    }
    if (!strcmp(name, "vkDestroyFence"))
    {
        return (void*)entry_vkDestroyFence;
    }
    if (!strcmp(name, "vkResetFences"))
    {
        return (void*)entry_vkResetFences;
    }
    if (!strcmp(name, "vkGetFenceStatus"))
    {
        return (void*)entry_vkGetFenceStatus;
    }
    if (!strcmp(name, "vkWaitForFences"))
    {
        return (void*)entry_vkWaitForFences;
    }
    if (!strcmp(name, "vkCreateSemaphore"))
    {
        return (void*)entry_vkCreateSemaphore;
    }
    if (!strcmp(name, "vkDestroySemaphore"))
    {
        return (void*)entry_vkDestroySemaphore;
    }
    if (!strcmp(name, "vkCreateEvent"))
    {
        return (void*)entry_vkCreateEvent;
    }
    if (!strcmp(name, "vkDestroyEvent"))
    {
        return (void*)entry_vkDestroyEvent;
    }
    if (!strcmp(name, "vkGetEventStatus"))
    {
        return (void*)entry_vkGetEventStatus;
    }
    if (!strcmp(name, "vkSetEvent"))
    {
        return (void*)entry_vkSetEvent;
    }
    if (!strcmp(name, "vkResetEvent"))
    {
        return (void*)entry_vkResetEvent;
    }
    if (!strcmp(name, "vkCreateQueryPool"))
    {
        return (void*)entry_vkCreateQueryPool;
    }
    if (!strcmp(name, "vkDestroyQueryPool"))
    {
        return (void*)entry_vkDestroyQueryPool;
    }
    if (!strcmp(name, "vkGetQueryPoolResults"))
    {
        return (void*)entry_vkGetQueryPoolResults;
    }
    if (!strcmp(name, "vkCreateBuffer"))
    {
        return (void*)entry_vkCreateBuffer;
    }
    if (!strcmp(name, "vkDestroyBuffer"))
    {
        return (void*)entry_vkDestroyBuffer;
    }
    if (!strcmp(name, "vkCreateBufferView"))
    {
        return (void*)entry_vkCreateBufferView;
    }
    if (!strcmp(name, "vkDestroyBufferView"))
    {
        return (void*)entry_vkDestroyBufferView;
    }
    if (!strcmp(name, "vkCreateImage"))
    {
        return (void*)entry_vkCreateImage;
    }
    if (!strcmp(name, "vkDestroyImage"))
    {
        return (void*)entry_vkDestroyImage;
    }
    if (!strcmp(name, "vkGetImageSubresourceLayout"))
    {
        return (void*)entry_vkGetImageSubresourceLayout;
    }
    if (!strcmp(name, "vkCreateImageView"))
    {
        return (void*)entry_vkCreateImageView;
    }
    if (!strcmp(name, "vkDestroyImageView"))
    {
        return (void*)entry_vkDestroyImageView;
    }
    if (!strcmp(name, "vkCreateShaderModule"))
    {
        return (void*)entry_vkCreateShaderModule;
    }
    if (!strcmp(name, "vkDestroyShaderModule"))
    {
        return (void*)entry_vkDestroyShaderModule;
    }
    if (!strcmp(name, "vkCreatePipelineCache"))
    {
        return (void*)entry_vkCreatePipelineCache;
    }
    if (!strcmp(name, "vkDestroyPipelineCache"))
    {
        return (void*)entry_vkDestroyPipelineCache;
    }
    if (!strcmp(name, "vkGetPipelineCacheData"))
    {
        return (void*)entry_vkGetPipelineCacheData;
    }
    if (!strcmp(name, "vkMergePipelineCaches"))
    {
        return (void*)entry_vkMergePipelineCaches;
    }
    if (!strcmp(name, "vkCreateGraphicsPipelines"))
    {
        return (void*)entry_vkCreateGraphicsPipelines;
    }
    if (!strcmp(name, "vkCreateComputePipelines"))
    {
        return (void*)entry_vkCreateComputePipelines;
    }
    if (!strcmp(name, "vkDestroyPipeline"))
    {
        return (void*)entry_vkDestroyPipeline;
    }
    if (!strcmp(name, "vkCreatePipelineLayout"))
    {
        return (void*)entry_vkCreatePipelineLayout;
    }
    if (!strcmp(name, "vkDestroyPipelineLayout"))
    {
        return (void*)entry_vkDestroyPipelineLayout;
    }
    if (!strcmp(name, "vkCreateSampler"))
    {
        return (void*)entry_vkCreateSampler;
    }
    if (!strcmp(name, "vkDestroySampler"))
    {
        return (void*)entry_vkDestroySampler;
    }
    if (!strcmp(name, "vkCreateDescriptorSetLayout"))
    {
        return (void*)entry_vkCreateDescriptorSetLayout;
    }
    if (!strcmp(name, "vkDestroyDescriptorSetLayout"))
    {
        return (void*)entry_vkDestroyDescriptorSetLayout;
    }
    if (!strcmp(name, "vkCreateDescriptorPool"))
    {
        return (void*)entry_vkCreateDescriptorPool;
    }
    if (!strcmp(name, "vkDestroyDescriptorPool"))
    {
        return (void*)entry_vkDestroyDescriptorPool;
    }
    if (!strcmp(name, "vkResetDescriptorPool"))
    {
        return (void*)entry_vkResetDescriptorPool;
    }
    if (!strcmp(name, "vkAllocateDescriptorSets"))
    {
        return (void*)entry_vkAllocateDescriptorSets;
    }
    if (!strcmp(name, "vkFreeDescriptorSets"))
    {
        return (void*)entry_vkFreeDescriptorSets;
    }
    if (!strcmp(name, "vkUpdateDescriptorSets"))
    {
        return (void*)entry_vkUpdateDescriptorSets;
    }
    if (!strcmp(name, "vkCreateFramebuffer"))
    {
        return (void*)entry_vkCreateFramebuffer;
    }
    if (!strcmp(name, "vkDestroyFramebuffer"))
    {
        return (void*)entry_vkDestroyFramebuffer;
    }
    if (!strcmp(name, "vkCreateRenderPass"))
    {
        return (void*)entry_vkCreateRenderPass;
    }
    if (!strcmp(name, "vkDestroyRenderPass"))
    {
        return (void*)entry_vkDestroyRenderPass;
    }
    if (!strcmp(name, "vkGetRenderAreaGranularity"))
    {
        return (void*)entry_vkGetRenderAreaGranularity;
    }
    if (!strcmp(name, "vkCreateCommandPool"))
    {
        return (void*)entry_vkCreateCommandPool;
    }
    if (!strcmp(name, "vkDestroyCommandPool"))
    {
        return (void*)entry_vkDestroyCommandPool;
    }
    if (!strcmp(name, "vkResetCommandPool"))
    {
        return (void*)entry_vkResetCommandPool;
    }
    if (!strcmp(name, "vkAllocateCommandBuffers"))
    {
        return (void*)entry_vkAllocateCommandBuffers;
    }
    if (!strcmp(name, "vkFreeCommandBuffers"))
    {
        return (void*)entry_vkFreeCommandBuffers;
    }
    if (!strcmp(name, "vkBeginCommandBuffer"))
    {
        return (void*)entry_vkBeginCommandBuffer;
    }
    if (!strcmp(name, "vkEndCommandBuffer"))
    {
        return (void*)entry_vkEndCommandBuffer;
    }
    if (!strcmp(name, "vkResetCommandBuffer"))
    {
        return (void*)entry_vkResetCommandBuffer;
    }
    if (!strcmp(name, "vkCmdBindPipeline"))
    {
        return (void*)entry_vkCmdBindPipeline;
    }
    if (!strcmp(name, "vkCmdSetViewport"))
    {
        return (void*)entry_vkCmdSetViewport;
    }
    if (!strcmp(name, "vkCmdSetScissor"))
    {
        return (void*)entry_vkCmdSetScissor;
    }
    if (!strcmp(name, "vkCmdSetLineWidth"))
    {
        return (void*)entry_vkCmdSetLineWidth;
    }
    if (!strcmp(name, "vkCmdSetDepthBias"))
    {
        return (void*)entry_vkCmdSetDepthBias;
    }
    if (!strcmp(name, "vkCmdSetBlendConstants"))
    {
        return (void*)entry_vkCmdSetBlendConstants;
    }
    if (!strcmp(name, "vkCmdSetDepthBounds"))
    {
        return (void*)entry_vkCmdSetDepthBounds;
    }
    if (!strcmp(name, "vkCmdSetStencilCompareMask"))
    {
        return (void*)entry_vkCmdSetStencilCompareMask;
    }
    if (!strcmp(name, "vkCmdSetStencilWriteMask"))
    {
        return (void*)entry_vkCmdSetStencilWriteMask;
    }
    if (!strcmp(name, "vkCmdSetStencilReference"))
    {
        return (void*)entry_vkCmdSetStencilReference;
    }
    if (!strcmp(name, "vkCmdBindDescriptorSets"))
    {
        return (void*)entry_vkCmdBindDescriptorSets;
    }
    if (!strcmp(name, "vkCmdBindIndexBuffer"))
    {
        return (void*)entry_vkCmdBindIndexBuffer;
    }
    if (!strcmp(name, "vkCmdBindVertexBuffers"))
    {
        return (void*)entry_vkCmdBindVertexBuffers;
    }
    if (!strcmp(name, "vkCmdDraw"))
    {
        return (void*)entry_vkCmdDraw;
    }
    if (!strcmp(name, "vkCmdDrawIndexed"))
    {
        return (void*)entry_vkCmdDrawIndexed;
    }
    if (!strcmp(name, "vkCmdDrawIndirect"))
    {
        return (void*)entry_vkCmdDrawIndirect;
    }
    if (!strcmp(name, "vkCmdDrawIndexedIndirect"))
    {
        return (void*)entry_vkCmdDrawIndexedIndirect;
    }
    if (!strcmp(name, "vkCmdDispatch"))
    {
        return (void*)entry_vkCmdDispatch;
    }
    if (!strcmp(name, "vkCmdDispatchIndirect"))
    {
        return (void*)entry_vkCmdDispatchIndirect;
    }
    if (!strcmp(name, "vkCmdCopyBuffer"))
    {
        return (void*)entry_vkCmdCopyBuffer;
    }
    if (!strcmp(name, "vkCmdCopyImage"))
    {
        return (void*)entry_vkCmdCopyImage;
    }
    if (!strcmp(name, "vkCmdBlitImage"))
    {
        return (void*)entry_vkCmdBlitImage;
    }
    if (!strcmp(name, "vkCmdCopyBufferToImage"))
    {
        return (void*)entry_vkCmdCopyBufferToImage;
    }
    if (!strcmp(name, "vkCmdCopyImageToBuffer"))
    {
        return (void*)entry_vkCmdCopyImageToBuffer;
    }
    if (!strcmp(name, "vkCmdUpdateBuffer"))
    {
        return (void*)entry_vkCmdUpdateBuffer;
    }
    if (!strcmp(name, "vkCmdFillBuffer"))
    {
        return (void*)entry_vkCmdFillBuffer;
    }
    if (!strcmp(name, "vkCmdClearColorImage"))
    {
        return (void*)entry_vkCmdClearColorImage;
    }
    if (!strcmp(name, "vkCmdClearDepthStencilImage"))
    {
        return (void*)entry_vkCmdClearDepthStencilImage;
    }
    if (!strcmp(name, "vkCmdClearAttachments"))
    {
        return (void*)entry_vkCmdClearAttachments;
    }
    if (!strcmp(name, "vkCmdResolveImage"))
    {
        return (void*)entry_vkCmdResolveImage;
    }
    if (!strcmp(name, "vkCmdSetEvent"))
    {
        return (void*)entry_vkCmdSetEvent;
    }
    if (!strcmp(name, "vkCmdResetEvent"))
    {
        return (void*)entry_vkCmdResetEvent;
    }
    if (!strcmp(name, "vkCmdWaitEvents"))
    {
        return (void*)entry_vkCmdWaitEvents;
    }
    if (!strcmp(name, "vkCmdPipelineBarrier"))
    {
        return (void*)entry_vkCmdPipelineBarrier;
    }
    if (!strcmp(name, "vkCmdBeginQuery"))
    {
        return (void*)entry_vkCmdBeginQuery;
    }
    if (!strcmp(name, "vkCmdEndQuery"))
    {
        return (void*)entry_vkCmdEndQuery;
    }
    if (!strcmp(name, "vkCmdResetQueryPool"))
    {
        return (void*)entry_vkCmdResetQueryPool;
    }
    if (!strcmp(name, "vkCmdWriteTimestamp"))
    {
        return (void*)entry_vkCmdWriteTimestamp;
    }
    if (!strcmp(name, "vkCmdCopyQueryPoolResults"))
    {
        return (void*)entry_vkCmdCopyQueryPoolResults;
    }
    if (!strcmp(name, "vkCmdPushConstants"))
    {
        return (void*)entry_vkCmdPushConstants;
    }
    if (!strcmp(name, "vkCmdBeginRenderPass"))
    {
        return (void*)entry_vkCmdBeginRenderPass;
    }
    if (!strcmp(name, "vkCmdNextSubpass"))
    {
        return (void*)entry_vkCmdNextSubpass;
    }
    if (!strcmp(name, "vkCmdEndRenderPass"))
    {
        return (void*)entry_vkCmdEndRenderPass;
    }
    if (!strcmp(name, "vkCmdExecuteCommands"))
    {
        return (void*)entry_vkCmdExecuteCommands;
    }
#endif
#ifdef VK_VERSION_1_1
    if (!strcmp(name, "vkEnumerateInstanceVersion"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkBindBufferMemory2"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkBindImageMemory2"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetDeviceGroupPeerMemoryFeatures"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCmdSetDeviceMask"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCmdDispatchBase"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkEnumeratePhysicalDeviceGroups"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetImageMemoryRequirements2"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetBufferMemoryRequirements2"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetImageSparseMemoryRequirements2"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceFeatures2"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceProperties2"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceFormatProperties2"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceImageFormatProperties2"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceQueueFamilyProperties2"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceMemoryProperties2"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSparseImageFormatProperties2"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkTrimCommandPool"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetDeviceQueue2"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCreateSamplerYcbcrConversion"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkDestroySamplerYcbcrConversion"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCreateDescriptorUpdateTemplate"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkDestroyDescriptorUpdateTemplate"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkUpdateDescriptorSetWithTemplate"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceExternalBufferProperties"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceExternalFenceProperties"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceExternalSemaphoreProperties"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetDescriptorSetLayoutSupport"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_surface
    if (!strcmp(name, "vkDestroySurfaceKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSurfaceSupportKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSurfaceCapabilitiesKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSurfaceFormatsKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSurfacePresentModesKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_swapchain
    if (!strcmp(name, "vkCreateSwapchainKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkDestroySwapchainKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetSwapchainImagesKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkAcquireNextImageKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkQueuePresentKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetDeviceGroupPresentCapabilitiesKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetDeviceGroupSurfacePresentModesKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDevicePresentRectanglesKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkAcquireNextImage2KHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_display
    if (!strcmp(name, "vkGetPhysicalDeviceDisplayPropertiesKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceDisplayPlanePropertiesKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetDisplayPlaneSupportedDisplaysKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetDisplayModePropertiesKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCreateDisplayModeKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetDisplayPlaneCapabilitiesKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCreateDisplayPlaneSurfaceKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_display_swapchain
    if (!strcmp(name, "vkCreateSharedSwapchainsKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_xlib_surface
    if (!strcmp(name, "vkCreateXlibSurfaceKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceXlibPresentationSupportKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_xcb_surface
    if (!strcmp(name, "vkCreateXcbSurfaceKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceXcbPresentationSupportKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_wayland_surface
    if (!strcmp(name, "vkCreateWaylandSurfaceKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceWaylandPresentationSupportKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_mir_surface
    if (!strcmp(name, "vkCreateMirSurfaceKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceMirPresentationSupportKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_android_surface
    if (!strcmp(name, "vkCreateAndroidSurfaceKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_win32_surface
    if (!strcmp(name, "vkCreateWin32SurfaceKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceWin32PresentationSupportKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_get_physical_device_properties2
    if (!strcmp(name, "vkGetPhysicalDeviceFeatures2KHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceProperties2KHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceFormatProperties2KHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceImageFormatProperties2KHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceQueueFamilyProperties2KHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceMemoryProperties2KHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSparseImageFormatProperties2KHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_device_group
    if (!strcmp(name, "vkGetDeviceGroupPeerMemoryFeaturesKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCmdSetDeviceMaskKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCmdDispatchBaseKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_maintenance1
    if (!strcmp(name, "vkTrimCommandPoolKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_device_group_creation
    if (!strcmp(name, "vkEnumeratePhysicalDeviceGroupsKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_external_memory_capabilities
    if (!strcmp(name, "vkGetPhysicalDeviceExternalBufferPropertiesKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_external_memory_win32
    if (!strcmp(name, "vkGetMemoryWin32HandleKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetMemoryWin32HandlePropertiesKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_external_memory_fd
    if (!strcmp(name, "vkGetMemoryFdKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetMemoryFdPropertiesKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_external_semaphore_capabilities
    if (!strcmp(name, "vkGetPhysicalDeviceExternalSemaphorePropertiesKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_external_semaphore_win32
    if (!strcmp(name, "vkImportSemaphoreWin32HandleKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetSemaphoreWin32HandleKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_external_semaphore_fd
    if (!strcmp(name, "vkImportSemaphoreFdKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetSemaphoreFdKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_push_descriptor
    if (!strcmp(name, "vkCmdPushDescriptorSetKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCmdPushDescriptorSetWithTemplateKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_descriptor_update_template
    if (!strcmp(name, "vkCreateDescriptorUpdateTemplateKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkDestroyDescriptorUpdateTemplateKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkUpdateDescriptorSetWithTemplateKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_create_renderpass2
    if (!strcmp(name, "vkCreateRenderPass2KHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCmdBeginRenderPass2KHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCmdNextSubpass2KHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCmdEndRenderPass2KHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_shared_presentable_image
    if (!strcmp(name, "vkGetSwapchainStatusKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_external_fence_capabilities
    if (!strcmp(name, "vkGetPhysicalDeviceExternalFencePropertiesKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_external_fence_win32
    if (!strcmp(name, "vkImportFenceWin32HandleKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetFenceWin32HandleKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_external_fence_fd
    if (!strcmp(name, "vkImportFenceFdKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetFenceFdKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_get_surface_capabilities2
    if (!strcmp(name, "vkGetPhysicalDeviceSurfaceCapabilities2KHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSurfaceFormats2KHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_get_display_properties2
    if (!strcmp(name, "vkGetPhysicalDeviceDisplayProperties2KHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceDisplayPlaneProperties2KHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetDisplayModeProperties2KHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetDisplayPlaneCapabilities2KHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_get_memory_requirements2
    if (!strcmp(name, "vkGetImageMemoryRequirements2KHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetBufferMemoryRequirements2KHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetImageSparseMemoryRequirements2KHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_sampler_ycbcr_conversion
    if (!strcmp(name, "vkCreateSamplerYcbcrConversionKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkDestroySamplerYcbcrConversionKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_bind_memory2
    if (!strcmp(name, "vkBindBufferMemory2KHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkBindImageMemory2KHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_maintenance3
    if (!strcmp(name, "vkGetDescriptorSetLayoutSupportKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_KHR_draw_indirect_count
    if (!strcmp(name, "vkCmdDrawIndirectCountKHR"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCmdDrawIndexedIndirectCountKHR"))
    {
        return nullptr;
    }
#endif
#ifdef VK_ANDROID_native_buffer
    if (!strcmp(name, "vkGetSwapchainGrallocUsageANDROID"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkAcquireImageANDROID"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkQueueSignalReleaseImageANDROID"))
    {
        return nullptr;
    }
#endif
#ifdef VK_EXT_debug_report
    if (!strcmp(name, "vkCreateDebugReportCallbackEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkDestroyDebugReportCallbackEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkDebugReportMessageEXT"))
    {
        return nullptr;
    }
#endif
#ifdef VK_EXT_debug_marker
    if (!strcmp(name, "vkDebugMarkerSetObjectTagEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkDebugMarkerSetObjectNameEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCmdDebugMarkerBeginEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCmdDebugMarkerEndEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCmdDebugMarkerInsertEXT"))
    {
        return nullptr;
    }
#endif
#ifdef VK_AMD_draw_indirect_count
    if (!strcmp(name, "vkCmdDrawIndirectCountAMD"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCmdDrawIndexedIndirectCountAMD"))
    {
        return nullptr;
    }
#endif
#ifdef VK_AMD_shader_info
    if (!strcmp(name, "vkGetShaderInfoAMD"))
    {
        return nullptr;
    }
#endif
#ifdef VK_NV_external_memory_capabilities
    if (!strcmp(name, "vkGetPhysicalDeviceExternalImageFormatPropertiesNV"))
    {
        return nullptr;
    }
#endif
#ifdef VK_NV_external_memory_win32
    if (!strcmp(name, "vkGetMemoryWin32HandleNV"))
    {
        return nullptr;
    }
#endif
#ifdef VK_NN_vi_surface
    if (!strcmp(name, "vkCreateViSurfaceNN"))
    {
        return nullptr;
    }
#endif
#ifdef VK_EXT_conditional_rendering
    if (!strcmp(name, "vkCmdBeginConditionalRenderingEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCmdEndConditionalRenderingEXT"))
    {
        return nullptr;
    }
#endif
#ifdef VK_NVX_device_generated_commands
    if (!strcmp(name, "vkCmdProcessCommandsNVX"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCmdReserveSpaceForCommandsNVX"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCreateIndirectCommandsLayoutNVX"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkDestroyIndirectCommandsLayoutNVX"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCreateObjectTableNVX"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkDestroyObjectTableNVX"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkRegisterObjectsNVX"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkUnregisterObjectsNVX"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceGeneratedCommandsPropertiesNVX"))
    {
        return nullptr;
    }
#endif
#ifdef VK_NV_clip_space_w_scaling
    if (!strcmp(name, "vkCmdSetViewportWScalingNV"))
    {
        return nullptr;
    }
#endif
#ifdef VK_EXT_direct_mode_display
    if (!strcmp(name, "vkReleaseDisplayEXT"))
    {
        return nullptr;
    }
#endif
#ifdef VK_EXT_acquire_xlib_display
    if (!strcmp(name, "vkAcquireXlibDisplayEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetRandROutputDisplayEXT"))
    {
        return nullptr;
    }
#endif
#ifdef VK_EXT_display_surface_counter
    if (!strcmp(name, "vkGetPhysicalDeviceSurfaceCapabilities2EXT"))
    {
        return nullptr;
    }
#endif
#ifdef VK_EXT_display_control
    if (!strcmp(name, "vkDisplayPowerControlEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkRegisterDeviceEventEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkRegisterDisplayEventEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetSwapchainCounterEXT"))
    {
        return nullptr;
    }
#endif
#ifdef VK_GOOGLE_display_timing
    if (!strcmp(name, "vkGetRefreshCycleDurationGOOGLE"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPastPresentationTimingGOOGLE"))
    {
        return nullptr;
    }
#endif
#ifdef VK_EXT_discard_rectangles
    if (!strcmp(name, "vkCmdSetDiscardRectangleEXT"))
    {
        return nullptr;
    }
#endif
#ifdef VK_EXT_hdr_metadata
    if (!strcmp(name, "vkSetHdrMetadataEXT"))
    {
        return nullptr;
    }
#endif
#ifdef VK_MVK_ios_surface
    if (!strcmp(name, "vkCreateIOSSurfaceMVK"))
    {
        return nullptr;
    }
#endif
#ifdef VK_MVK_macos_surface
    if (!strcmp(name, "vkCreateMacOSSurfaceMVK"))
    {
        return nullptr;
    }
#endif
#ifdef VK_EXT_debug_utils
    if (!strcmp(name, "vkSetDebugUtilsObjectNameEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkSetDebugUtilsObjectTagEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkQueueBeginDebugUtilsLabelEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkQueueEndDebugUtilsLabelEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkQueueInsertDebugUtilsLabelEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCmdBeginDebugUtilsLabelEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCmdEndDebugUtilsLabelEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCmdInsertDebugUtilsLabelEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCreateDebugUtilsMessengerEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkDestroyDebugUtilsMessengerEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkSubmitDebugUtilsMessageEXT"))
    {
        return nullptr;
    }
#endif
#ifdef VK_ANDROID_external_memory_android_hardware_buffer
    if (!strcmp(name, "vkGetAndroidHardwareBufferPropertiesANDROID"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetMemoryAndroidHardwareBufferANDROID"))
    {
        return nullptr;
    }
#endif
#ifdef VK_EXT_sample_locations
    if (!strcmp(name, "vkCmdSetSampleLocationsEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceMultisamplePropertiesEXT"))
    {
        return nullptr;
    }
#endif
#ifdef VK_EXT_validation_cache
    if (!strcmp(name, "vkCreateValidationCacheEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkDestroyValidationCacheEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkMergeValidationCachesEXT"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetValidationCacheDataEXT"))
    {
        return nullptr;
    }
#endif
#ifdef VK_EXT_external_memory_host
    if (!strcmp(name, "vkGetMemoryHostPointerPropertiesEXT"))
    {
        return nullptr;
    }
#endif
#ifdef VK_AMD_buffer_marker
    if (!strcmp(name, "vkCmdWriteBufferMarkerAMD"))
    {
        return nullptr;
    }
#endif
#ifdef VK_NV_device_diagnostic_checkpoints
    if (!strcmp(name, "vkCmdSetCheckpointNV"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetQueueCheckpointDataNV"))
    {
        return nullptr;
    }
#endif
#ifdef VK_GOOGLE_address_space
    if (!strcmp(name, "vkMapMemoryIntoAddressSpaceGOOGLE"))
    {
        return nullptr;
    }
#endif
#ifdef VK_GOOGLE_color_buffer
    if (!strcmp(name, "vkRegisterImageColorBufferGOOGLE"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkRegisterBufferColorBufferGOOGLE"))
    {
        return nullptr;
    }
#endif
#ifdef VK_GOOGLE_sized_descriptor_update_template
    if (!strcmp(name, "vkUpdateDescriptorSetWithTemplateSizedGOOGLE"))
    {
        return nullptr;
    }
#endif
#ifdef VK_GOOGLE_async_command_buffers
    if (!strcmp(name, "vkBeginCommandBufferAsyncGOOGLE"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkEndCommandBufferAsyncGOOGLE"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkResetCommandBufferAsyncGOOGLE"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCommandBufferHostSyncGOOGLE"))
    {
        return nullptr;
    }
#endif
#ifdef VK_GOOGLE_create_resources_with_requirements
    if (!strcmp(name, "vkCreateImageWithRequirementsGOOGLE"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkCreateBufferWithRequirementsGOOGLE"))
    {
        return nullptr;
    }
#endif
#ifdef VK_GOOGLE_address_space_info
    if (!strcmp(name, "vkGetMemoryHostAddressInfoGOOGLE"))
    {
        return nullptr;
    }
#endif
#ifdef VK_GOOGLE_free_memory_sync
    if (!strcmp(name, "vkFreeMemorySyncGOOGLE"))
    {
        return nullptr;
    }
#endif
    return nullptr;
}
void* goldfish_vulkan_get_instance_proc_address(VkInstance instance, const char* name){
    auto resources = ResourceTracker::get();
    bool has1_1OrHigher = resources->getApiVersionFromInstance(instance) >= VK_API_VERSION_1_1;
#ifdef VK_VERSION_1_0
    if (!strcmp(name, "vkCreateInstance"))
    {
        return (void*)entry_vkCreateInstance;
    }
    if (!strcmp(name, "vkDestroyInstance"))
    {
        return (void*)entry_vkDestroyInstance;
    }
    if (!strcmp(name, "vkEnumeratePhysicalDevices"))
    {
        return (void*)entry_vkEnumeratePhysicalDevices;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceFeatures"))
    {
        return (void*)entry_vkGetPhysicalDeviceFeatures;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceFormatProperties"))
    {
        return (void*)entry_vkGetPhysicalDeviceFormatProperties;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceImageFormatProperties"))
    {
        return (void*)entry_vkGetPhysicalDeviceImageFormatProperties;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceProperties"))
    {
        return (void*)entry_vkGetPhysicalDeviceProperties;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceQueueFamilyProperties"))
    {
        return (void*)entry_vkGetPhysicalDeviceQueueFamilyProperties;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceMemoryProperties"))
    {
        return (void*)entry_vkGetPhysicalDeviceMemoryProperties;
    }
    if (!strcmp(name, "vkGetInstanceProcAddr"))
    {
        return (void*)entry_vkGetInstanceProcAddr;
    }
    if (!strcmp(name, "vkGetDeviceProcAddr"))
    {
        return (void*)entry_vkGetDeviceProcAddr;
    }
    if (!strcmp(name, "vkCreateDevice"))
    {
        return (void*)entry_vkCreateDevice;
    }
    if (!strcmp(name, "vkDestroyDevice"))
    {
        return (void*)entry_vkDestroyDevice;
    }
    if (!strcmp(name, "vkEnumerateInstanceExtensionProperties"))
    {
        return (void*)entry_vkEnumerateInstanceExtensionProperties;
    }
    if (!strcmp(name, "vkEnumerateDeviceExtensionProperties"))
    {
        return (void*)entry_vkEnumerateDeviceExtensionProperties;
    }
    if (!strcmp(name, "vkEnumerateInstanceLayerProperties"))
    {
        return (void*)entry_vkEnumerateInstanceLayerProperties;
    }
    if (!strcmp(name, "vkEnumerateDeviceLayerProperties"))
    {
        return (void*)entry_vkEnumerateDeviceLayerProperties;
    }
    if (!strcmp(name, "vkGetDeviceQueue"))
    {
        return (void*)entry_vkGetDeviceQueue;
    }
    if (!strcmp(name, "vkQueueSubmit"))
    {
        return (void*)entry_vkQueueSubmit;
    }
    if (!strcmp(name, "vkQueueWaitIdle"))
    {
        return (void*)entry_vkQueueWaitIdle;
    }
    if (!strcmp(name, "vkDeviceWaitIdle"))
    {
        return (void*)entry_vkDeviceWaitIdle;
    }
    if (!strcmp(name, "vkAllocateMemory"))
    {
        return (void*)entry_vkAllocateMemory;
    }
    if (!strcmp(name, "vkFreeMemory"))
    {
        return (void*)entry_vkFreeMemory;
    }
    if (!strcmp(name, "vkMapMemory"))
    {
        return (void*)entry_vkMapMemory;
    }
    if (!strcmp(name, "vkUnmapMemory"))
    {
        return (void*)entry_vkUnmapMemory;
    }
    if (!strcmp(name, "vkFlushMappedMemoryRanges"))
    {
        return (void*)entry_vkFlushMappedMemoryRanges;
    }
    if (!strcmp(name, "vkInvalidateMappedMemoryRanges"))
    {
        return (void*)entry_vkInvalidateMappedMemoryRanges;
    }
    if (!strcmp(name, "vkGetDeviceMemoryCommitment"))
    {
        return (void*)entry_vkGetDeviceMemoryCommitment;
    }
    if (!strcmp(name, "vkBindBufferMemory"))
    {
        return (void*)entry_vkBindBufferMemory;
    }
    if (!strcmp(name, "vkBindImageMemory"))
    {
        return (void*)entry_vkBindImageMemory;
    }
    if (!strcmp(name, "vkGetBufferMemoryRequirements"))
    {
        return (void*)entry_vkGetBufferMemoryRequirements;
    }
    if (!strcmp(name, "vkGetImageMemoryRequirements"))
    {
        return (void*)entry_vkGetImageMemoryRequirements;
    }
    if (!strcmp(name, "vkGetImageSparseMemoryRequirements"))
    {
        return (void*)entry_vkGetImageSparseMemoryRequirements;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSparseImageFormatProperties"))
    {
        return (void*)entry_vkGetPhysicalDeviceSparseImageFormatProperties;
    }
    if (!strcmp(name, "vkQueueBindSparse"))
    {
        return (void*)entry_vkQueueBindSparse;
    }
    if (!strcmp(name, "vkCreateFence"))
    {
        return (void*)entry_vkCreateFence;
    }
    if (!strcmp(name, "vkDestroyFence"))
    {
        return (void*)entry_vkDestroyFence;
    }
    if (!strcmp(name, "vkResetFences"))
    {
        return (void*)entry_vkResetFences;
    }
    if (!strcmp(name, "vkGetFenceStatus"))
    {
        return (void*)entry_vkGetFenceStatus;
    }
    if (!strcmp(name, "vkWaitForFences"))
    {
        return (void*)entry_vkWaitForFences;
    }
    if (!strcmp(name, "vkCreateSemaphore"))
    {
        return (void*)entry_vkCreateSemaphore;
    }
    if (!strcmp(name, "vkDestroySemaphore"))
    {
        return (void*)entry_vkDestroySemaphore;
    }
    if (!strcmp(name, "vkCreateEvent"))
    {
        return (void*)entry_vkCreateEvent;
    }
    if (!strcmp(name, "vkDestroyEvent"))
    {
        return (void*)entry_vkDestroyEvent;
    }
    if (!strcmp(name, "vkGetEventStatus"))
    {
        return (void*)entry_vkGetEventStatus;
    }
    if (!strcmp(name, "vkSetEvent"))
    {
        return (void*)entry_vkSetEvent;
    }
    if (!strcmp(name, "vkResetEvent"))
    {
        return (void*)entry_vkResetEvent;
    }
    if (!strcmp(name, "vkCreateQueryPool"))
    {
        return (void*)entry_vkCreateQueryPool;
    }
    if (!strcmp(name, "vkDestroyQueryPool"))
    {
        return (void*)entry_vkDestroyQueryPool;
    }
    if (!strcmp(name, "vkGetQueryPoolResults"))
    {
        return (void*)entry_vkGetQueryPoolResults;
    }
    if (!strcmp(name, "vkCreateBuffer"))
    {
        return (void*)entry_vkCreateBuffer;
    }
    if (!strcmp(name, "vkDestroyBuffer"))
    {
        return (void*)entry_vkDestroyBuffer;
    }
    if (!strcmp(name, "vkCreateBufferView"))
    {
        return (void*)entry_vkCreateBufferView;
    }
    if (!strcmp(name, "vkDestroyBufferView"))
    {
        return (void*)entry_vkDestroyBufferView;
    }
    if (!strcmp(name, "vkCreateImage"))
    {
        return (void*)entry_vkCreateImage;
    }
    if (!strcmp(name, "vkDestroyImage"))
    {
        return (void*)entry_vkDestroyImage;
    }
    if (!strcmp(name, "vkGetImageSubresourceLayout"))
    {
        return (void*)entry_vkGetImageSubresourceLayout;
    }
    if (!strcmp(name, "vkCreateImageView"))
    {
        return (void*)entry_vkCreateImageView;
    }
    if (!strcmp(name, "vkDestroyImageView"))
    {
        return (void*)entry_vkDestroyImageView;
    }
    if (!strcmp(name, "vkCreateShaderModule"))
    {
        return (void*)entry_vkCreateShaderModule;
    }
    if (!strcmp(name, "vkDestroyShaderModule"))
    {
        return (void*)entry_vkDestroyShaderModule;
    }
    if (!strcmp(name, "vkCreatePipelineCache"))
    {
        return (void*)entry_vkCreatePipelineCache;
    }
    if (!strcmp(name, "vkDestroyPipelineCache"))
    {
        return (void*)entry_vkDestroyPipelineCache;
    }
    if (!strcmp(name, "vkGetPipelineCacheData"))
    {
        return (void*)entry_vkGetPipelineCacheData;
    }
    if (!strcmp(name, "vkMergePipelineCaches"))
    {
        return (void*)entry_vkMergePipelineCaches;
    }
    if (!strcmp(name, "vkCreateGraphicsPipelines"))
    {
        return (void*)entry_vkCreateGraphicsPipelines;
    }
    if (!strcmp(name, "vkCreateComputePipelines"))
    {
        return (void*)entry_vkCreateComputePipelines;
    }
    if (!strcmp(name, "vkDestroyPipeline"))
    {
        return (void*)entry_vkDestroyPipeline;
    }
    if (!strcmp(name, "vkCreatePipelineLayout"))
    {
        return (void*)entry_vkCreatePipelineLayout;
    }
    if (!strcmp(name, "vkDestroyPipelineLayout"))
    {
        return (void*)entry_vkDestroyPipelineLayout;
    }
    if (!strcmp(name, "vkCreateSampler"))
    {
        return (void*)entry_vkCreateSampler;
    }
    if (!strcmp(name, "vkDestroySampler"))
    {
        return (void*)entry_vkDestroySampler;
    }
    if (!strcmp(name, "vkCreateDescriptorSetLayout"))
    {
        return (void*)entry_vkCreateDescriptorSetLayout;
    }
    if (!strcmp(name, "vkDestroyDescriptorSetLayout"))
    {
        return (void*)entry_vkDestroyDescriptorSetLayout;
    }
    if (!strcmp(name, "vkCreateDescriptorPool"))
    {
        return (void*)entry_vkCreateDescriptorPool;
    }
    if (!strcmp(name, "vkDestroyDescriptorPool"))
    {
        return (void*)entry_vkDestroyDescriptorPool;
    }
    if (!strcmp(name, "vkResetDescriptorPool"))
    {
        return (void*)entry_vkResetDescriptorPool;
    }
    if (!strcmp(name, "vkAllocateDescriptorSets"))
    {
        return (void*)entry_vkAllocateDescriptorSets;
    }
    if (!strcmp(name, "vkFreeDescriptorSets"))
    {
        return (void*)entry_vkFreeDescriptorSets;
    }
    if (!strcmp(name, "vkUpdateDescriptorSets"))
    {
        return (void*)entry_vkUpdateDescriptorSets;
    }
    if (!strcmp(name, "vkCreateFramebuffer"))
    {
        return (void*)entry_vkCreateFramebuffer;
    }
    if (!strcmp(name, "vkDestroyFramebuffer"))
    {
        return (void*)entry_vkDestroyFramebuffer;
    }
    if (!strcmp(name, "vkCreateRenderPass"))
    {
        return (void*)entry_vkCreateRenderPass;
    }
    if (!strcmp(name, "vkDestroyRenderPass"))
    {
        return (void*)entry_vkDestroyRenderPass;
    }
    if (!strcmp(name, "vkGetRenderAreaGranularity"))
    {
        return (void*)entry_vkGetRenderAreaGranularity;
    }
    if (!strcmp(name, "vkCreateCommandPool"))
    {
        return (void*)entry_vkCreateCommandPool;
    }
    if (!strcmp(name, "vkDestroyCommandPool"))
    {
        return (void*)entry_vkDestroyCommandPool;
    }
    if (!strcmp(name, "vkResetCommandPool"))
    {
        return (void*)entry_vkResetCommandPool;
    }
    if (!strcmp(name, "vkAllocateCommandBuffers"))
    {
        return (void*)entry_vkAllocateCommandBuffers;
    }
    if (!strcmp(name, "vkFreeCommandBuffers"))
    {
        return (void*)entry_vkFreeCommandBuffers;
    }
    if (!strcmp(name, "vkBeginCommandBuffer"))
    {
        return (void*)entry_vkBeginCommandBuffer;
    }
    if (!strcmp(name, "vkEndCommandBuffer"))
    {
        return (void*)entry_vkEndCommandBuffer;
    }
    if (!strcmp(name, "vkResetCommandBuffer"))
    {
        return (void*)entry_vkResetCommandBuffer;
    }
    if (!strcmp(name, "vkCmdBindPipeline"))
    {
        return (void*)entry_vkCmdBindPipeline;
    }
    if (!strcmp(name, "vkCmdSetViewport"))
    {
        return (void*)entry_vkCmdSetViewport;
    }
    if (!strcmp(name, "vkCmdSetScissor"))
    {
        return (void*)entry_vkCmdSetScissor;
    }
    if (!strcmp(name, "vkCmdSetLineWidth"))
    {
        return (void*)entry_vkCmdSetLineWidth;
    }
    if (!strcmp(name, "vkCmdSetDepthBias"))
    {
        return (void*)entry_vkCmdSetDepthBias;
    }
    if (!strcmp(name, "vkCmdSetBlendConstants"))
    {
        return (void*)entry_vkCmdSetBlendConstants;
    }
    if (!strcmp(name, "vkCmdSetDepthBounds"))
    {
        return (void*)entry_vkCmdSetDepthBounds;
    }
    if (!strcmp(name, "vkCmdSetStencilCompareMask"))
    {
        return (void*)entry_vkCmdSetStencilCompareMask;
    }
    if (!strcmp(name, "vkCmdSetStencilWriteMask"))
    {
        return (void*)entry_vkCmdSetStencilWriteMask;
    }
    if (!strcmp(name, "vkCmdSetStencilReference"))
    {
        return (void*)entry_vkCmdSetStencilReference;
    }
    if (!strcmp(name, "vkCmdBindDescriptorSets"))
    {
        return (void*)entry_vkCmdBindDescriptorSets;
    }
    if (!strcmp(name, "vkCmdBindIndexBuffer"))
    {
        return (void*)entry_vkCmdBindIndexBuffer;
    }
    if (!strcmp(name, "vkCmdBindVertexBuffers"))
    {
        return (void*)entry_vkCmdBindVertexBuffers;
    }
    if (!strcmp(name, "vkCmdDraw"))
    {
        return (void*)entry_vkCmdDraw;
    }
    if (!strcmp(name, "vkCmdDrawIndexed"))
    {
        return (void*)entry_vkCmdDrawIndexed;
    }
    if (!strcmp(name, "vkCmdDrawIndirect"))
    {
        return (void*)entry_vkCmdDrawIndirect;
    }
    if (!strcmp(name, "vkCmdDrawIndexedIndirect"))
    {
        return (void*)entry_vkCmdDrawIndexedIndirect;
    }
    if (!strcmp(name, "vkCmdDispatch"))
    {
        return (void*)entry_vkCmdDispatch;
    }
    if (!strcmp(name, "vkCmdDispatchIndirect"))
    {
        return (void*)entry_vkCmdDispatchIndirect;
    }
    if (!strcmp(name, "vkCmdCopyBuffer"))
    {
        return (void*)entry_vkCmdCopyBuffer;
    }
    if (!strcmp(name, "vkCmdCopyImage"))
    {
        return (void*)entry_vkCmdCopyImage;
    }
    if (!strcmp(name, "vkCmdBlitImage"))
    {
        return (void*)entry_vkCmdBlitImage;
    }
    if (!strcmp(name, "vkCmdCopyBufferToImage"))
    {
        return (void*)entry_vkCmdCopyBufferToImage;
    }
    if (!strcmp(name, "vkCmdCopyImageToBuffer"))
    {
        return (void*)entry_vkCmdCopyImageToBuffer;
    }
    if (!strcmp(name, "vkCmdUpdateBuffer"))
    {
        return (void*)entry_vkCmdUpdateBuffer;
    }
    if (!strcmp(name, "vkCmdFillBuffer"))
    {
        return (void*)entry_vkCmdFillBuffer;
    }
    if (!strcmp(name, "vkCmdClearColorImage"))
    {
        return (void*)entry_vkCmdClearColorImage;
    }
    if (!strcmp(name, "vkCmdClearDepthStencilImage"))
    {
        return (void*)entry_vkCmdClearDepthStencilImage;
    }
    if (!strcmp(name, "vkCmdClearAttachments"))
    {
        return (void*)entry_vkCmdClearAttachments;
    }
    if (!strcmp(name, "vkCmdResolveImage"))
    {
        return (void*)entry_vkCmdResolveImage;
    }
    if (!strcmp(name, "vkCmdSetEvent"))
    {
        return (void*)entry_vkCmdSetEvent;
    }
    if (!strcmp(name, "vkCmdResetEvent"))
    {
        return (void*)entry_vkCmdResetEvent;
    }
    if (!strcmp(name, "vkCmdWaitEvents"))
    {
        return (void*)entry_vkCmdWaitEvents;
    }
    if (!strcmp(name, "vkCmdPipelineBarrier"))
    {
        return (void*)entry_vkCmdPipelineBarrier;
    }
    if (!strcmp(name, "vkCmdBeginQuery"))
    {
        return (void*)entry_vkCmdBeginQuery;
    }
    if (!strcmp(name, "vkCmdEndQuery"))
    {
        return (void*)entry_vkCmdEndQuery;
    }
    if (!strcmp(name, "vkCmdResetQueryPool"))
    {
        return (void*)entry_vkCmdResetQueryPool;
    }
    if (!strcmp(name, "vkCmdWriteTimestamp"))
    {
        return (void*)entry_vkCmdWriteTimestamp;
    }
    if (!strcmp(name, "vkCmdCopyQueryPoolResults"))
    {
        return (void*)entry_vkCmdCopyQueryPoolResults;
    }
    if (!strcmp(name, "vkCmdPushConstants"))
    {
        return (void*)entry_vkCmdPushConstants;
    }
    if (!strcmp(name, "vkCmdBeginRenderPass"))
    {
        return (void*)entry_vkCmdBeginRenderPass;
    }
    if (!strcmp(name, "vkCmdNextSubpass"))
    {
        return (void*)entry_vkCmdNextSubpass;
    }
    if (!strcmp(name, "vkCmdEndRenderPass"))
    {
        return (void*)entry_vkCmdEndRenderPass;
    }
    if (!strcmp(name, "vkCmdExecuteCommands"))
    {
        return (void*)entry_vkCmdExecuteCommands;
    }
#endif
#ifdef VK_VERSION_1_1
    if (!strcmp(name, "vkEnumerateInstanceVersion"))
    {
        return has1_1OrHigher ? (void*)entry_vkEnumerateInstanceVersion : nullptr;
    }
    if (!strcmp(name, "vkBindBufferMemory2"))
    {
        return (void*)dynCheck_entry_vkBindBufferMemory2;
    }
    if (!strcmp(name, "vkBindImageMemory2"))
    {
        return (void*)dynCheck_entry_vkBindImageMemory2;
    }
    if (!strcmp(name, "vkGetDeviceGroupPeerMemoryFeatures"))
    {
        return (void*)dynCheck_entry_vkGetDeviceGroupPeerMemoryFeatures;
    }
    if (!strcmp(name, "vkCmdSetDeviceMask"))
    {
        return has1_1OrHigher ? (void*)entry_vkCmdSetDeviceMask : nullptr;
    }
    if (!strcmp(name, "vkCmdDispatchBase"))
    {
        return has1_1OrHigher ? (void*)entry_vkCmdDispatchBase : nullptr;
    }
    if (!strcmp(name, "vkEnumeratePhysicalDeviceGroups"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetImageMemoryRequirements2"))
    {
        return (void*)dynCheck_entry_vkGetImageMemoryRequirements2;
    }
    if (!strcmp(name, "vkGetBufferMemoryRequirements2"))
    {
        return (void*)dynCheck_entry_vkGetBufferMemoryRequirements2;
    }
    if (!strcmp(name, "vkGetImageSparseMemoryRequirements2"))
    {
        return (void*)dynCheck_entry_vkGetImageSparseMemoryRequirements2;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceFeatures2"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetPhysicalDeviceFeatures2 : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceProperties2"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetPhysicalDeviceProperties2 : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceFormatProperties2"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetPhysicalDeviceFormatProperties2 : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceImageFormatProperties2"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetPhysicalDeviceImageFormatProperties2 : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceQueueFamilyProperties2"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetPhysicalDeviceQueueFamilyProperties2 : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceMemoryProperties2"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetPhysicalDeviceMemoryProperties2 : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSparseImageFormatProperties2"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetPhysicalDeviceSparseImageFormatProperties2 : nullptr;
    }
    if (!strcmp(name, "vkTrimCommandPool"))
    {
        return (void*)dynCheck_entry_vkTrimCommandPool;
    }
    if (!strcmp(name, "vkGetDeviceQueue2"))
    {
        return (void*)dynCheck_entry_vkGetDeviceQueue2;
    }
    if (!strcmp(name, "vkCreateSamplerYcbcrConversion"))
    {
        return (void*)dynCheck_entry_vkCreateSamplerYcbcrConversion;
    }
    if (!strcmp(name, "vkDestroySamplerYcbcrConversion"))
    {
        return (void*)dynCheck_entry_vkDestroySamplerYcbcrConversion;
    }
    if (!strcmp(name, "vkCreateDescriptorUpdateTemplate"))
    {
        return (void*)dynCheck_entry_vkCreateDescriptorUpdateTemplate;
    }
    if (!strcmp(name, "vkDestroyDescriptorUpdateTemplate"))
    {
        return (void*)dynCheck_entry_vkDestroyDescriptorUpdateTemplate;
    }
    if (!strcmp(name, "vkUpdateDescriptorSetWithTemplate"))
    {
        return (void*)dynCheck_entry_vkUpdateDescriptorSetWithTemplate;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceExternalBufferProperties"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetPhysicalDeviceExternalBufferProperties : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceExternalFenceProperties"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetPhysicalDeviceExternalFenceProperties : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceExternalSemaphoreProperties"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetPhysicalDeviceExternalSemaphoreProperties : nullptr;
    }
    if (!strcmp(name, "vkGetDescriptorSetLayoutSupport"))
    {
        return (void*)dynCheck_entry_vkGetDescriptorSetLayoutSupport;
    }
#endif
#ifdef VK_KHR_surface
    if (!strcmp(name, "vkDestroySurfaceKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_surface");
        return hasExt ? (void*)entry_vkDestroySurfaceKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSurfaceSupportKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_surface");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceSurfaceSupportKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSurfaceCapabilitiesKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_surface");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceSurfaceCapabilitiesKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSurfaceFormatsKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_surface");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceSurfaceFormatsKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSurfacePresentModesKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_surface");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceSurfacePresentModesKHR : nullptr;
    }
#endif
#ifdef VK_KHR_swapchain
    if (!strcmp(name, "vkCreateSwapchainKHR"))
    {
        return (void*)dynCheck_entry_vkCreateSwapchainKHR;
    }
    if (!strcmp(name, "vkDestroySwapchainKHR"))
    {
        return (void*)dynCheck_entry_vkDestroySwapchainKHR;
    }
    if (!strcmp(name, "vkGetSwapchainImagesKHR"))
    {
        return (void*)dynCheck_entry_vkGetSwapchainImagesKHR;
    }
    if (!strcmp(name, "vkAcquireNextImageKHR"))
    {
        return (void*)dynCheck_entry_vkAcquireNextImageKHR;
    }
    if (!strcmp(name, "vkQueuePresentKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_swapchain");
        return hasExt ? (void*)entry_vkQueuePresentKHR : nullptr;
    }
    if (!strcmp(name, "vkGetDeviceGroupPresentCapabilitiesKHR"))
    {
        return (void*)dynCheck_entry_vkGetDeviceGroupPresentCapabilitiesKHR;
    }
    if (!strcmp(name, "vkGetDeviceGroupSurfacePresentModesKHR"))
    {
        return (void*)dynCheck_entry_vkGetDeviceGroupSurfacePresentModesKHR;
    }
    if (!strcmp(name, "vkGetPhysicalDevicePresentRectanglesKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_swapchain");
        return hasExt ? (void*)entry_vkGetPhysicalDevicePresentRectanglesKHR : nullptr;
    }
    if (!strcmp(name, "vkAcquireNextImage2KHR"))
    {
        return (void*)dynCheck_entry_vkAcquireNextImage2KHR;
    }
#endif
#ifdef VK_KHR_display
    if (!strcmp(name, "vkGetPhysicalDeviceDisplayPropertiesKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_display");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceDisplayPropertiesKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceDisplayPlanePropertiesKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_display");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceDisplayPlanePropertiesKHR : nullptr;
    }
    if (!strcmp(name, "vkGetDisplayPlaneSupportedDisplaysKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_display");
        return hasExt ? (void*)entry_vkGetDisplayPlaneSupportedDisplaysKHR : nullptr;
    }
    if (!strcmp(name, "vkGetDisplayModePropertiesKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_display");
        return hasExt ? (void*)entry_vkGetDisplayModePropertiesKHR : nullptr;
    }
    if (!strcmp(name, "vkCreateDisplayModeKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_display");
        return hasExt ? (void*)entry_vkCreateDisplayModeKHR : nullptr;
    }
    if (!strcmp(name, "vkGetDisplayPlaneCapabilitiesKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_display");
        return hasExt ? (void*)entry_vkGetDisplayPlaneCapabilitiesKHR : nullptr;
    }
    if (!strcmp(name, "vkCreateDisplayPlaneSurfaceKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_display");
        return hasExt ? (void*)entry_vkCreateDisplayPlaneSurfaceKHR : nullptr;
    }
#endif
#ifdef VK_KHR_display_swapchain
    if (!strcmp(name, "vkCreateSharedSwapchainsKHR"))
    {
        return (void*)dynCheck_entry_vkCreateSharedSwapchainsKHR;
    }
#endif
#ifdef VK_KHR_xlib_surface
    if (!strcmp(name, "vkCreateXlibSurfaceKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_xlib_surface");
        return hasExt ? (void*)entry_vkCreateXlibSurfaceKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceXlibPresentationSupportKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_xlib_surface");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceXlibPresentationSupportKHR : nullptr;
    }
#endif
#ifdef VK_KHR_xcb_surface
    if (!strcmp(name, "vkCreateXcbSurfaceKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_xcb_surface");
        return hasExt ? (void*)entry_vkCreateXcbSurfaceKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceXcbPresentationSupportKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_xcb_surface");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceXcbPresentationSupportKHR : nullptr;
    }
#endif
#ifdef VK_KHR_wayland_surface
    if (!strcmp(name, "vkCreateWaylandSurfaceKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_wayland_surface");
        return hasExt ? (void*)entry_vkCreateWaylandSurfaceKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceWaylandPresentationSupportKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_wayland_surface");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceWaylandPresentationSupportKHR : nullptr;
    }
#endif
#ifdef VK_KHR_mir_surface
    if (!strcmp(name, "vkCreateMirSurfaceKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_mir_surface");
        return hasExt ? (void*)entry_vkCreateMirSurfaceKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceMirPresentationSupportKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_mir_surface");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceMirPresentationSupportKHR : nullptr;
    }
#endif
#ifdef VK_KHR_android_surface
    if (!strcmp(name, "vkCreateAndroidSurfaceKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_android_surface");
        return hasExt ? (void*)entry_vkCreateAndroidSurfaceKHR : nullptr;
    }
#endif
#ifdef VK_KHR_win32_surface
    if (!strcmp(name, "vkCreateWin32SurfaceKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_win32_surface");
        return hasExt ? (void*)entry_vkCreateWin32SurfaceKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceWin32PresentationSupportKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_win32_surface");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceWin32PresentationSupportKHR : nullptr;
    }
#endif
#ifdef VK_KHR_get_physical_device_properties2
    if (!strcmp(name, "vkGetPhysicalDeviceFeatures2KHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_get_physical_device_properties2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceFeatures2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceProperties2KHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_get_physical_device_properties2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceProperties2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceFormatProperties2KHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_get_physical_device_properties2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceFormatProperties2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceImageFormatProperties2KHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_get_physical_device_properties2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceImageFormatProperties2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceQueueFamilyProperties2KHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_get_physical_device_properties2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceQueueFamilyProperties2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceMemoryProperties2KHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_get_physical_device_properties2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceMemoryProperties2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSparseImageFormatProperties2KHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_get_physical_device_properties2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceSparseImageFormatProperties2KHR : nullptr;
    }
#endif
#ifdef VK_KHR_device_group
    if (!strcmp(name, "vkGetDeviceGroupPeerMemoryFeaturesKHR"))
    {
        return (void*)dynCheck_entry_vkGetDeviceGroupPeerMemoryFeaturesKHR;
    }
    if (!strcmp(name, "vkCmdSetDeviceMaskKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_device_group");
        return hasExt ? (void*)entry_vkCmdSetDeviceMaskKHR : nullptr;
    }
    if (!strcmp(name, "vkCmdDispatchBaseKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_device_group");
        return hasExt ? (void*)entry_vkCmdDispatchBaseKHR : nullptr;
    }
#endif
#ifdef VK_KHR_maintenance1
    if (!strcmp(name, "vkTrimCommandPoolKHR"))
    {
        return (void*)dynCheck_entry_vkTrimCommandPoolKHR;
    }
#endif
#ifdef VK_KHR_device_group_creation
    if (!strcmp(name, "vkEnumeratePhysicalDeviceGroupsKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_device_group_creation");
        return hasExt ? (void*)entry_vkEnumeratePhysicalDeviceGroupsKHR : nullptr;
    }
#endif
#ifdef VK_KHR_external_memory_capabilities
    if (!strcmp(name, "vkGetPhysicalDeviceExternalBufferPropertiesKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_external_memory_capabilities");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceExternalBufferPropertiesKHR : nullptr;
    }
#endif
#ifdef VK_KHR_external_memory_win32
    if (!strcmp(name, "vkGetMemoryWin32HandleKHR"))
    {
        return (void*)dynCheck_entry_vkGetMemoryWin32HandleKHR;
    }
    if (!strcmp(name, "vkGetMemoryWin32HandlePropertiesKHR"))
    {
        return (void*)dynCheck_entry_vkGetMemoryWin32HandlePropertiesKHR;
    }
#endif
#ifdef VK_KHR_external_memory_fd
    if (!strcmp(name, "vkGetMemoryFdKHR"))
    {
        return (void*)dynCheck_entry_vkGetMemoryFdKHR;
    }
    if (!strcmp(name, "vkGetMemoryFdPropertiesKHR"))
    {
        return (void*)dynCheck_entry_vkGetMemoryFdPropertiesKHR;
    }
#endif
#ifdef VK_KHR_external_semaphore_capabilities
    if (!strcmp(name, "vkGetPhysicalDeviceExternalSemaphorePropertiesKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_external_semaphore_capabilities");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceExternalSemaphorePropertiesKHR : nullptr;
    }
#endif
#ifdef VK_KHR_external_semaphore_win32
    if (!strcmp(name, "vkImportSemaphoreWin32HandleKHR"))
    {
        return (void*)dynCheck_entry_vkImportSemaphoreWin32HandleKHR;
    }
    if (!strcmp(name, "vkGetSemaphoreWin32HandleKHR"))
    {
        return (void*)dynCheck_entry_vkGetSemaphoreWin32HandleKHR;
    }
#endif
#ifdef VK_KHR_external_semaphore_fd
    if (!strcmp(name, "vkImportSemaphoreFdKHR"))
    {
        return (void*)dynCheck_entry_vkImportSemaphoreFdKHR;
    }
    if (!strcmp(name, "vkGetSemaphoreFdKHR"))
    {
        return (void*)dynCheck_entry_vkGetSemaphoreFdKHR;
    }
#endif
#ifdef VK_KHR_push_descriptor
    if (!strcmp(name, "vkCmdPushDescriptorSetKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_push_descriptor");
        return hasExt ? (void*)entry_vkCmdPushDescriptorSetKHR : nullptr;
    }
    if (!strcmp(name, "vkCmdPushDescriptorSetWithTemplateKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_push_descriptor");
        return hasExt ? (void*)entry_vkCmdPushDescriptorSetWithTemplateKHR : nullptr;
    }
#endif
#ifdef VK_KHR_descriptor_update_template
    if (!strcmp(name, "vkCreateDescriptorUpdateTemplateKHR"))
    {
        return (void*)dynCheck_entry_vkCreateDescriptorUpdateTemplateKHR;
    }
    if (!strcmp(name, "vkDestroyDescriptorUpdateTemplateKHR"))
    {
        return (void*)dynCheck_entry_vkDestroyDescriptorUpdateTemplateKHR;
    }
    if (!strcmp(name, "vkUpdateDescriptorSetWithTemplateKHR"))
    {
        return (void*)dynCheck_entry_vkUpdateDescriptorSetWithTemplateKHR;
    }
#endif
#ifdef VK_KHR_create_renderpass2
    if (!strcmp(name, "vkCreateRenderPass2KHR"))
    {
        return (void*)dynCheck_entry_vkCreateRenderPass2KHR;
    }
    if (!strcmp(name, "vkCmdBeginRenderPass2KHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_create_renderpass2");
        return hasExt ? (void*)entry_vkCmdBeginRenderPass2KHR : nullptr;
    }
    if (!strcmp(name, "vkCmdNextSubpass2KHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_create_renderpass2");
        return hasExt ? (void*)entry_vkCmdNextSubpass2KHR : nullptr;
    }
    if (!strcmp(name, "vkCmdEndRenderPass2KHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_create_renderpass2");
        return hasExt ? (void*)entry_vkCmdEndRenderPass2KHR : nullptr;
    }
#endif
#ifdef VK_KHR_shared_presentable_image
    if (!strcmp(name, "vkGetSwapchainStatusKHR"))
    {
        return (void*)dynCheck_entry_vkGetSwapchainStatusKHR;
    }
#endif
#ifdef VK_KHR_external_fence_capabilities
    if (!strcmp(name, "vkGetPhysicalDeviceExternalFencePropertiesKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_external_fence_capabilities");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceExternalFencePropertiesKHR : nullptr;
    }
#endif
#ifdef VK_KHR_external_fence_win32
    if (!strcmp(name, "vkImportFenceWin32HandleKHR"))
    {
        return (void*)dynCheck_entry_vkImportFenceWin32HandleKHR;
    }
    if (!strcmp(name, "vkGetFenceWin32HandleKHR"))
    {
        return (void*)dynCheck_entry_vkGetFenceWin32HandleKHR;
    }
#endif
#ifdef VK_KHR_external_fence_fd
    if (!strcmp(name, "vkImportFenceFdKHR"))
    {
        return (void*)dynCheck_entry_vkImportFenceFdKHR;
    }
    if (!strcmp(name, "vkGetFenceFdKHR"))
    {
        return (void*)dynCheck_entry_vkGetFenceFdKHR;
    }
#endif
#ifdef VK_KHR_get_surface_capabilities2
    if (!strcmp(name, "vkGetPhysicalDeviceSurfaceCapabilities2KHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_get_surface_capabilities2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceSurfaceCapabilities2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSurfaceFormats2KHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_get_surface_capabilities2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceSurfaceFormats2KHR : nullptr;
    }
#endif
#ifdef VK_KHR_get_display_properties2
    if (!strcmp(name, "vkGetPhysicalDeviceDisplayProperties2KHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_get_display_properties2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceDisplayProperties2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceDisplayPlaneProperties2KHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_get_display_properties2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceDisplayPlaneProperties2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetDisplayModeProperties2KHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_get_display_properties2");
        return hasExt ? (void*)entry_vkGetDisplayModeProperties2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetDisplayPlaneCapabilities2KHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_get_display_properties2");
        return hasExt ? (void*)entry_vkGetDisplayPlaneCapabilities2KHR : nullptr;
    }
#endif
#ifdef VK_KHR_get_memory_requirements2
    if (!strcmp(name, "vkGetImageMemoryRequirements2KHR"))
    {
        return (void*)dynCheck_entry_vkGetImageMemoryRequirements2KHR;
    }
    if (!strcmp(name, "vkGetBufferMemoryRequirements2KHR"))
    {
        return (void*)dynCheck_entry_vkGetBufferMemoryRequirements2KHR;
    }
    if (!strcmp(name, "vkGetImageSparseMemoryRequirements2KHR"))
    {
        return (void*)dynCheck_entry_vkGetImageSparseMemoryRequirements2KHR;
    }
#endif
#ifdef VK_KHR_sampler_ycbcr_conversion
    if (!strcmp(name, "vkCreateSamplerYcbcrConversionKHR"))
    {
        return (void*)dynCheck_entry_vkCreateSamplerYcbcrConversionKHR;
    }
    if (!strcmp(name, "vkDestroySamplerYcbcrConversionKHR"))
    {
        return (void*)dynCheck_entry_vkDestroySamplerYcbcrConversionKHR;
    }
#endif
#ifdef VK_KHR_bind_memory2
    if (!strcmp(name, "vkBindBufferMemory2KHR"))
    {
        return (void*)dynCheck_entry_vkBindBufferMemory2KHR;
    }
    if (!strcmp(name, "vkBindImageMemory2KHR"))
    {
        return (void*)dynCheck_entry_vkBindImageMemory2KHR;
    }
#endif
#ifdef VK_KHR_maintenance3
    if (!strcmp(name, "vkGetDescriptorSetLayoutSupportKHR"))
    {
        return (void*)dynCheck_entry_vkGetDescriptorSetLayoutSupportKHR;
    }
#endif
#ifdef VK_KHR_draw_indirect_count
    if (!strcmp(name, "vkCmdDrawIndirectCountKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_draw_indirect_count");
        return hasExt ? (void*)entry_vkCmdDrawIndirectCountKHR : nullptr;
    }
    if (!strcmp(name, "vkCmdDrawIndexedIndirectCountKHR"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_KHR_draw_indirect_count");
        return hasExt ? (void*)entry_vkCmdDrawIndexedIndirectCountKHR : nullptr;
    }
#endif
#ifdef VK_ANDROID_native_buffer
    if (!strcmp(name, "vkGetSwapchainGrallocUsageANDROID"))
    {
        return (void*)dynCheck_entry_vkGetSwapchainGrallocUsageANDROID;
    }
    if (!strcmp(name, "vkAcquireImageANDROID"))
    {
        return (void*)dynCheck_entry_vkAcquireImageANDROID;
    }
    if (!strcmp(name, "vkQueueSignalReleaseImageANDROID"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_ANDROID_native_buffer");
        return hasExt ? (void*)entry_vkQueueSignalReleaseImageANDROID : nullptr;
    }
#endif
#ifdef VK_EXT_debug_report
    if (!strcmp(name, "vkCreateDebugReportCallbackEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_debug_report");
        return hasExt ? (void*)entry_vkCreateDebugReportCallbackEXT : nullptr;
    }
    if (!strcmp(name, "vkDestroyDebugReportCallbackEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_debug_report");
        return hasExt ? (void*)entry_vkDestroyDebugReportCallbackEXT : nullptr;
    }
    if (!strcmp(name, "vkDebugReportMessageEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_debug_report");
        return hasExt ? (void*)entry_vkDebugReportMessageEXT : nullptr;
    }
#endif
#ifdef VK_EXT_debug_marker
    if (!strcmp(name, "vkDebugMarkerSetObjectTagEXT"))
    {
        return (void*)dynCheck_entry_vkDebugMarkerSetObjectTagEXT;
    }
    if (!strcmp(name, "vkDebugMarkerSetObjectNameEXT"))
    {
        return (void*)dynCheck_entry_vkDebugMarkerSetObjectNameEXT;
    }
    if (!strcmp(name, "vkCmdDebugMarkerBeginEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_debug_marker");
        return hasExt ? (void*)entry_vkCmdDebugMarkerBeginEXT : nullptr;
    }
    if (!strcmp(name, "vkCmdDebugMarkerEndEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_debug_marker");
        return hasExt ? (void*)entry_vkCmdDebugMarkerEndEXT : nullptr;
    }
    if (!strcmp(name, "vkCmdDebugMarkerInsertEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_debug_marker");
        return hasExt ? (void*)entry_vkCmdDebugMarkerInsertEXT : nullptr;
    }
#endif
#ifdef VK_AMD_draw_indirect_count
    if (!strcmp(name, "vkCmdDrawIndirectCountAMD"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_AMD_draw_indirect_count");
        return hasExt ? (void*)entry_vkCmdDrawIndirectCountAMD : nullptr;
    }
    if (!strcmp(name, "vkCmdDrawIndexedIndirectCountAMD"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_AMD_draw_indirect_count");
        return hasExt ? (void*)entry_vkCmdDrawIndexedIndirectCountAMD : nullptr;
    }
#endif
#ifdef VK_AMD_shader_info
    if (!strcmp(name, "vkGetShaderInfoAMD"))
    {
        return (void*)dynCheck_entry_vkGetShaderInfoAMD;
    }
#endif
#ifdef VK_NV_external_memory_capabilities
    if (!strcmp(name, "vkGetPhysicalDeviceExternalImageFormatPropertiesNV"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_NV_external_memory_capabilities");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceExternalImageFormatPropertiesNV : nullptr;
    }
#endif
#ifdef VK_NV_external_memory_win32
    if (!strcmp(name, "vkGetMemoryWin32HandleNV"))
    {
        return (void*)dynCheck_entry_vkGetMemoryWin32HandleNV;
    }
#endif
#ifdef VK_NN_vi_surface
    if (!strcmp(name, "vkCreateViSurfaceNN"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_NN_vi_surface");
        return hasExt ? (void*)entry_vkCreateViSurfaceNN : nullptr;
    }
#endif
#ifdef VK_EXT_conditional_rendering
    if (!strcmp(name, "vkCmdBeginConditionalRenderingEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_conditional_rendering");
        return hasExt ? (void*)entry_vkCmdBeginConditionalRenderingEXT : nullptr;
    }
    if (!strcmp(name, "vkCmdEndConditionalRenderingEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_conditional_rendering");
        return hasExt ? (void*)entry_vkCmdEndConditionalRenderingEXT : nullptr;
    }
#endif
#ifdef VK_NVX_device_generated_commands
    if (!strcmp(name, "vkCmdProcessCommandsNVX"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_NVX_device_generated_commands");
        return hasExt ? (void*)entry_vkCmdProcessCommandsNVX : nullptr;
    }
    if (!strcmp(name, "vkCmdReserveSpaceForCommandsNVX"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_NVX_device_generated_commands");
        return hasExt ? (void*)entry_vkCmdReserveSpaceForCommandsNVX : nullptr;
    }
    if (!strcmp(name, "vkCreateIndirectCommandsLayoutNVX"))
    {
        return (void*)dynCheck_entry_vkCreateIndirectCommandsLayoutNVX;
    }
    if (!strcmp(name, "vkDestroyIndirectCommandsLayoutNVX"))
    {
        return (void*)dynCheck_entry_vkDestroyIndirectCommandsLayoutNVX;
    }
    if (!strcmp(name, "vkCreateObjectTableNVX"))
    {
        return (void*)dynCheck_entry_vkCreateObjectTableNVX;
    }
    if (!strcmp(name, "vkDestroyObjectTableNVX"))
    {
        return (void*)dynCheck_entry_vkDestroyObjectTableNVX;
    }
    if (!strcmp(name, "vkRegisterObjectsNVX"))
    {
        return (void*)dynCheck_entry_vkRegisterObjectsNVX;
    }
    if (!strcmp(name, "vkUnregisterObjectsNVX"))
    {
        return (void*)dynCheck_entry_vkUnregisterObjectsNVX;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceGeneratedCommandsPropertiesNVX"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_NVX_device_generated_commands");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceGeneratedCommandsPropertiesNVX : nullptr;
    }
#endif
#ifdef VK_NV_clip_space_w_scaling
    if (!strcmp(name, "vkCmdSetViewportWScalingNV"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_NV_clip_space_w_scaling");
        return hasExt ? (void*)entry_vkCmdSetViewportWScalingNV : nullptr;
    }
#endif
#ifdef VK_EXT_direct_mode_display
    if (!strcmp(name, "vkReleaseDisplayEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_direct_mode_display");
        return hasExt ? (void*)entry_vkReleaseDisplayEXT : nullptr;
    }
#endif
#ifdef VK_EXT_acquire_xlib_display
    if (!strcmp(name, "vkAcquireXlibDisplayEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_acquire_xlib_display");
        return hasExt ? (void*)entry_vkAcquireXlibDisplayEXT : nullptr;
    }
    if (!strcmp(name, "vkGetRandROutputDisplayEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_acquire_xlib_display");
        return hasExt ? (void*)entry_vkGetRandROutputDisplayEXT : nullptr;
    }
#endif
#ifdef VK_EXT_display_surface_counter
    if (!strcmp(name, "vkGetPhysicalDeviceSurfaceCapabilities2EXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_display_surface_counter");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceSurfaceCapabilities2EXT : nullptr;
    }
#endif
#ifdef VK_EXT_display_control
    if (!strcmp(name, "vkDisplayPowerControlEXT"))
    {
        return (void*)dynCheck_entry_vkDisplayPowerControlEXT;
    }
    if (!strcmp(name, "vkRegisterDeviceEventEXT"))
    {
        return (void*)dynCheck_entry_vkRegisterDeviceEventEXT;
    }
    if (!strcmp(name, "vkRegisterDisplayEventEXT"))
    {
        return (void*)dynCheck_entry_vkRegisterDisplayEventEXT;
    }
    if (!strcmp(name, "vkGetSwapchainCounterEXT"))
    {
        return (void*)dynCheck_entry_vkGetSwapchainCounterEXT;
    }
#endif
#ifdef VK_GOOGLE_display_timing
    if (!strcmp(name, "vkGetRefreshCycleDurationGOOGLE"))
    {
        return (void*)dynCheck_entry_vkGetRefreshCycleDurationGOOGLE;
    }
    if (!strcmp(name, "vkGetPastPresentationTimingGOOGLE"))
    {
        return (void*)dynCheck_entry_vkGetPastPresentationTimingGOOGLE;
    }
#endif
#ifdef VK_EXT_discard_rectangles
    if (!strcmp(name, "vkCmdSetDiscardRectangleEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_discard_rectangles");
        return hasExt ? (void*)entry_vkCmdSetDiscardRectangleEXT : nullptr;
    }
#endif
#ifdef VK_EXT_hdr_metadata
    if (!strcmp(name, "vkSetHdrMetadataEXT"))
    {
        return (void*)dynCheck_entry_vkSetHdrMetadataEXT;
    }
#endif
#ifdef VK_MVK_ios_surface
    if (!strcmp(name, "vkCreateIOSSurfaceMVK"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_MVK_ios_surface");
        return hasExt ? (void*)entry_vkCreateIOSSurfaceMVK : nullptr;
    }
#endif
#ifdef VK_MVK_macos_surface
    if (!strcmp(name, "vkCreateMacOSSurfaceMVK"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_MVK_macos_surface");
        return hasExt ? (void*)entry_vkCreateMacOSSurfaceMVK : nullptr;
    }
#endif
#ifdef VK_EXT_debug_utils
    if (!strcmp(name, "vkSetDebugUtilsObjectNameEXT"))
    {
        return (void*)dynCheck_entry_vkSetDebugUtilsObjectNameEXT;
    }
    if (!strcmp(name, "vkSetDebugUtilsObjectTagEXT"))
    {
        return (void*)dynCheck_entry_vkSetDebugUtilsObjectTagEXT;
    }
    if (!strcmp(name, "vkQueueBeginDebugUtilsLabelEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_debug_utils");
        return hasExt ? (void*)entry_vkQueueBeginDebugUtilsLabelEXT : nullptr;
    }
    if (!strcmp(name, "vkQueueEndDebugUtilsLabelEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_debug_utils");
        return hasExt ? (void*)entry_vkQueueEndDebugUtilsLabelEXT : nullptr;
    }
    if (!strcmp(name, "vkQueueInsertDebugUtilsLabelEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_debug_utils");
        return hasExt ? (void*)entry_vkQueueInsertDebugUtilsLabelEXT : nullptr;
    }
    if (!strcmp(name, "vkCmdBeginDebugUtilsLabelEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_debug_utils");
        return hasExt ? (void*)entry_vkCmdBeginDebugUtilsLabelEXT : nullptr;
    }
    if (!strcmp(name, "vkCmdEndDebugUtilsLabelEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_debug_utils");
        return hasExt ? (void*)entry_vkCmdEndDebugUtilsLabelEXT : nullptr;
    }
    if (!strcmp(name, "vkCmdInsertDebugUtilsLabelEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_debug_utils");
        return hasExt ? (void*)entry_vkCmdInsertDebugUtilsLabelEXT : nullptr;
    }
    if (!strcmp(name, "vkCreateDebugUtilsMessengerEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_debug_utils");
        return hasExt ? (void*)entry_vkCreateDebugUtilsMessengerEXT : nullptr;
    }
    if (!strcmp(name, "vkDestroyDebugUtilsMessengerEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_debug_utils");
        return hasExt ? (void*)entry_vkDestroyDebugUtilsMessengerEXT : nullptr;
    }
    if (!strcmp(name, "vkSubmitDebugUtilsMessageEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_debug_utils");
        return hasExt ? (void*)entry_vkSubmitDebugUtilsMessageEXT : nullptr;
    }
#endif
#ifdef VK_ANDROID_external_memory_android_hardware_buffer
    if (!strcmp(name, "vkGetAndroidHardwareBufferPropertiesANDROID"))
    {
        return (void*)dynCheck_entry_vkGetAndroidHardwareBufferPropertiesANDROID;
    }
    if (!strcmp(name, "vkGetMemoryAndroidHardwareBufferANDROID"))
    {
        return (void*)dynCheck_entry_vkGetMemoryAndroidHardwareBufferANDROID;
    }
#endif
#ifdef VK_EXT_sample_locations
    if (!strcmp(name, "vkCmdSetSampleLocationsEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_sample_locations");
        return hasExt ? (void*)entry_vkCmdSetSampleLocationsEXT : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceMultisamplePropertiesEXT"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_EXT_sample_locations");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceMultisamplePropertiesEXT : nullptr;
    }
#endif
#ifdef VK_EXT_validation_cache
    if (!strcmp(name, "vkCreateValidationCacheEXT"))
    {
        return (void*)dynCheck_entry_vkCreateValidationCacheEXT;
    }
    if (!strcmp(name, "vkDestroyValidationCacheEXT"))
    {
        return (void*)dynCheck_entry_vkDestroyValidationCacheEXT;
    }
    if (!strcmp(name, "vkMergeValidationCachesEXT"))
    {
        return (void*)dynCheck_entry_vkMergeValidationCachesEXT;
    }
    if (!strcmp(name, "vkGetValidationCacheDataEXT"))
    {
        return (void*)dynCheck_entry_vkGetValidationCacheDataEXT;
    }
#endif
#ifdef VK_EXT_external_memory_host
    if (!strcmp(name, "vkGetMemoryHostPointerPropertiesEXT"))
    {
        return (void*)dynCheck_entry_vkGetMemoryHostPointerPropertiesEXT;
    }
#endif
#ifdef VK_AMD_buffer_marker
    if (!strcmp(name, "vkCmdWriteBufferMarkerAMD"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_AMD_buffer_marker");
        return hasExt ? (void*)entry_vkCmdWriteBufferMarkerAMD : nullptr;
    }
#endif
#ifdef VK_NV_device_diagnostic_checkpoints
    if (!strcmp(name, "vkCmdSetCheckpointNV"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_NV_device_diagnostic_checkpoints");
        return hasExt ? (void*)entry_vkCmdSetCheckpointNV : nullptr;
    }
    if (!strcmp(name, "vkGetQueueCheckpointDataNV"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_NV_device_diagnostic_checkpoints");
        return hasExt ? (void*)entry_vkGetQueueCheckpointDataNV : nullptr;
    }
#endif
#ifdef VK_GOOGLE_address_space
    if (!strcmp(name, "vkMapMemoryIntoAddressSpaceGOOGLE"))
    {
        return (void*)dynCheck_entry_vkMapMemoryIntoAddressSpaceGOOGLE;
    }
#endif
#ifdef VK_GOOGLE_color_buffer
    if (!strcmp(name, "vkRegisterImageColorBufferGOOGLE"))
    {
        return (void*)dynCheck_entry_vkRegisterImageColorBufferGOOGLE;
    }
    if (!strcmp(name, "vkRegisterBufferColorBufferGOOGLE"))
    {
        return (void*)dynCheck_entry_vkRegisterBufferColorBufferGOOGLE;
    }
#endif
#ifdef VK_GOOGLE_sized_descriptor_update_template
    if (!strcmp(name, "vkUpdateDescriptorSetWithTemplateSizedGOOGLE"))
    {
        return (void*)dynCheck_entry_vkUpdateDescriptorSetWithTemplateSizedGOOGLE;
    }
#endif
#ifdef VK_GOOGLE_async_command_buffers
    if (!strcmp(name, "vkBeginCommandBufferAsyncGOOGLE"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_GOOGLE_async_command_buffers");
        return hasExt ? (void*)entry_vkBeginCommandBufferAsyncGOOGLE : nullptr;
    }
    if (!strcmp(name, "vkEndCommandBufferAsyncGOOGLE"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_GOOGLE_async_command_buffers");
        return hasExt ? (void*)entry_vkEndCommandBufferAsyncGOOGLE : nullptr;
    }
    if (!strcmp(name, "vkResetCommandBufferAsyncGOOGLE"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_GOOGLE_async_command_buffers");
        return hasExt ? (void*)entry_vkResetCommandBufferAsyncGOOGLE : nullptr;
    }
    if (!strcmp(name, "vkCommandBufferHostSyncGOOGLE"))
    {
        bool hasExt = resources->hasInstanceExtension(instance, "VK_GOOGLE_async_command_buffers");
        return hasExt ? (void*)entry_vkCommandBufferHostSyncGOOGLE : nullptr;
    }
#endif
#ifdef VK_GOOGLE_create_resources_with_requirements
    if (!strcmp(name, "vkCreateImageWithRequirementsGOOGLE"))
    {
        return (void*)dynCheck_entry_vkCreateImageWithRequirementsGOOGLE;
    }
    if (!strcmp(name, "vkCreateBufferWithRequirementsGOOGLE"))
    {
        return (void*)dynCheck_entry_vkCreateBufferWithRequirementsGOOGLE;
    }
#endif
#ifdef VK_GOOGLE_address_space_info
    if (!strcmp(name, "vkGetMemoryHostAddressInfoGOOGLE"))
    {
        return (void*)dynCheck_entry_vkGetMemoryHostAddressInfoGOOGLE;
    }
#endif
#ifdef VK_GOOGLE_free_memory_sync
    if (!strcmp(name, "vkFreeMemorySyncGOOGLE"))
    {
        return (void*)dynCheck_entry_vkFreeMemorySyncGOOGLE;
    }
#endif
    return nullptr;
}
void* goldfish_vulkan_get_device_proc_address(VkDevice device, const char* name){
    auto resources = ResourceTracker::get();
    bool has1_1OrHigher = resources->getApiVersionFromDevice(device) >= VK_API_VERSION_1_1;
#ifdef VK_VERSION_1_0
    if (!strcmp(name, "vkCreateInstance"))
    {
        return (void*)entry_vkCreateInstance;
    }
    if (!strcmp(name, "vkDestroyInstance"))
    {
        return (void*)entry_vkDestroyInstance;
    }
    if (!strcmp(name, "vkEnumeratePhysicalDevices"))
    {
        return (void*)entry_vkEnumeratePhysicalDevices;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceFeatures"))
    {
        return (void*)entry_vkGetPhysicalDeviceFeatures;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceFormatProperties"))
    {
        return (void*)entry_vkGetPhysicalDeviceFormatProperties;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceImageFormatProperties"))
    {
        return (void*)entry_vkGetPhysicalDeviceImageFormatProperties;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceProperties"))
    {
        return (void*)entry_vkGetPhysicalDeviceProperties;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceQueueFamilyProperties"))
    {
        return (void*)entry_vkGetPhysicalDeviceQueueFamilyProperties;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceMemoryProperties"))
    {
        return (void*)entry_vkGetPhysicalDeviceMemoryProperties;
    }
    if (!strcmp(name, "vkGetInstanceProcAddr"))
    {
        return (void*)entry_vkGetInstanceProcAddr;
    }
    if (!strcmp(name, "vkGetDeviceProcAddr"))
    {
        return (void*)entry_vkGetDeviceProcAddr;
    }
    if (!strcmp(name, "vkCreateDevice"))
    {
        return (void*)entry_vkCreateDevice;
    }
    if (!strcmp(name, "vkDestroyDevice"))
    {
        return (void*)entry_vkDestroyDevice;
    }
    if (!strcmp(name, "vkEnumerateInstanceExtensionProperties"))
    {
        return (void*)entry_vkEnumerateInstanceExtensionProperties;
    }
    if (!strcmp(name, "vkEnumerateDeviceExtensionProperties"))
    {
        return (void*)entry_vkEnumerateDeviceExtensionProperties;
    }
    if (!strcmp(name, "vkEnumerateInstanceLayerProperties"))
    {
        return (void*)entry_vkEnumerateInstanceLayerProperties;
    }
    if (!strcmp(name, "vkEnumerateDeviceLayerProperties"))
    {
        return (void*)entry_vkEnumerateDeviceLayerProperties;
    }
    if (!strcmp(name, "vkGetDeviceQueue"))
    {
        return (void*)entry_vkGetDeviceQueue;
    }
    if (!strcmp(name, "vkQueueSubmit"))
    {
        return (void*)entry_vkQueueSubmit;
    }
    if (!strcmp(name, "vkQueueWaitIdle"))
    {
        return (void*)entry_vkQueueWaitIdle;
    }
    if (!strcmp(name, "vkDeviceWaitIdle"))
    {
        return (void*)entry_vkDeviceWaitIdle;
    }
    if (!strcmp(name, "vkAllocateMemory"))
    {
        return (void*)entry_vkAllocateMemory;
    }
    if (!strcmp(name, "vkFreeMemory"))
    {
        return (void*)entry_vkFreeMemory;
    }
    if (!strcmp(name, "vkMapMemory"))
    {
        return (void*)entry_vkMapMemory;
    }
    if (!strcmp(name, "vkUnmapMemory"))
    {
        return (void*)entry_vkUnmapMemory;
    }
    if (!strcmp(name, "vkFlushMappedMemoryRanges"))
    {
        return (void*)entry_vkFlushMappedMemoryRanges;
    }
    if (!strcmp(name, "vkInvalidateMappedMemoryRanges"))
    {
        return (void*)entry_vkInvalidateMappedMemoryRanges;
    }
    if (!strcmp(name, "vkGetDeviceMemoryCommitment"))
    {
        return (void*)entry_vkGetDeviceMemoryCommitment;
    }
    if (!strcmp(name, "vkBindBufferMemory"))
    {
        return (void*)entry_vkBindBufferMemory;
    }
    if (!strcmp(name, "vkBindImageMemory"))
    {
        return (void*)entry_vkBindImageMemory;
    }
    if (!strcmp(name, "vkGetBufferMemoryRequirements"))
    {
        return (void*)entry_vkGetBufferMemoryRequirements;
    }
    if (!strcmp(name, "vkGetImageMemoryRequirements"))
    {
        return (void*)entry_vkGetImageMemoryRequirements;
    }
    if (!strcmp(name, "vkGetImageSparseMemoryRequirements"))
    {
        return (void*)entry_vkGetImageSparseMemoryRequirements;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSparseImageFormatProperties"))
    {
        return (void*)entry_vkGetPhysicalDeviceSparseImageFormatProperties;
    }
    if (!strcmp(name, "vkQueueBindSparse"))
    {
        return (void*)entry_vkQueueBindSparse;
    }
    if (!strcmp(name, "vkCreateFence"))
    {
        return (void*)entry_vkCreateFence;
    }
    if (!strcmp(name, "vkDestroyFence"))
    {
        return (void*)entry_vkDestroyFence;
    }
    if (!strcmp(name, "vkResetFences"))
    {
        return (void*)entry_vkResetFences;
    }
    if (!strcmp(name, "vkGetFenceStatus"))
    {
        return (void*)entry_vkGetFenceStatus;
    }
    if (!strcmp(name, "vkWaitForFences"))
    {
        return (void*)entry_vkWaitForFences;
    }
    if (!strcmp(name, "vkCreateSemaphore"))
    {
        return (void*)entry_vkCreateSemaphore;
    }
    if (!strcmp(name, "vkDestroySemaphore"))
    {
        return (void*)entry_vkDestroySemaphore;
    }
    if (!strcmp(name, "vkCreateEvent"))
    {
        return (void*)entry_vkCreateEvent;
    }
    if (!strcmp(name, "vkDestroyEvent"))
    {
        return (void*)entry_vkDestroyEvent;
    }
    if (!strcmp(name, "vkGetEventStatus"))
    {
        return (void*)entry_vkGetEventStatus;
    }
    if (!strcmp(name, "vkSetEvent"))
    {
        return (void*)entry_vkSetEvent;
    }
    if (!strcmp(name, "vkResetEvent"))
    {
        return (void*)entry_vkResetEvent;
    }
    if (!strcmp(name, "vkCreateQueryPool"))
    {
        return (void*)entry_vkCreateQueryPool;
    }
    if (!strcmp(name, "vkDestroyQueryPool"))
    {
        return (void*)entry_vkDestroyQueryPool;
    }
    if (!strcmp(name, "vkGetQueryPoolResults"))
    {
        return (void*)entry_vkGetQueryPoolResults;
    }
    if (!strcmp(name, "vkCreateBuffer"))
    {
        return (void*)entry_vkCreateBuffer;
    }
    if (!strcmp(name, "vkDestroyBuffer"))
    {
        return (void*)entry_vkDestroyBuffer;
    }
    if (!strcmp(name, "vkCreateBufferView"))
    {
        return (void*)entry_vkCreateBufferView;
    }
    if (!strcmp(name, "vkDestroyBufferView"))
    {
        return (void*)entry_vkDestroyBufferView;
    }
    if (!strcmp(name, "vkCreateImage"))
    {
        return (void*)entry_vkCreateImage;
    }
    if (!strcmp(name, "vkDestroyImage"))
    {
        return (void*)entry_vkDestroyImage;
    }
    if (!strcmp(name, "vkGetImageSubresourceLayout"))
    {
        return (void*)entry_vkGetImageSubresourceLayout;
    }
    if (!strcmp(name, "vkCreateImageView"))
    {
        return (void*)entry_vkCreateImageView;
    }
    if (!strcmp(name, "vkDestroyImageView"))
    {
        return (void*)entry_vkDestroyImageView;
    }
    if (!strcmp(name, "vkCreateShaderModule"))
    {
        return (void*)entry_vkCreateShaderModule;
    }
    if (!strcmp(name, "vkDestroyShaderModule"))
    {
        return (void*)entry_vkDestroyShaderModule;
    }
    if (!strcmp(name, "vkCreatePipelineCache"))
    {
        return (void*)entry_vkCreatePipelineCache;
    }
    if (!strcmp(name, "vkDestroyPipelineCache"))
    {
        return (void*)entry_vkDestroyPipelineCache;
    }
    if (!strcmp(name, "vkGetPipelineCacheData"))
    {
        return (void*)entry_vkGetPipelineCacheData;
    }
    if (!strcmp(name, "vkMergePipelineCaches"))
    {
        return (void*)entry_vkMergePipelineCaches;
    }
    if (!strcmp(name, "vkCreateGraphicsPipelines"))
    {
        return (void*)entry_vkCreateGraphicsPipelines;
    }
    if (!strcmp(name, "vkCreateComputePipelines"))
    {
        return (void*)entry_vkCreateComputePipelines;
    }
    if (!strcmp(name, "vkDestroyPipeline"))
    {
        return (void*)entry_vkDestroyPipeline;
    }
    if (!strcmp(name, "vkCreatePipelineLayout"))
    {
        return (void*)entry_vkCreatePipelineLayout;
    }
    if (!strcmp(name, "vkDestroyPipelineLayout"))
    {
        return (void*)entry_vkDestroyPipelineLayout;
    }
    if (!strcmp(name, "vkCreateSampler"))
    {
        return (void*)entry_vkCreateSampler;
    }
    if (!strcmp(name, "vkDestroySampler"))
    {
        return (void*)entry_vkDestroySampler;
    }
    if (!strcmp(name, "vkCreateDescriptorSetLayout"))
    {
        return (void*)entry_vkCreateDescriptorSetLayout;
    }
    if (!strcmp(name, "vkDestroyDescriptorSetLayout"))
    {
        return (void*)entry_vkDestroyDescriptorSetLayout;
    }
    if (!strcmp(name, "vkCreateDescriptorPool"))
    {
        return (void*)entry_vkCreateDescriptorPool;
    }
    if (!strcmp(name, "vkDestroyDescriptorPool"))
    {
        return (void*)entry_vkDestroyDescriptorPool;
    }
    if (!strcmp(name, "vkResetDescriptorPool"))
    {
        return (void*)entry_vkResetDescriptorPool;
    }
    if (!strcmp(name, "vkAllocateDescriptorSets"))
    {
        return (void*)entry_vkAllocateDescriptorSets;
    }
    if (!strcmp(name, "vkFreeDescriptorSets"))
    {
        return (void*)entry_vkFreeDescriptorSets;
    }
    if (!strcmp(name, "vkUpdateDescriptorSets"))
    {
        return (void*)entry_vkUpdateDescriptorSets;
    }
    if (!strcmp(name, "vkCreateFramebuffer"))
    {
        return (void*)entry_vkCreateFramebuffer;
    }
    if (!strcmp(name, "vkDestroyFramebuffer"))
    {
        return (void*)entry_vkDestroyFramebuffer;
    }
    if (!strcmp(name, "vkCreateRenderPass"))
    {
        return (void*)entry_vkCreateRenderPass;
    }
    if (!strcmp(name, "vkDestroyRenderPass"))
    {
        return (void*)entry_vkDestroyRenderPass;
    }
    if (!strcmp(name, "vkGetRenderAreaGranularity"))
    {
        return (void*)entry_vkGetRenderAreaGranularity;
    }
    if (!strcmp(name, "vkCreateCommandPool"))
    {
        return (void*)entry_vkCreateCommandPool;
    }
    if (!strcmp(name, "vkDestroyCommandPool"))
    {
        return (void*)entry_vkDestroyCommandPool;
    }
    if (!strcmp(name, "vkResetCommandPool"))
    {
        return (void*)entry_vkResetCommandPool;
    }
    if (!strcmp(name, "vkAllocateCommandBuffers"))
    {
        return (void*)entry_vkAllocateCommandBuffers;
    }
    if (!strcmp(name, "vkFreeCommandBuffers"))
    {
        return (void*)entry_vkFreeCommandBuffers;
    }
    if (!strcmp(name, "vkBeginCommandBuffer"))
    {
        return (void*)entry_vkBeginCommandBuffer;
    }
    if (!strcmp(name, "vkEndCommandBuffer"))
    {
        return (void*)entry_vkEndCommandBuffer;
    }
    if (!strcmp(name, "vkResetCommandBuffer"))
    {
        return (void*)entry_vkResetCommandBuffer;
    }
    if (!strcmp(name, "vkCmdBindPipeline"))
    {
        return (void*)entry_vkCmdBindPipeline;
    }
    if (!strcmp(name, "vkCmdSetViewport"))
    {
        return (void*)entry_vkCmdSetViewport;
    }
    if (!strcmp(name, "vkCmdSetScissor"))
    {
        return (void*)entry_vkCmdSetScissor;
    }
    if (!strcmp(name, "vkCmdSetLineWidth"))
    {
        return (void*)entry_vkCmdSetLineWidth;
    }
    if (!strcmp(name, "vkCmdSetDepthBias"))
    {
        return (void*)entry_vkCmdSetDepthBias;
    }
    if (!strcmp(name, "vkCmdSetBlendConstants"))
    {
        return (void*)entry_vkCmdSetBlendConstants;
    }
    if (!strcmp(name, "vkCmdSetDepthBounds"))
    {
        return (void*)entry_vkCmdSetDepthBounds;
    }
    if (!strcmp(name, "vkCmdSetStencilCompareMask"))
    {
        return (void*)entry_vkCmdSetStencilCompareMask;
    }
    if (!strcmp(name, "vkCmdSetStencilWriteMask"))
    {
        return (void*)entry_vkCmdSetStencilWriteMask;
    }
    if (!strcmp(name, "vkCmdSetStencilReference"))
    {
        return (void*)entry_vkCmdSetStencilReference;
    }
    if (!strcmp(name, "vkCmdBindDescriptorSets"))
    {
        return (void*)entry_vkCmdBindDescriptorSets;
    }
    if (!strcmp(name, "vkCmdBindIndexBuffer"))
    {
        return (void*)entry_vkCmdBindIndexBuffer;
    }
    if (!strcmp(name, "vkCmdBindVertexBuffers"))
    {
        return (void*)entry_vkCmdBindVertexBuffers;
    }
    if (!strcmp(name, "vkCmdDraw"))
    {
        return (void*)entry_vkCmdDraw;
    }
    if (!strcmp(name, "vkCmdDrawIndexed"))
    {
        return (void*)entry_vkCmdDrawIndexed;
    }
    if (!strcmp(name, "vkCmdDrawIndirect"))
    {
        return (void*)entry_vkCmdDrawIndirect;
    }
    if (!strcmp(name, "vkCmdDrawIndexedIndirect"))
    {
        return (void*)entry_vkCmdDrawIndexedIndirect;
    }
    if (!strcmp(name, "vkCmdDispatch"))
    {
        return (void*)entry_vkCmdDispatch;
    }
    if (!strcmp(name, "vkCmdDispatchIndirect"))
    {
        return (void*)entry_vkCmdDispatchIndirect;
    }
    if (!strcmp(name, "vkCmdCopyBuffer"))
    {
        return (void*)entry_vkCmdCopyBuffer;
    }
    if (!strcmp(name, "vkCmdCopyImage"))
    {
        return (void*)entry_vkCmdCopyImage;
    }
    if (!strcmp(name, "vkCmdBlitImage"))
    {
        return (void*)entry_vkCmdBlitImage;
    }
    if (!strcmp(name, "vkCmdCopyBufferToImage"))
    {
        return (void*)entry_vkCmdCopyBufferToImage;
    }
    if (!strcmp(name, "vkCmdCopyImageToBuffer"))
    {
        return (void*)entry_vkCmdCopyImageToBuffer;
    }
    if (!strcmp(name, "vkCmdUpdateBuffer"))
    {
        return (void*)entry_vkCmdUpdateBuffer;
    }
    if (!strcmp(name, "vkCmdFillBuffer"))
    {
        return (void*)entry_vkCmdFillBuffer;
    }
    if (!strcmp(name, "vkCmdClearColorImage"))
    {
        return (void*)entry_vkCmdClearColorImage;
    }
    if (!strcmp(name, "vkCmdClearDepthStencilImage"))
    {
        return (void*)entry_vkCmdClearDepthStencilImage;
    }
    if (!strcmp(name, "vkCmdClearAttachments"))
    {
        return (void*)entry_vkCmdClearAttachments;
    }
    if (!strcmp(name, "vkCmdResolveImage"))
    {
        return (void*)entry_vkCmdResolveImage;
    }
    if (!strcmp(name, "vkCmdSetEvent"))
    {
        return (void*)entry_vkCmdSetEvent;
    }
    if (!strcmp(name, "vkCmdResetEvent"))
    {
        return (void*)entry_vkCmdResetEvent;
    }
    if (!strcmp(name, "vkCmdWaitEvents"))
    {
        return (void*)entry_vkCmdWaitEvents;
    }
    if (!strcmp(name, "vkCmdPipelineBarrier"))
    {
        return (void*)entry_vkCmdPipelineBarrier;
    }
    if (!strcmp(name, "vkCmdBeginQuery"))
    {
        return (void*)entry_vkCmdBeginQuery;
    }
    if (!strcmp(name, "vkCmdEndQuery"))
    {
        return (void*)entry_vkCmdEndQuery;
    }
    if (!strcmp(name, "vkCmdResetQueryPool"))
    {
        return (void*)entry_vkCmdResetQueryPool;
    }
    if (!strcmp(name, "vkCmdWriteTimestamp"))
    {
        return (void*)entry_vkCmdWriteTimestamp;
    }
    if (!strcmp(name, "vkCmdCopyQueryPoolResults"))
    {
        return (void*)entry_vkCmdCopyQueryPoolResults;
    }
    if (!strcmp(name, "vkCmdPushConstants"))
    {
        return (void*)entry_vkCmdPushConstants;
    }
    if (!strcmp(name, "vkCmdBeginRenderPass"))
    {
        return (void*)entry_vkCmdBeginRenderPass;
    }
    if (!strcmp(name, "vkCmdNextSubpass"))
    {
        return (void*)entry_vkCmdNextSubpass;
    }
    if (!strcmp(name, "vkCmdEndRenderPass"))
    {
        return (void*)entry_vkCmdEndRenderPass;
    }
    if (!strcmp(name, "vkCmdExecuteCommands"))
    {
        return (void*)entry_vkCmdExecuteCommands;
    }
#endif
#ifdef VK_VERSION_1_1
    if (!strcmp(name, "vkEnumerateInstanceVersion"))
    {
        return has1_1OrHigher ? (void*)entry_vkEnumerateInstanceVersion : nullptr;
    }
    if (!strcmp(name, "vkBindBufferMemory2"))
    {
        return has1_1OrHigher ? (void*)entry_vkBindBufferMemory2 : nullptr;
    }
    if (!strcmp(name, "vkBindImageMemory2"))
    {
        return has1_1OrHigher ? (void*)entry_vkBindImageMemory2 : nullptr;
    }
    if (!strcmp(name, "vkGetDeviceGroupPeerMemoryFeatures"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetDeviceGroupPeerMemoryFeatures : nullptr;
    }
    if (!strcmp(name, "vkCmdSetDeviceMask"))
    {
        return has1_1OrHigher ? (void*)entry_vkCmdSetDeviceMask : nullptr;
    }
    if (!strcmp(name, "vkCmdDispatchBase"))
    {
        return has1_1OrHigher ? (void*)entry_vkCmdDispatchBase : nullptr;
    }
    if (!strcmp(name, "vkEnumeratePhysicalDeviceGroups"))
    {
        return nullptr;
    }
    if (!strcmp(name, "vkGetImageMemoryRequirements2"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetImageMemoryRequirements2 : nullptr;
    }
    if (!strcmp(name, "vkGetBufferMemoryRequirements2"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetBufferMemoryRequirements2 : nullptr;
    }
    if (!strcmp(name, "vkGetImageSparseMemoryRequirements2"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetImageSparseMemoryRequirements2 : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceFeatures2"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetPhysicalDeviceFeatures2 : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceProperties2"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetPhysicalDeviceProperties2 : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceFormatProperties2"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetPhysicalDeviceFormatProperties2 : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceImageFormatProperties2"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetPhysicalDeviceImageFormatProperties2 : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceQueueFamilyProperties2"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetPhysicalDeviceQueueFamilyProperties2 : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceMemoryProperties2"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetPhysicalDeviceMemoryProperties2 : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSparseImageFormatProperties2"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetPhysicalDeviceSparseImageFormatProperties2 : nullptr;
    }
    if (!strcmp(name, "vkTrimCommandPool"))
    {
        return has1_1OrHigher ? (void*)entry_vkTrimCommandPool : nullptr;
    }
    if (!strcmp(name, "vkGetDeviceQueue2"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetDeviceQueue2 : nullptr;
    }
    if (!strcmp(name, "vkCreateSamplerYcbcrConversion"))
    {
        return has1_1OrHigher ? (void*)entry_vkCreateSamplerYcbcrConversion : nullptr;
    }
    if (!strcmp(name, "vkDestroySamplerYcbcrConversion"))
    {
        return has1_1OrHigher ? (void*)entry_vkDestroySamplerYcbcrConversion : nullptr;
    }
    if (!strcmp(name, "vkCreateDescriptorUpdateTemplate"))
    {
        return has1_1OrHigher ? (void*)entry_vkCreateDescriptorUpdateTemplate : nullptr;
    }
    if (!strcmp(name, "vkDestroyDescriptorUpdateTemplate"))
    {
        return has1_1OrHigher ? (void*)entry_vkDestroyDescriptorUpdateTemplate : nullptr;
    }
    if (!strcmp(name, "vkUpdateDescriptorSetWithTemplate"))
    {
        return has1_1OrHigher ? (void*)entry_vkUpdateDescriptorSetWithTemplate : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceExternalBufferProperties"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetPhysicalDeviceExternalBufferProperties : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceExternalFenceProperties"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetPhysicalDeviceExternalFenceProperties : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceExternalSemaphoreProperties"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetPhysicalDeviceExternalSemaphoreProperties : nullptr;
    }
    if (!strcmp(name, "vkGetDescriptorSetLayoutSupport"))
    {
        return has1_1OrHigher ? (void*)entry_vkGetDescriptorSetLayoutSupport : nullptr;
    }
#endif
#ifdef VK_KHR_surface
    if (!strcmp(name, "vkDestroySurfaceKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_surface");
        return hasExt ? (void*)entry_vkDestroySurfaceKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSurfaceSupportKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_surface");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceSurfaceSupportKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSurfaceCapabilitiesKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_surface");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceSurfaceCapabilitiesKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSurfaceFormatsKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_surface");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceSurfaceFormatsKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSurfacePresentModesKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_surface");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceSurfacePresentModesKHR : nullptr;
    }
#endif
#ifdef VK_KHR_swapchain
    if (!strcmp(name, "vkCreateSwapchainKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_swapchain");
        return hasExt ? (void*)entry_vkCreateSwapchainKHR : nullptr;
    }
    if (!strcmp(name, "vkDestroySwapchainKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_swapchain");
        return hasExt ? (void*)entry_vkDestroySwapchainKHR : nullptr;
    }
    if (!strcmp(name, "vkGetSwapchainImagesKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_swapchain");
        return hasExt ? (void*)entry_vkGetSwapchainImagesKHR : nullptr;
    }
    if (!strcmp(name, "vkAcquireNextImageKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_swapchain");
        return hasExt ? (void*)entry_vkAcquireNextImageKHR : nullptr;
    }
    if (!strcmp(name, "vkQueuePresentKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_swapchain");
        return hasExt ? (void*)entry_vkQueuePresentKHR : nullptr;
    }
    if (!strcmp(name, "vkGetDeviceGroupPresentCapabilitiesKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_swapchain");
        return hasExt ? (void*)entry_vkGetDeviceGroupPresentCapabilitiesKHR : nullptr;
    }
    if (!strcmp(name, "vkGetDeviceGroupSurfacePresentModesKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_swapchain");
        return hasExt ? (void*)entry_vkGetDeviceGroupSurfacePresentModesKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDevicePresentRectanglesKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_swapchain");
        return hasExt ? (void*)entry_vkGetPhysicalDevicePresentRectanglesKHR : nullptr;
    }
    if (!strcmp(name, "vkAcquireNextImage2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_swapchain");
        return hasExt ? (void*)entry_vkAcquireNextImage2KHR : nullptr;
    }
#endif
#ifdef VK_KHR_display
    if (!strcmp(name, "vkGetPhysicalDeviceDisplayPropertiesKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_display");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceDisplayPropertiesKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceDisplayPlanePropertiesKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_display");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceDisplayPlanePropertiesKHR : nullptr;
    }
    if (!strcmp(name, "vkGetDisplayPlaneSupportedDisplaysKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_display");
        return hasExt ? (void*)entry_vkGetDisplayPlaneSupportedDisplaysKHR : nullptr;
    }
    if (!strcmp(name, "vkGetDisplayModePropertiesKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_display");
        return hasExt ? (void*)entry_vkGetDisplayModePropertiesKHR : nullptr;
    }
    if (!strcmp(name, "vkCreateDisplayModeKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_display");
        return hasExt ? (void*)entry_vkCreateDisplayModeKHR : nullptr;
    }
    if (!strcmp(name, "vkGetDisplayPlaneCapabilitiesKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_display");
        return hasExt ? (void*)entry_vkGetDisplayPlaneCapabilitiesKHR : nullptr;
    }
    if (!strcmp(name, "vkCreateDisplayPlaneSurfaceKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_display");
        return hasExt ? (void*)entry_vkCreateDisplayPlaneSurfaceKHR : nullptr;
    }
#endif
#ifdef VK_KHR_display_swapchain
    if (!strcmp(name, "vkCreateSharedSwapchainsKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_display_swapchain");
        return hasExt ? (void*)entry_vkCreateSharedSwapchainsKHR : nullptr;
    }
#endif
#ifdef VK_KHR_xlib_surface
    if (!strcmp(name, "vkCreateXlibSurfaceKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_xlib_surface");
        return hasExt ? (void*)entry_vkCreateXlibSurfaceKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceXlibPresentationSupportKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_xlib_surface");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceXlibPresentationSupportKHR : nullptr;
    }
#endif
#ifdef VK_KHR_xcb_surface
    if (!strcmp(name, "vkCreateXcbSurfaceKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_xcb_surface");
        return hasExt ? (void*)entry_vkCreateXcbSurfaceKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceXcbPresentationSupportKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_xcb_surface");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceXcbPresentationSupportKHR : nullptr;
    }
#endif
#ifdef VK_KHR_wayland_surface
    if (!strcmp(name, "vkCreateWaylandSurfaceKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_wayland_surface");
        return hasExt ? (void*)entry_vkCreateWaylandSurfaceKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceWaylandPresentationSupportKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_wayland_surface");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceWaylandPresentationSupportKHR : nullptr;
    }
#endif
#ifdef VK_KHR_mir_surface
    if (!strcmp(name, "vkCreateMirSurfaceKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_mir_surface");
        return hasExt ? (void*)entry_vkCreateMirSurfaceKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceMirPresentationSupportKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_mir_surface");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceMirPresentationSupportKHR : nullptr;
    }
#endif
#ifdef VK_KHR_android_surface
    if (!strcmp(name, "vkCreateAndroidSurfaceKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_android_surface");
        return hasExt ? (void*)entry_vkCreateAndroidSurfaceKHR : nullptr;
    }
#endif
#ifdef VK_KHR_win32_surface
    if (!strcmp(name, "vkCreateWin32SurfaceKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_win32_surface");
        return hasExt ? (void*)entry_vkCreateWin32SurfaceKHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceWin32PresentationSupportKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_win32_surface");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceWin32PresentationSupportKHR : nullptr;
    }
#endif
#ifdef VK_KHR_get_physical_device_properties2
    if (!strcmp(name, "vkGetPhysicalDeviceFeatures2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_get_physical_device_properties2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceFeatures2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceProperties2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_get_physical_device_properties2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceProperties2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceFormatProperties2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_get_physical_device_properties2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceFormatProperties2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceImageFormatProperties2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_get_physical_device_properties2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceImageFormatProperties2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceQueueFamilyProperties2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_get_physical_device_properties2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceQueueFamilyProperties2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceMemoryProperties2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_get_physical_device_properties2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceMemoryProperties2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSparseImageFormatProperties2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_get_physical_device_properties2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceSparseImageFormatProperties2KHR : nullptr;
    }
#endif
#ifdef VK_KHR_device_group
    if (!strcmp(name, "vkGetDeviceGroupPeerMemoryFeaturesKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_device_group");
        return hasExt ? (void*)entry_vkGetDeviceGroupPeerMemoryFeaturesKHR : nullptr;
    }
    if (!strcmp(name, "vkCmdSetDeviceMaskKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_device_group");
        return hasExt ? (void*)entry_vkCmdSetDeviceMaskKHR : nullptr;
    }
    if (!strcmp(name, "vkCmdDispatchBaseKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_device_group");
        return hasExt ? (void*)entry_vkCmdDispatchBaseKHR : nullptr;
    }
#endif
#ifdef VK_KHR_maintenance1
    if (!strcmp(name, "vkTrimCommandPoolKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_maintenance1");
        return hasExt ? (void*)entry_vkTrimCommandPoolKHR : nullptr;
    }
#endif
#ifdef VK_KHR_device_group_creation
    if (!strcmp(name, "vkEnumeratePhysicalDeviceGroupsKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_device_group_creation");
        return hasExt ? (void*)entry_vkEnumeratePhysicalDeviceGroupsKHR : nullptr;
    }
#endif
#ifdef VK_KHR_external_memory_capabilities
    if (!strcmp(name, "vkGetPhysicalDeviceExternalBufferPropertiesKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_external_memory_capabilities");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceExternalBufferPropertiesKHR : nullptr;
    }
#endif
#ifdef VK_KHR_external_memory_win32
    if (!strcmp(name, "vkGetMemoryWin32HandleKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_external_memory_win32");
        return hasExt ? (void*)entry_vkGetMemoryWin32HandleKHR : nullptr;
    }
    if (!strcmp(name, "vkGetMemoryWin32HandlePropertiesKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_external_memory_win32");
        return hasExt ? (void*)entry_vkGetMemoryWin32HandlePropertiesKHR : nullptr;
    }
#endif
#ifdef VK_KHR_external_memory_fd
    if (!strcmp(name, "vkGetMemoryFdKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_external_memory_fd");
        return hasExt ? (void*)entry_vkGetMemoryFdKHR : nullptr;
    }
    if (!strcmp(name, "vkGetMemoryFdPropertiesKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_external_memory_fd");
        return hasExt ? (void*)entry_vkGetMemoryFdPropertiesKHR : nullptr;
    }
#endif
#ifdef VK_KHR_external_semaphore_capabilities
    if (!strcmp(name, "vkGetPhysicalDeviceExternalSemaphorePropertiesKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_external_semaphore_capabilities");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceExternalSemaphorePropertiesKHR : nullptr;
    }
#endif
#ifdef VK_KHR_external_semaphore_win32
    if (!strcmp(name, "vkImportSemaphoreWin32HandleKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_external_semaphore_win32");
        return hasExt ? (void*)entry_vkImportSemaphoreWin32HandleKHR : nullptr;
    }
    if (!strcmp(name, "vkGetSemaphoreWin32HandleKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_external_semaphore_win32");
        return hasExt ? (void*)entry_vkGetSemaphoreWin32HandleKHR : nullptr;
    }
#endif
#ifdef VK_KHR_external_semaphore_fd
    if (!strcmp(name, "vkImportSemaphoreFdKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_external_semaphore_fd");
        return hasExt ? (void*)entry_vkImportSemaphoreFdKHR : nullptr;
    }
    if (!strcmp(name, "vkGetSemaphoreFdKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_external_semaphore_fd");
        return hasExt ? (void*)entry_vkGetSemaphoreFdKHR : nullptr;
    }
#endif
#ifdef VK_KHR_push_descriptor
    if (!strcmp(name, "vkCmdPushDescriptorSetKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_push_descriptor");
        return hasExt ? (void*)entry_vkCmdPushDescriptorSetKHR : nullptr;
    }
    if (!strcmp(name, "vkCmdPushDescriptorSetWithTemplateKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_push_descriptor");
        return hasExt ? (void*)entry_vkCmdPushDescriptorSetWithTemplateKHR : nullptr;
    }
#endif
#ifdef VK_KHR_descriptor_update_template
    if (!strcmp(name, "vkCreateDescriptorUpdateTemplateKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_descriptor_update_template");
        return hasExt ? (void*)entry_vkCreateDescriptorUpdateTemplateKHR : nullptr;
    }
    if (!strcmp(name, "vkDestroyDescriptorUpdateTemplateKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_descriptor_update_template");
        return hasExt ? (void*)entry_vkDestroyDescriptorUpdateTemplateKHR : nullptr;
    }
    if (!strcmp(name, "vkUpdateDescriptorSetWithTemplateKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_descriptor_update_template");
        return hasExt ? (void*)entry_vkUpdateDescriptorSetWithTemplateKHR : nullptr;
    }
#endif
#ifdef VK_KHR_create_renderpass2
    if (!strcmp(name, "vkCreateRenderPass2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_create_renderpass2");
        return hasExt ? (void*)entry_vkCreateRenderPass2KHR : nullptr;
    }
    if (!strcmp(name, "vkCmdBeginRenderPass2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_create_renderpass2");
        return hasExt ? (void*)entry_vkCmdBeginRenderPass2KHR : nullptr;
    }
    if (!strcmp(name, "vkCmdNextSubpass2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_create_renderpass2");
        return hasExt ? (void*)entry_vkCmdNextSubpass2KHR : nullptr;
    }
    if (!strcmp(name, "vkCmdEndRenderPass2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_create_renderpass2");
        return hasExt ? (void*)entry_vkCmdEndRenderPass2KHR : nullptr;
    }
#endif
#ifdef VK_KHR_shared_presentable_image
    if (!strcmp(name, "vkGetSwapchainStatusKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_shared_presentable_image");
        return hasExt ? (void*)entry_vkGetSwapchainStatusKHR : nullptr;
    }
#endif
#ifdef VK_KHR_external_fence_capabilities
    if (!strcmp(name, "vkGetPhysicalDeviceExternalFencePropertiesKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_external_fence_capabilities");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceExternalFencePropertiesKHR : nullptr;
    }
#endif
#ifdef VK_KHR_external_fence_win32
    if (!strcmp(name, "vkImportFenceWin32HandleKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_external_fence_win32");
        return hasExt ? (void*)entry_vkImportFenceWin32HandleKHR : nullptr;
    }
    if (!strcmp(name, "vkGetFenceWin32HandleKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_external_fence_win32");
        return hasExt ? (void*)entry_vkGetFenceWin32HandleKHR : nullptr;
    }
#endif
#ifdef VK_KHR_external_fence_fd
    if (!strcmp(name, "vkImportFenceFdKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_external_fence_fd");
        return hasExt ? (void*)entry_vkImportFenceFdKHR : nullptr;
    }
    if (!strcmp(name, "vkGetFenceFdKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_external_fence_fd");
        return hasExt ? (void*)entry_vkGetFenceFdKHR : nullptr;
    }
#endif
#ifdef VK_KHR_get_surface_capabilities2
    if (!strcmp(name, "vkGetPhysicalDeviceSurfaceCapabilities2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_get_surface_capabilities2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceSurfaceCapabilities2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceSurfaceFormats2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_get_surface_capabilities2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceSurfaceFormats2KHR : nullptr;
    }
#endif
#ifdef VK_KHR_get_display_properties2
    if (!strcmp(name, "vkGetPhysicalDeviceDisplayProperties2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_get_display_properties2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceDisplayProperties2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceDisplayPlaneProperties2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_get_display_properties2");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceDisplayPlaneProperties2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetDisplayModeProperties2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_get_display_properties2");
        return hasExt ? (void*)entry_vkGetDisplayModeProperties2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetDisplayPlaneCapabilities2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_get_display_properties2");
        return hasExt ? (void*)entry_vkGetDisplayPlaneCapabilities2KHR : nullptr;
    }
#endif
#ifdef VK_KHR_get_memory_requirements2
    if (!strcmp(name, "vkGetImageMemoryRequirements2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_get_memory_requirements2");
        return hasExt ? (void*)entry_vkGetImageMemoryRequirements2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetBufferMemoryRequirements2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_get_memory_requirements2");
        return hasExt ? (void*)entry_vkGetBufferMemoryRequirements2KHR : nullptr;
    }
    if (!strcmp(name, "vkGetImageSparseMemoryRequirements2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_get_memory_requirements2");
        return hasExt ? (void*)entry_vkGetImageSparseMemoryRequirements2KHR : nullptr;
    }
#endif
#ifdef VK_KHR_sampler_ycbcr_conversion
    if (!strcmp(name, "vkCreateSamplerYcbcrConversionKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_sampler_ycbcr_conversion");
        return hasExt ? (void*)entry_vkCreateSamplerYcbcrConversionKHR : nullptr;
    }
    if (!strcmp(name, "vkDestroySamplerYcbcrConversionKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_sampler_ycbcr_conversion");
        return hasExt ? (void*)entry_vkDestroySamplerYcbcrConversionKHR : nullptr;
    }
#endif
#ifdef VK_KHR_bind_memory2
    if (!strcmp(name, "vkBindBufferMemory2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_bind_memory2");
        return hasExt ? (void*)entry_vkBindBufferMemory2KHR : nullptr;
    }
    if (!strcmp(name, "vkBindImageMemory2KHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_bind_memory2");
        return hasExt ? (void*)entry_vkBindImageMemory2KHR : nullptr;
    }
#endif
#ifdef VK_KHR_maintenance3
    if (!strcmp(name, "vkGetDescriptorSetLayoutSupportKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_maintenance3");
        return hasExt ? (void*)entry_vkGetDescriptorSetLayoutSupportKHR : nullptr;
    }
#endif
#ifdef VK_KHR_draw_indirect_count
    if (!strcmp(name, "vkCmdDrawIndirectCountKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_draw_indirect_count");
        return hasExt ? (void*)entry_vkCmdDrawIndirectCountKHR : nullptr;
    }
    if (!strcmp(name, "vkCmdDrawIndexedIndirectCountKHR"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_KHR_draw_indirect_count");
        return hasExt ? (void*)entry_vkCmdDrawIndexedIndirectCountKHR : nullptr;
    }
#endif
#ifdef VK_ANDROID_native_buffer
    if (!strcmp(name, "vkGetSwapchainGrallocUsageANDROID"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_ANDROID_native_buffer");
        return hasExt ? (void*)entry_vkGetSwapchainGrallocUsageANDROID : nullptr;
    }
    if (!strcmp(name, "vkAcquireImageANDROID"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_ANDROID_native_buffer");
        return hasExt ? (void*)entry_vkAcquireImageANDROID : nullptr;
    }
    if (!strcmp(name, "vkQueueSignalReleaseImageANDROID"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_ANDROID_native_buffer");
        return hasExt ? (void*)entry_vkQueueSignalReleaseImageANDROID : nullptr;
    }
#endif
#ifdef VK_EXT_debug_report
    if (!strcmp(name, "vkCreateDebugReportCallbackEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_debug_report");
        return hasExt ? (void*)entry_vkCreateDebugReportCallbackEXT : nullptr;
    }
    if (!strcmp(name, "vkDestroyDebugReportCallbackEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_debug_report");
        return hasExt ? (void*)entry_vkDestroyDebugReportCallbackEXT : nullptr;
    }
    if (!strcmp(name, "vkDebugReportMessageEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_debug_report");
        return hasExt ? (void*)entry_vkDebugReportMessageEXT : nullptr;
    }
#endif
#ifdef VK_EXT_debug_marker
    if (!strcmp(name, "vkDebugMarkerSetObjectTagEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_debug_marker");
        return hasExt ? (void*)entry_vkDebugMarkerSetObjectTagEXT : nullptr;
    }
    if (!strcmp(name, "vkDebugMarkerSetObjectNameEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_debug_marker");
        return hasExt ? (void*)entry_vkDebugMarkerSetObjectNameEXT : nullptr;
    }
    if (!strcmp(name, "vkCmdDebugMarkerBeginEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_debug_marker");
        return hasExt ? (void*)entry_vkCmdDebugMarkerBeginEXT : nullptr;
    }
    if (!strcmp(name, "vkCmdDebugMarkerEndEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_debug_marker");
        return hasExt ? (void*)entry_vkCmdDebugMarkerEndEXT : nullptr;
    }
    if (!strcmp(name, "vkCmdDebugMarkerInsertEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_debug_marker");
        return hasExt ? (void*)entry_vkCmdDebugMarkerInsertEXT : nullptr;
    }
#endif
#ifdef VK_AMD_draw_indirect_count
    if (!strcmp(name, "vkCmdDrawIndirectCountAMD"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_AMD_draw_indirect_count");
        return hasExt ? (void*)entry_vkCmdDrawIndirectCountAMD : nullptr;
    }
    if (!strcmp(name, "vkCmdDrawIndexedIndirectCountAMD"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_AMD_draw_indirect_count");
        return hasExt ? (void*)entry_vkCmdDrawIndexedIndirectCountAMD : nullptr;
    }
#endif
#ifdef VK_AMD_shader_info
    if (!strcmp(name, "vkGetShaderInfoAMD"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_AMD_shader_info");
        return hasExt ? (void*)entry_vkGetShaderInfoAMD : nullptr;
    }
#endif
#ifdef VK_NV_external_memory_capabilities
    if (!strcmp(name, "vkGetPhysicalDeviceExternalImageFormatPropertiesNV"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_NV_external_memory_capabilities");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceExternalImageFormatPropertiesNV : nullptr;
    }
#endif
#ifdef VK_NV_external_memory_win32
    if (!strcmp(name, "vkGetMemoryWin32HandleNV"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_NV_external_memory_win32");
        return hasExt ? (void*)entry_vkGetMemoryWin32HandleNV : nullptr;
    }
#endif
#ifdef VK_NN_vi_surface
    if (!strcmp(name, "vkCreateViSurfaceNN"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_NN_vi_surface");
        return hasExt ? (void*)entry_vkCreateViSurfaceNN : nullptr;
    }
#endif
#ifdef VK_EXT_conditional_rendering
    if (!strcmp(name, "vkCmdBeginConditionalRenderingEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_conditional_rendering");
        return hasExt ? (void*)entry_vkCmdBeginConditionalRenderingEXT : nullptr;
    }
    if (!strcmp(name, "vkCmdEndConditionalRenderingEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_conditional_rendering");
        return hasExt ? (void*)entry_vkCmdEndConditionalRenderingEXT : nullptr;
    }
#endif
#ifdef VK_NVX_device_generated_commands
    if (!strcmp(name, "vkCmdProcessCommandsNVX"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_NVX_device_generated_commands");
        return hasExt ? (void*)entry_vkCmdProcessCommandsNVX : nullptr;
    }
    if (!strcmp(name, "vkCmdReserveSpaceForCommandsNVX"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_NVX_device_generated_commands");
        return hasExt ? (void*)entry_vkCmdReserveSpaceForCommandsNVX : nullptr;
    }
    if (!strcmp(name, "vkCreateIndirectCommandsLayoutNVX"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_NVX_device_generated_commands");
        return hasExt ? (void*)entry_vkCreateIndirectCommandsLayoutNVX : nullptr;
    }
    if (!strcmp(name, "vkDestroyIndirectCommandsLayoutNVX"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_NVX_device_generated_commands");
        return hasExt ? (void*)entry_vkDestroyIndirectCommandsLayoutNVX : nullptr;
    }
    if (!strcmp(name, "vkCreateObjectTableNVX"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_NVX_device_generated_commands");
        return hasExt ? (void*)entry_vkCreateObjectTableNVX : nullptr;
    }
    if (!strcmp(name, "vkDestroyObjectTableNVX"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_NVX_device_generated_commands");
        return hasExt ? (void*)entry_vkDestroyObjectTableNVX : nullptr;
    }
    if (!strcmp(name, "vkRegisterObjectsNVX"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_NVX_device_generated_commands");
        return hasExt ? (void*)entry_vkRegisterObjectsNVX : nullptr;
    }
    if (!strcmp(name, "vkUnregisterObjectsNVX"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_NVX_device_generated_commands");
        return hasExt ? (void*)entry_vkUnregisterObjectsNVX : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceGeneratedCommandsPropertiesNVX"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_NVX_device_generated_commands");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceGeneratedCommandsPropertiesNVX : nullptr;
    }
#endif
#ifdef VK_NV_clip_space_w_scaling
    if (!strcmp(name, "vkCmdSetViewportWScalingNV"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_NV_clip_space_w_scaling");
        return hasExt ? (void*)entry_vkCmdSetViewportWScalingNV : nullptr;
    }
#endif
#ifdef VK_EXT_direct_mode_display
    if (!strcmp(name, "vkReleaseDisplayEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_direct_mode_display");
        return hasExt ? (void*)entry_vkReleaseDisplayEXT : nullptr;
    }
#endif
#ifdef VK_EXT_acquire_xlib_display
    if (!strcmp(name, "vkAcquireXlibDisplayEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_acquire_xlib_display");
        return hasExt ? (void*)entry_vkAcquireXlibDisplayEXT : nullptr;
    }
    if (!strcmp(name, "vkGetRandROutputDisplayEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_acquire_xlib_display");
        return hasExt ? (void*)entry_vkGetRandROutputDisplayEXT : nullptr;
    }
#endif
#ifdef VK_EXT_display_surface_counter
    if (!strcmp(name, "vkGetPhysicalDeviceSurfaceCapabilities2EXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_display_surface_counter");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceSurfaceCapabilities2EXT : nullptr;
    }
#endif
#ifdef VK_EXT_display_control
    if (!strcmp(name, "vkDisplayPowerControlEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_display_control");
        return hasExt ? (void*)entry_vkDisplayPowerControlEXT : nullptr;
    }
    if (!strcmp(name, "vkRegisterDeviceEventEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_display_control");
        return hasExt ? (void*)entry_vkRegisterDeviceEventEXT : nullptr;
    }
    if (!strcmp(name, "vkRegisterDisplayEventEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_display_control");
        return hasExt ? (void*)entry_vkRegisterDisplayEventEXT : nullptr;
    }
    if (!strcmp(name, "vkGetSwapchainCounterEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_display_control");
        return hasExt ? (void*)entry_vkGetSwapchainCounterEXT : nullptr;
    }
#endif
#ifdef VK_GOOGLE_display_timing
    if (!strcmp(name, "vkGetRefreshCycleDurationGOOGLE"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_GOOGLE_display_timing");
        return hasExt ? (void*)entry_vkGetRefreshCycleDurationGOOGLE : nullptr;
    }
    if (!strcmp(name, "vkGetPastPresentationTimingGOOGLE"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_GOOGLE_display_timing");
        return hasExt ? (void*)entry_vkGetPastPresentationTimingGOOGLE : nullptr;
    }
#endif
#ifdef VK_EXT_discard_rectangles
    if (!strcmp(name, "vkCmdSetDiscardRectangleEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_discard_rectangles");
        return hasExt ? (void*)entry_vkCmdSetDiscardRectangleEXT : nullptr;
    }
#endif
#ifdef VK_EXT_hdr_metadata
    if (!strcmp(name, "vkSetHdrMetadataEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_hdr_metadata");
        return hasExt ? (void*)entry_vkSetHdrMetadataEXT : nullptr;
    }
#endif
#ifdef VK_MVK_ios_surface
    if (!strcmp(name, "vkCreateIOSSurfaceMVK"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_MVK_ios_surface");
        return hasExt ? (void*)entry_vkCreateIOSSurfaceMVK : nullptr;
    }
#endif
#ifdef VK_MVK_macos_surface
    if (!strcmp(name, "vkCreateMacOSSurfaceMVK"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_MVK_macos_surface");
        return hasExt ? (void*)entry_vkCreateMacOSSurfaceMVK : nullptr;
    }
#endif
#ifdef VK_EXT_debug_utils
    if (!strcmp(name, "vkSetDebugUtilsObjectNameEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_debug_utils");
        return hasExt ? (void*)entry_vkSetDebugUtilsObjectNameEXT : nullptr;
    }
    if (!strcmp(name, "vkSetDebugUtilsObjectTagEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_debug_utils");
        return hasExt ? (void*)entry_vkSetDebugUtilsObjectTagEXT : nullptr;
    }
    if (!strcmp(name, "vkQueueBeginDebugUtilsLabelEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_debug_utils");
        return hasExt ? (void*)entry_vkQueueBeginDebugUtilsLabelEXT : nullptr;
    }
    if (!strcmp(name, "vkQueueEndDebugUtilsLabelEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_debug_utils");
        return hasExt ? (void*)entry_vkQueueEndDebugUtilsLabelEXT : nullptr;
    }
    if (!strcmp(name, "vkQueueInsertDebugUtilsLabelEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_debug_utils");
        return hasExt ? (void*)entry_vkQueueInsertDebugUtilsLabelEXT : nullptr;
    }
    if (!strcmp(name, "vkCmdBeginDebugUtilsLabelEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_debug_utils");
        return hasExt ? (void*)entry_vkCmdBeginDebugUtilsLabelEXT : nullptr;
    }
    if (!strcmp(name, "vkCmdEndDebugUtilsLabelEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_debug_utils");
        return hasExt ? (void*)entry_vkCmdEndDebugUtilsLabelEXT : nullptr;
    }
    if (!strcmp(name, "vkCmdInsertDebugUtilsLabelEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_debug_utils");
        return hasExt ? (void*)entry_vkCmdInsertDebugUtilsLabelEXT : nullptr;
    }
    if (!strcmp(name, "vkCreateDebugUtilsMessengerEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_debug_utils");
        return hasExt ? (void*)entry_vkCreateDebugUtilsMessengerEXT : nullptr;
    }
    if (!strcmp(name, "vkDestroyDebugUtilsMessengerEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_debug_utils");
        return hasExt ? (void*)entry_vkDestroyDebugUtilsMessengerEXT : nullptr;
    }
    if (!strcmp(name, "vkSubmitDebugUtilsMessageEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_debug_utils");
        return hasExt ? (void*)entry_vkSubmitDebugUtilsMessageEXT : nullptr;
    }
#endif
#ifdef VK_ANDROID_external_memory_android_hardware_buffer
    if (!strcmp(name, "vkGetAndroidHardwareBufferPropertiesANDROID"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_ANDROID_external_memory_android_hardware_buffer");
        return hasExt ? (void*)entry_vkGetAndroidHardwareBufferPropertiesANDROID : nullptr;
    }
    if (!strcmp(name, "vkGetMemoryAndroidHardwareBufferANDROID"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_ANDROID_external_memory_android_hardware_buffer");
        return hasExt ? (void*)entry_vkGetMemoryAndroidHardwareBufferANDROID : nullptr;
    }
#endif
#ifdef VK_EXT_sample_locations
    if (!strcmp(name, "vkCmdSetSampleLocationsEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_sample_locations");
        return hasExt ? (void*)entry_vkCmdSetSampleLocationsEXT : nullptr;
    }
    if (!strcmp(name, "vkGetPhysicalDeviceMultisamplePropertiesEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_sample_locations");
        return hasExt ? (void*)entry_vkGetPhysicalDeviceMultisamplePropertiesEXT : nullptr;
    }
#endif
#ifdef VK_EXT_validation_cache
    if (!strcmp(name, "vkCreateValidationCacheEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_validation_cache");
        return hasExt ? (void*)entry_vkCreateValidationCacheEXT : nullptr;
    }
    if (!strcmp(name, "vkDestroyValidationCacheEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_validation_cache");
        return hasExt ? (void*)entry_vkDestroyValidationCacheEXT : nullptr;
    }
    if (!strcmp(name, "vkMergeValidationCachesEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_validation_cache");
        return hasExt ? (void*)entry_vkMergeValidationCachesEXT : nullptr;
    }
    if (!strcmp(name, "vkGetValidationCacheDataEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_validation_cache");
        return hasExt ? (void*)entry_vkGetValidationCacheDataEXT : nullptr;
    }
#endif
#ifdef VK_EXT_external_memory_host
    if (!strcmp(name, "vkGetMemoryHostPointerPropertiesEXT"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_EXT_external_memory_host");
        return hasExt ? (void*)entry_vkGetMemoryHostPointerPropertiesEXT : nullptr;
    }
#endif
#ifdef VK_AMD_buffer_marker
    if (!strcmp(name, "vkCmdWriteBufferMarkerAMD"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_AMD_buffer_marker");
        return hasExt ? (void*)entry_vkCmdWriteBufferMarkerAMD : nullptr;
    }
#endif
#ifdef VK_NV_device_diagnostic_checkpoints
    if (!strcmp(name, "vkCmdSetCheckpointNV"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_NV_device_diagnostic_checkpoints");
        return hasExt ? (void*)entry_vkCmdSetCheckpointNV : nullptr;
    }
    if (!strcmp(name, "vkGetQueueCheckpointDataNV"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_NV_device_diagnostic_checkpoints");
        return hasExt ? (void*)entry_vkGetQueueCheckpointDataNV : nullptr;
    }
#endif
#ifdef VK_GOOGLE_address_space
    if (!strcmp(name, "vkMapMemoryIntoAddressSpaceGOOGLE"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_GOOGLE_address_space");
        return hasExt ? (void*)entry_vkMapMemoryIntoAddressSpaceGOOGLE : nullptr;
    }
#endif
#ifdef VK_GOOGLE_color_buffer
    if (!strcmp(name, "vkRegisterImageColorBufferGOOGLE"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_GOOGLE_color_buffer");
        return hasExt ? (void*)entry_vkRegisterImageColorBufferGOOGLE : nullptr;
    }
    if (!strcmp(name, "vkRegisterBufferColorBufferGOOGLE"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_GOOGLE_color_buffer");
        return hasExt ? (void*)entry_vkRegisterBufferColorBufferGOOGLE : nullptr;
    }
#endif
#ifdef VK_GOOGLE_sized_descriptor_update_template
    if (!strcmp(name, "vkUpdateDescriptorSetWithTemplateSizedGOOGLE"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_GOOGLE_sized_descriptor_update_template");
        return hasExt ? (void*)entry_vkUpdateDescriptorSetWithTemplateSizedGOOGLE : nullptr;
    }
#endif
#ifdef VK_GOOGLE_async_command_buffers
    if (!strcmp(name, "vkBeginCommandBufferAsyncGOOGLE"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_GOOGLE_async_command_buffers");
        return hasExt ? (void*)entry_vkBeginCommandBufferAsyncGOOGLE : nullptr;
    }
    if (!strcmp(name, "vkEndCommandBufferAsyncGOOGLE"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_GOOGLE_async_command_buffers");
        return hasExt ? (void*)entry_vkEndCommandBufferAsyncGOOGLE : nullptr;
    }
    if (!strcmp(name, "vkResetCommandBufferAsyncGOOGLE"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_GOOGLE_async_command_buffers");
        return hasExt ? (void*)entry_vkResetCommandBufferAsyncGOOGLE : nullptr;
    }
    if (!strcmp(name, "vkCommandBufferHostSyncGOOGLE"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_GOOGLE_async_command_buffers");
        return hasExt ? (void*)entry_vkCommandBufferHostSyncGOOGLE : nullptr;
    }
#endif
#ifdef VK_GOOGLE_create_resources_with_requirements
    if (!strcmp(name, "vkCreateImageWithRequirementsGOOGLE"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_GOOGLE_create_resources_with_requirements");
        return hasExt ? (void*)entry_vkCreateImageWithRequirementsGOOGLE : nullptr;
    }
    if (!strcmp(name, "vkCreateBufferWithRequirementsGOOGLE"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_GOOGLE_create_resources_with_requirements");
        return hasExt ? (void*)entry_vkCreateBufferWithRequirementsGOOGLE : nullptr;
    }
#endif
#ifdef VK_GOOGLE_address_space_info
    if (!strcmp(name, "vkGetMemoryHostAddressInfoGOOGLE"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_GOOGLE_address_space_info");
        return hasExt ? (void*)entry_vkGetMemoryHostAddressInfoGOOGLE : nullptr;
    }
#endif
#ifdef VK_GOOGLE_free_memory_sync
    if (!strcmp(name, "vkFreeMemorySyncGOOGLE"))
    {
        bool hasExt = resources->hasDeviceExtension(device, "VK_GOOGLE_free_memory_sync");
        return hasExt ? (void*)entry_vkFreeMemorySyncGOOGLE : nullptr;
    }
#endif
    return nullptr;
}

} // namespace goldfish_vk

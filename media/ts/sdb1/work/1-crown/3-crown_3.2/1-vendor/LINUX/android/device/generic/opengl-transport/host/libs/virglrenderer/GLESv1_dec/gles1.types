GLbitfield 32 0x%08x
GLboolean 8 %d
GLclampf 32 %f
GLclampx 32 0x%08x
GLeglImageOES 32 %p
GLenum 32 0x%08x
GLfixed 32 0x%08x
GLfloat 32 %f
GLint 32 %d
GLintptr 32 0x%08lx
GLshort 16 %d
GLsizei 32 %d
GLsizeiptr 32 0x%08lx
GLubyte 8 0x%02x
GLuint 32 %u
GLvoid 0 %x
GLchar 8 %d
GLenum* 32 0x%08x
GLboolean* 32 0x%08x
GLclampf* 32 0x%08x
GLclampx* 32 0x%08x
GLeglImageOES* 32 0x%08x
GLfixed* 32 0x%08x
GLfloat* 32 0x%08x
GLint* 32 0x%08x
GLshort* 32 0x%08x
GLsizei* 32 0x%08x
GLubyte* 32 0x%08x
GLuint* 32 0x%08x
GLvoid* 32 0x%08x
GLchar* 32 0x%08x
GLvoid** 32 0x%08x
void* 32 0x%08x
GLvoid*const* 32 0x%08x

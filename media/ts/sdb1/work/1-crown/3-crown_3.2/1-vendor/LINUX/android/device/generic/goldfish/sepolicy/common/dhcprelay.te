# DHCP relay
type dhcprelay, domain;
type dhcprelay_exec, exec_type, vendor_file_type, file_type;

init_daemon_domain(dhcprelay)
net_domain(dhcprelay)

allow dhcprelay execns:fd use;

set_prop(dhcprelay, net_wlan0_prop);
set_prop(dhcprelay, net_eth0_prop);
dontaudit dhcprelay kernel:system module_request;
allow dhcprelay self:capability { net_admin net_bind_service net_raw };
allow dhcprelay self:udp_socket create;
allow dhcprelay self:netlink_route_socket { write nlmsg_write };
allow dhcprelay varrun_file:dir search;
allow dhcprelay self:packet_socket { create bind write read };
allowxperm dhcprelay self:udp_socket ioctl { SIOCSIFFLAGS
                                             SIOCSIFADDR
                                             SIOCSIFNETMASK
                                             SIOCSIFMTU
                                             SIOCGIFHWADDR };

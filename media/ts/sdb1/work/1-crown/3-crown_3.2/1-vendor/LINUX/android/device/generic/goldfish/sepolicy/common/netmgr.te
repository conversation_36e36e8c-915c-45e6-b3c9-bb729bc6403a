# Wifi manager
type netmgr, domain;
type netmgr_exec, exec_type, vendor_file_type, file_type;

init_daemon_domain(netmgr)
net_domain(netmgr)

allow netmgr execns:fd use;

# Set property to indicate bridging is complete
set_prop(netmgr, vendor_net);
# Set ctrl.restart property to restart hostapd when config changes
set_prop(netmgr, ctl_default_prop);
# Modify hostapd config file
allow netmgr hostapd_data_file:file create_file_perms;
allow netmgr hostapd_data_file:dir rw_dir_perms;
# Assign addresses to new interfaces as hostapd brings them up
allow netmgr self:capability { net_raw net_admin };
allow netmgr self:socket { create };
allow netmgr self:unix_dgram_socket ioctl;
allow netmgr self:packet_socket { ioctl getopt map };
allow netmgr self:udp_socket { ioctl };
allow netmgr proc_net:file { read getattr open };
allowxperm netmgr self:unix_dgram_socket ioctl { SIOCETHTOOL };
allowxperm netmgr self:udp_socket ioctl { SIOCSIFFLAGS
                                          SIOCBRADDBR
                                          SIOCBRADDIF
                                          SIOCBRDELIF };
allowxperm netmgr self:packet_socket ioctl { SIOCGIFINDEX SIOCGIFHWADDR };

# Allow netmgr to run ip and modify route table to block unblock traffic
allow netmgr goldfish_ip_exec:file execute_no_trans;
allow netmgr self:netlink_route_socket nlmsg_write;
# Packet socket for wifi forwarding
allow netmgr self:packet_socket { bind create read setopt write };
allow netmgr kernel:system module_request;
allow netmgr self:capability sys_module;

// Copyright (C) 2020 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

cc_binary {
  name: "device.google.atv.audio_proxy@5.0-service",
  vendor: true,
  relative_install_path: "hw",

  srcs: [
    "AudioProxyDevicesManagerImpl.cpp",
    "BusDeviceProvider.cpp",
    "DeviceImpl.cpp",
    "DevicesFactoryImpl.cpp",
    "main.cpp",
  ],

  init_rc: [
    "<EMAIL>",
  ],

  vintf_fragments: [ "manifest_audio_proxy.xml" ],

  shared_libs: [
    "android.hardware.audio@5.0",
    "android.hardware.audio.common@5.0",
    "android.hardware.audio.common@5.0-util",
    "libhidlbase",
    "liblog",
    "libutils",
    "device.google.atv.audio_proxy@5.0",
  ],

  header_libs: [
    "android.hardware.audio.common.util@all-versions",
  ],

  cflags: [
    "-DMAJOR_VERSION=5",
    "-DMINOR_VERSION=0",
    "-include common/all-versions/VersionMacro.h",
  ],
}

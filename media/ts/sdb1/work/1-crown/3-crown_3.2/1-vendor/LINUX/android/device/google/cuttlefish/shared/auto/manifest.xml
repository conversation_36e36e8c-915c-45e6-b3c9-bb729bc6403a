<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Copyright 2018, The Android Open Source Project.
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** limitations under the License.
*/
-->
<!-- Android Auto Embedded specific HALs-->
<manifest version="1.0" type="device" target-level="5">
    <!-- FIXME: Implement automotive.evs HAL
    <hal format="hidl">
        <name>android.hardware.automotive.evs</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IEvsEnumerator</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
    <hal format="hidl">
        <name>android.hardware.automotive.can</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>ICanController</name>
            <instance>socketcan</instance>
        </interface>
        <interface>
            <name>ICanBus</name>
            <instance>test</instance>
        </interface>
    </hal>
    <!-- FIXME: Move this to shared manifest.xml -->
    <hal format="hidl">
        <name>android.hardware.broadcastradio</name>
        <transport>hwbinder</transport>
        <version>2.0</version>
        <interface>
            <name>IBroadcastRadio</name>
            <instance>amfm</instance>
            <instance>dab</instance>
        </interface>
    </hal>
</manifest>

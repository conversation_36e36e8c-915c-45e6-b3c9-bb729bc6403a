//
// Copyright (C) 2019 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

cc_library_static {
    name: "libwebrtc",
    srcs: [
        "AdbWebSocketHandler.cpp",
        "DTLS.cpp",
        "G711Packetizer.cpp",
        "Keyboard.cpp",
        "MyWebSocketHandler.cpp",
        "OpusPacketizer.cpp",
        "Packetizer.cpp",
        "RTPSender.cpp",
        "RTPSession.cpp",
        "RTPSocketHandler.cpp",
        "SCTPHandler.cpp",
        "SDP.cpp",
        "ServerState.cpp",
        "STUNClient.cpp",
        "STUNMessage.cpp",
        "Utils.cpp",
        "VP8Packetizer.cpp",
    ],
    static_libs: [
        "libhttps",
        "libsrtp2",
        "libcuttlefish_host_config",
        "libcuttlefish_screen_connector",
        "libcuttlefish_wayland_server",
        "libgflags",
        "libjsoncpp",
        "libsource",
        "libdrm",
        "libffi",
        "libwayland_server",
        "libwayland_extension_server_protocols",
    ],
    shared_libs: [
        "libssl",
        "libbase",
        "libcuttlefish_fs",
    ],
    defaults: ["cuttlefish_host_only"],
    local_include_dirs: ["include"],
    export_include_dirs: ["include"],
}

cc_binary_host {
    name: "webRTC",
    srcs: [
        "webRTC.cpp",
    ],
    shared_libs: [
        "libbase",
        "libcrypto",
        "libcuttlefish_fs",
        "libcuttlefish_utils",
        "libopus",
        "libssl",
        "libvpx",
        "libyuv",
    ],
    static_libs: [
        "libcuttlefish_host_config",
        "libcuttlefish_screen_connector",
        "libcuttlefish_wayland_server",
        "libgflags",
        "libhttps",
        "libjsoncpp",
        "libsource",
        "libsrtp2",
        "libwebrtc",
        "libdrm",
        "libffi",
        "libwayland_server",
        "libwayland_extension_server_protocols",
    ],
    cpp_std: "experimental",
    defaults: ["cuttlefish_host_only"],
}

// TODO(jemoreira): Ideally these files should be in $HOST_OUT/webrtc but I
// couldn't find a module type that would produce that, prebuilt_usr_share_host
// is the next best thing for now.
prebuilt_usr_share_host {
    name: "webrtc_index.html",
    src: "assets/index.html",
    filename: "index.html",
    sub_dir: "webrtc/assets",
}

prebuilt_usr_share_host {
    name: "webrtc_style.css",
    src: "assets/style.css",
    filename: "style.css",
    sub_dir: "webrtc/assets",
}

prebuilt_usr_share_host {
    name: "webrtc_logcat.js",
    src: "assets/js/logcat.js",
    filename: "logcat.js",
    sub_dir: "webrtc/assets/js",
}

prebuilt_usr_share_host {
    name: "webrtc_receive.js",
    src: "assets/js/receive.js",
    filename: "receive.js",
    sub_dir: "webrtc/assets/js",
}

prebuilt_usr_share_host {
    name: "webrtc_viewpane.js",
    src: "assets/js/viewpane.js",
    filename: "viewpane.js",
    sub_dir: "webrtc/assets/js",
}

prebuilt_usr_share_host {
    name: "webrtc_server.crt",
    src: "certs/server.crt",
    filename: "server.crt",
    sub_dir: "webrtc/certs",
}

prebuilt_usr_share_host {
    name: "webrtc_server.key",
    src: "certs/server.key",
    filename: "server.key",
    sub_dir: "webrtc/certs",
}

prebuilt_usr_share_host {
    name: "webrtc_server.p12",
    src: "certs/server.p12",
    filename: "server.p12",
    sub_dir: "webrtc/certs",
}

prebuilt_usr_share_host {
    name: "webrtc_trusted.pem",
    src: "certs/trusted.pem",
    filename: "trusted.pem",
    sub_dir: "webrtc/certs",
}

# Copyright (c) 2019-2020, The Linux Foundation. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted (subject to the limitations in the
# disclaimer below) provided that the following conditions are met:
#
#    * Redistributions of source code must retain the above copyright
#      notice, this list of conditions and the following disclaimer.
#
#    * Redistributions in binary form must reproduce the above
#      copyright notice, this list of conditions and the following
#      disclaimer in the documentation and/or other materials provided
#      with the distribution.
#
#    * Neither the name of The Linux Foundation nor the names of its
#      contributors may be used to endorse or promote products derived
#      from this software without specific prior written permission.
#
# NO EXPRESS OR IMPLIED LICENSES TO ANY PARTY'S PATENT RIGHTS ARE
# GRANTED BY THIS LICENSE. THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT
# HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED
# WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
# MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
# IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR
# ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
# DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
# GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
# INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER
# IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
# OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
# IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

#
# system.prop
#

# FEATURE_QUECTEL_ANDROID_RIL_DRIVER Mod-S
# rild.libpath=/vendor/lib64/libril-qc-hal-qmi.so
rild.libpath=/vendor/lib64/libreference-ril.so
# FEATURE_QUECTEL_ANDROID_RIL_DRIVER Mod-E
#rild.libargs=-d /dev/smd0
persist.rild.nitz_plmn=
persist.rild.nitz_long_ons_0=
persist.rild.nitz_long_ons_1=
persist.rild.nitz_long_ons_2=
persist.rild.nitz_long_ons_3=
persist.rild.nitz_short_ons_0=
persist.rild.nitz_short_ons_1=
persist.rild.nitz_short_ons_2=
persist.rild.nitz_short_ons_3=
ril.subscription.types=NV,RUIM
DEVICE_PROVISIONED=1
# Set network mode to (T/L/G/W/1X/EVDO, T/L/G/W/1X/EVDO) for 7+7 mode device on DSDS mode
ro.telephony.default_network=22,22

dalvik.vm.heapsize=36m
dev.pm.dyn_samplingrate=1

#ro.hdmi.enable=true
#persist.speaker.prot.enable=false
qcom.hw.aac.encoder=true
#
# system props for the cne module
#
persist.vendor.cne.feature=1

#system props for the MM modules
media.stagefright.enable-player=true
media.stagefright.enable-http=true
media.stagefright.enable-aac=true
media.stagefright.enable-qcp=true
media.stagefright.enable-fma2dp=true
media.stagefright.enable-scan=true
media.stagefright.thumbnail.prefer_hw_codecs=true
mmp.enable.3g2=true
media.aac_51_output_enabled=true
#13631487 is decimal sum of supported codecs in AAL
#codecs:(PARSER_)AAC AC3 AMR_NB AMR_WB ASF AVI DTS FLV 3GP 3G2 MKV MP2PS MP2TS MP3 OGG QCP WAV FLAC AIFF APE DSD MOV XVID
vendor.mm.enable.qcom_parser=63963135
persist.mm.enable.prefetch=true

#
# system props for the data modules
#
ro.vendor.use_data_netmgrd=true
persist.data.netmgrd.qos.enable=true
persist.vendor.data.mode=concurrent

#system props for time-services
persist.timed.enable=true

#
# system prop for opengles version
#
# 196608 is decimal for 0x30000 to report version 3
# 196609 is decimal for 0x30001 to report version 3.1
# 196610 is decimal for 0x30002 to report version 3.2
ro.opengles.version=196610

# system property for maximum number of HFP client connections
bt.max.hfpclient.connections=1

#
# System props for telephony
# System prop to turn on CdmaLTEPhone always
telephony.lteOnCdmaDevice=1

#Simulate sdcard on /data/media
#
persist.fuse_sdcard=true

#system prop for Bluetooth SOC type
vendor.bluetooth.soc=hastings
ro.bluetooth.library_name=libbluetooth_qti.so
persist.vendor.btstack.enable.splita2dp=true
persist.vendor.btstack.a2dp_offload_cap=sbc-aptx-aptxtws-aptxhd-aac-ldac

#system prop for wipower support
ro.bluetooth.emb_wp_mode=true
ro.bluetooth.wipower=true

#
#snapdragon value add features
#
ro.qc.sdk.audio.ssr=false

##fluencetype can be "fluence" or "fluencepro" or "none"
ro.qc.sdk.audio.fluencetype=none
persist.audio.fluence.voicecall=true
persist.audio.fluence.voicerec=false
persist.audio.fluence.speaker=true

#system prop for RmNet Data
persist.rmnet.data.enable=true
persist.data.wda.enable=true
persist.data.df.dl_mode=5
persist.data.df.ul_mode=5
persist.data.df.agg.dl_pkt=10
persist.data.df.agg.dl_size=4096
persist.data.df.mux_count=8
persist.data.df.iwlan_mux=9
persist.data.df.dev_name=rmnet_usb0

#property to enable user to access Google WFD settings
persist.debug.wfd.enable=1
##property to choose between virtual/external wfd display
persist.sys.wfd.virtual=0

# enable tunnel encoding for amrwb
tunnel.audio.encode = true

#Buffer size in kbytes for compress offload playback
audio.offload.buffer.size.kb=32

#Enable offload audio video playback by default
av.offload.enable=true

#enable voice path for PCM VoIP by default
use.voice.path.for.pcm.voip=true

# system prop for NFC DT
ro.nfc.port=I2C

#enable dsp gapless mode by default
audio.offload.gapless.enabled=true

#initialize QCA1530 detection
sys.qca1530=detect

#Enable stm events
persist.debug.coresight.config=stm-events

#hwui properties
ro.hwui.texture_cache_size=72
ro.hwui.layer_cache_size=48
ro.hwui.r_buffer_cache_size=8
ro.hwui.path_cache_size=32
ro.hwui.gradient_cache_size=1
ro.hwui.drop_shadow_cache_size=6
ro.hwui.texture_cache_flushrate=0.4
ro.hwui.text_small_cache_width=1024
ro.hwui.text_small_cache_height=1024
ro.hwui.text_large_cache_width=2048
ro.hwui.text_large_cache_height=1024

config.disable_rtt=true

#Bringup properties
persist.sys.force_sw_gles=1
persist.vendor.radio.atfwd.start=true
ro.kernel.qemu.gles=0
qemu.hw.mainkeys=0

#Enable ULMK properties
ro.lmk.kill_heaviest_task=true
ro.lmk.kill_timeout_ms=15

#Expose aux camera for below packages
vendor.camera.aux.packagelist=org.codeaurora.snapcam


/*
 * Copyright (C) 2017 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "MiniFence.h"

#include <unistd.h>

namespace android {

const sp<MiniFence> MiniFence::NO_FENCE = sp<MiniFence>(new MiniFence);

MiniFence::MiniFence() :
    mFenceFd(-1) {
}

MiniFence::MiniFence(int fenceFd) :
    mFenceFd(fenceFd) {
}

MiniFence::~MiniFence() {
    if (mFenceFd != -1) {
        close(mFenceFd);
    }
}

int MiniFence::dup() const {
    return ::dup(mFenceFd);
}
}

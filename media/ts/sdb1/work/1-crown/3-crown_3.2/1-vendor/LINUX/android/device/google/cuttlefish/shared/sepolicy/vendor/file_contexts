##########################
# Devices
#

# crosvm (x86) block devices
/dev/block/pci/pci0000:00/0000:00:01\.0/by-name/misc u:object_r:misc_block_device:s0
/dev/block/pci/pci0000:00/0000:00:01\.0/by-name/boot_[ab] u:object_r:boot_block_device:s0
/dev/block/pci/pci0000:00/0000:00:01\.0/by-name/vendor_boot_[ab] u:object_r:boot_block_device:s0
/dev/block/pci/pci0000:00/0000:00:01\.0/by-name/verity_[ab] u:object_r:ab_block_device:s0
/dev/block/pci/pci0000:00/0000:00:01\.0/by-name/verity_system_[ab] u:object_r:ab_block_device:s0
/dev/block/pci/pci0000:00/0000:00:01\.0/by-name/super u:object_r:super_block_device:s0
/dev/block/pci/pci0000:00/0000:00:01\.0/by-name/userdata u:object_r:userdata_block_device:s0
/dev/block/pci/pci0000:00/0000:00:01\.0/by-name/cache u:object_r:cache_block_device:s0
/dev/block/pci/pci0000:00/0000:00:01\.0/by-name/metadata u:object_r:metadata_block_device:s0
# crosvm (arm64) block devices
/dev/block/platform/10000.pci/by-name/misc u:object_r:misc_block_device:s0
/dev/block/platform/10000.pci/by-name/boot_[ab] u:object_r:boot_block_device:s0
/dev/block/platform/10000.pci/by-name/vendor_boot_[ab] u:object_r:boot_block_device:s0
/dev/block/platform/10000.pci/by-name/verity_[ab] u:object_r:ab_block_device:s0
/dev/block/platform/10000.pci/by-name/verity_system_[ab] u:object_r:ab_block_device:s0
/dev/block/platform/10000.pci/by-name/super u:object_r:super_block_device:s0
/dev/block/platform/10000.pci/by-name/userdata u:object_r:userdata_block_device:s0
/dev/block/platform/10000.pci/by-name/cache u:object_r:cache_block_device:s0
/dev/block/platform/10000.pci/by-name/metadata u:object_r:metadata_block_device:s0
# qemu block devices
/dev/block/pci/pci0000:00/0000:00:03\.0/by-name/misc u:object_r:misc_block_device:s0
/dev/block/pci/pci0000:00/0000:00:03\.0/by-name/boot_[ab] u:object_r:boot_block_device:s0
/dev/block/pci/pci0000:00/0000:00:03\.0/by-name/vendor_boot_[ab] u:object_r:boot_block_device:s0
/dev/block/pci/pci0000:00/0000:00:03\.0/by-name/verity_[ab] u:object_r:ab_block_device:s0
/dev/block/pci/pci0000:00/0000:00:03\.0/by-name/verity_system_[ab] u:object_r:ab_block_device:s0
/dev/block/pci/pci0000:00/0000:00:03\.0/by-name/super u:object_r:super_block_device:s0
/dev/block/pci/pci0000:00/0000:00:03\.0/by-name/userdata u:object_r:userdata_block_device:s0
/dev/block/pci/pci0000:00/0000:00:03\.0/by-name/cache u:object_r:cache_block_device:s0
/dev/block/pci/pci0000:00/0000:00:03\.0/by-name/metadata u:object_r:metadata_block_device:s0

/dev/block/pmem0  u:object_r:rebootescrow_device:s0
/dev/block/zram0  u:object_r:swap_block_device:s0
/dev/dri u:object_r:gpu_device:s0
/dev/dri/card0  u:object_r:graphics_device:s0
/dev/dri/renderD128  u:object_r:gpu_device:s0
/dev/vport[0-9]p[0-9]*  u:object_r:virtual_serial_device:s0

#############################
# Root files
/initial\.metadata  u:object_r:initial_metadata_file:s0
/ts_snap\.txt  u:object_r:tombstone_snapshot_file:s0

#############################
# data files
/data/vendor/mediadrm(/.*)?  u:object_r:mediadrm_vendor_data_file:s0

#############################
# var files
/var/run/system(/.*)?  u:object_r:var_run_system_file:s0

#############################
# Vendor files
#
/vendor/bin/socket_vsock_proxy  u:object_r:socket_vsock_proxy_exec:s0
/vendor/bin/vsock_logcat  u:object_r:vsock_logcat_exec:s0
/vendor/bin/vsoc_input_service  u:object_r:vsoc_input_service_exec:s0
/vendor/bin/vport_trigger  u:object_r:vport_trigger_exec:s0
/vendor/bin/rename_netiface  u:object_r:rename_netiface_exec:s0
/vendor/bin/hw/libcuttlefish-rild  u:object_r:libcuttlefish_rild_exec:s0
/vendor/bin/hw/android\.hardware\.camera\.provider@2\.6-service-google u:object_r:hal_camera_default_exec:s0
/vendor/bin/hw/android\.hardware\.camera\.provider@2\.6-service-google-lazy u:object_r:hal_camera_default_exec:s0
/vendor/bin/hw/android\.hardware\.power\.stats@1\.0-service\.mock  u:object_r:hal_power_stats_default_exec:s0
/vendor/bin/hw/android\.hardware\.bluetooth@1\.1-service\.sim  u:object_r:hal_bluetooth_sim_exec:s0
/vendor/bin/hw/android\.hardware\.contexthub@1\.1-service\.mock  u:object_r:hal_contexthub_default_exec:s0
/vendor/bin/hw/android\.hardware\.drm@[0-9]+\.[0-9]+-service\.clearkey  u:object_r:hal_drm_clearkey_exec:s0
/vendor/bin/hw/android\.hardware\.drm@[0-9]+\.[0-9]+-service-lazy\.clearkey  u:object_r:hal_drm_clearkey_exec:s0
/vendor/bin/hw/android\.hardware\.drm@[0-9]+\.[0-9]+-service\.widevine  u:object_r:hal_drm_widevine_exec:s0
/vendor/bin/hw/android\.hardware\.drm@[0-9]+\.[0-9]+-service-lazy\.widevine  u:object_r:hal_drm_widevine_exec:s0
/vendor/bin/hw/android\.hardware\.graphics\.allocator@4\.0-service\.minigbm   u:object_r:hal_graphics_allocator_default_exec:s0
/vendor/bin/hw/android\.hardware\.gatekeeper@1\.0-service\.software  u:object_r:hal_gatekeeper_default_exec:s0
/vendor/bin/hw/android\.hardware\.health\.storage@1\.0-service\.cuttlefish u:object_r:hal_health_storage_default_exec:s0
/vendor/bin/hw/android\.hardware\.lights-service\.example u:object_r:hal_light_default_exec:s0
/vendor/bin/hw/android\.hardware\.neuralnetworks@1\.3-service-sample-.*   u:object_r:hal_neuralnetworks_sample_exec:s0
/vendor/bin/hw/android\.hardware\.vibrator@1\.x-service\.example u:object_r:hal_vibrator_default_exec:s0
/vendor/bin/ip_link_add  u:object_r:ip_link_add_exec:s0
/vendor/bin/setup_wifi  u:object_r:setup_wifi_exec:s0
/vendor/bin/hw/android\.hardware\.sensors@2\.1-service\.mock  u:object_r:hal_sensors_default_exec:s0
/vendor/bin/hw/android\.hardware\.input\.classifier@1\.0-service.default  u:object_r:hal_input_classifier_default_exec:s0
/vendor/bin/hw/android\.hardware\.thermal@2\.0-service\.mock  u:object_r:hal_thermal_default_exec:s0
/vendor/bin/hw/android\.hardware\.authsecret@1\.0-service  u:object_r:hal_authsecret_default_exec:s0
/vendor/bin/hw/android\.hardware\.rebootescrow-service\.default  u:object_r:hal_rebootescrow_default_exec:s0
/vendor/bin/init\.insmod\.sh  u:object_r:init_insmod_sh_exec:s0

/vendor/lib(64)?/libdrm.so  u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libglapi.so  u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/dri/.* u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/hw/android\.hardware\.graphics\.mapper@4\.0-impl\.minigbm\.so u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/hw/android\.hardware\.health@2\.0-impl-2\.1-cuttlefish\.so  u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/hw/vulkan.pastel.so  u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libcuttlefish_fs.so  u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/vsoc_lib.so  u:object_r:same_process_hal_file:s0

# gfxstream (to be better factored (fewer libraries?))
/vendor/lib(64)?/hw/vulkan\.ranchu\.so   u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libEGL_emulation\.so          u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libGLESv1_CM_emulation\.so    u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libGLESv2_emulation\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libOpenglCodecCommon\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libOpenglSystemCommon\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/lib_renderControl_enc\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libGLESv1_enc\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libGLESv2_enc\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libvulkan_enc\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libandroidemu\.so       u:object_r:same_process_hal_file:s0

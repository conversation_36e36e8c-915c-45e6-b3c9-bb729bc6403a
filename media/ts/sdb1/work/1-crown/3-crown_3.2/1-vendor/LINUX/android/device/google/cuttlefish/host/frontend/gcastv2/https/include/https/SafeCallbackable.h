/*
 * Copyright (C) 2019 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include <memory>

template<typename T>
std::function<void()> makeSafeCallback(T *obj, std::function<void(T *)> f) {
    auto weak_me = std::weak_ptr<T>(obj->shared_from_this());
    return [f, weak_me]{
        auto me = weak_me.lock();
        if (me) {
            f(me.get());
        }
    };
}

template<typename T, typename... Params>
std::function<void()> makeSafeCallback(
        T *obj, void (T::*f)(const Params&...), const Params&... params) {
    return makeSafeCallback<T>(
            obj,
            [f, params...](T *me) {
                (me->*f)(params...);
            });
}

template<typename T, typename... Params>
std::function<void()> makeSafeCallback(
        T *obj, void (T::*f)(Params...), const Params&... params) {
    return makeSafeCallback<T>(
            obj,
            [f, params...](T *me) {
                (me->*f)(params...);
            });
}

// Copyright (C) 2019 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.


cc_library_shared {
    name: "hwcomposer.cutf_cvm_ashmem",
    relative_install_path: "hw",
    defaults: ["cuttlefish_guest_only"],
    vendor: true,
    srcs: [
        "hwcomposer.cpp",
        "vsocket_screen_view.cpp",
    ],
    include_dirs: [
        "device/google/cuttlefish",
    ],
    export_include_dirs: ["."],
    static_libs: [
        "hwcomposer_common",
        "libyuv_static",
    ],
    shared_libs: [
        "android.hardware.graphics.mapper@4.0",
        "libbase",
        "libcutils",
        "libcuttlefish_device_config",
        "libcuttlefish_utils",
        "libcuttlefish_fs",
        "libdrm",
        "libgralloctypes",
        "libhardware",
        "libhidlbase",
        "libjpeg",
        "liblog",
        "libsync",
        "libutils",
    ],
}

cc_library_shared {
    name: "hwcomposer.cutf_hwc2",
    relative_install_path: "hw",
    defaults: ["cuttlefish_guest_only"],
    vendor: true,

    clang: true,
    cflags: [
        "-Wall",
        "-Werror",
    ],
    cppflags: [
        "-Wextra",
        "-Wunused",
        "-Wunreachable-code",

        // Disabling warning specific to hwc2on1adapter code
        "-Wno-sign-compare",
    ],

    srcs: [
        "HWC2.cpp",
        "MiniFence.cpp",
        "vsocket_screen_view.cpp",
    ],

    include_dirs: [
        "device/google/cuttlefish",
    ],

    export_include_dirs: ["."],

    static_libs: [
        "hwcomposer_common",
        "libyuv_static",
    ],

    shared_libs: [
        "android.hardware.graphics.mapper@4.0",
        "libcutils",
        "libcuttlefish_device_config",
        "libcuttlefish_utils",
        "libcuttlefish_fs",
        "libgralloctypes",
        "libhardware",
        "libhidlbase",
        "liblog",
        "libutils",
    ],
}

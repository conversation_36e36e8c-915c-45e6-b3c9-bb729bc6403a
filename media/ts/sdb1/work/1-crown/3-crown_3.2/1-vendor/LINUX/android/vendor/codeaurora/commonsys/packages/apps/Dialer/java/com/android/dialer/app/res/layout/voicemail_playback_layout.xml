<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2011 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout
  xmlns:android="http://schemas.android.com/apk/res/android"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:layout_marginStart="64dp"
  android:layout_marginEnd="24dp"
  android:background="?android:attr/colorBackgroundFloating"
  android:orientation="vertical">

  <TextView
    android:id="@+id/playback_state_text"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    style="@style/Dialer.TextAppearance.Secondary"/>

  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/voicemail_playback_top_padding"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <TextView
      android:id="@+id/playback_position_text"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:importantForAccessibility="no"
      style="@style/Dialer.TextAppearance.Secondary"/>

    <SeekBar
      android:id="@+id/playback_seek"
      android:layout_width="0dp"
      android:layout_height="wrap_content"
      android:layout_weight="1"
      android:contentDescription="@string/description_playback_seek"
      android:max="0"
      android:progress="0"
      android:progressDrawable="@drawable/seekbar_drawable"
      android:thumb="@drawable/ic_voicemail_seek_handle"/>

    <TextView
      android:id="@+id/total_duration_text"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:importantForAccessibility="no"
      style="@style/Dialer.TextAppearance.Secondary"/>

  </LinearLayout>

  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="horizontal">

    <Space
      android:layout_width="0dp"
      android:layout_height="match_parent"
      android:layout_weight="1"/>

    <ImageButton
      android:id="@+id/playback_speakerphone"
      style="@style/VoicemailPlaybackLayoutButtonStyle"
      android:contentDescription="@string/description_playback_speakerphone"
      android:src="@drawable/quantum_ic_volume_down_white_24"
      android:tint="?colorIcon"/>

    <Space
      android:layout_width="0dp"
      android:layout_height="match_parent"
      android:layout_weight="1"/>

    <ImageButton
      android:id="@+id/playback_start_stop"
      style="@style/VoicemailPlaybackLayoutButtonStyle"
      android:contentDescription="@string/voicemail_play_start_pause"
      android:src="@drawable/ic_play_arrow"/>

    <Space
      android:layout_width="0dp"
      android:layout_height="match_parent"
      android:layout_weight="1"/>

    <ImageButton
      android:id="@+id/delete_voicemail"
      style="@style/VoicemailPlaybackLayoutButtonStyle"
      android:contentDescription="@string/call_log_trash_voicemail"
      android:src="@drawable/quantum_ic_delete_white_24"
      android:tint="?colorIcon"/>

    <Space
      android:layout_width="0dp"
      android:layout_height="match_parent"
      android:layout_weight="1"/>

  </LinearLayout>

</LinearLayout>

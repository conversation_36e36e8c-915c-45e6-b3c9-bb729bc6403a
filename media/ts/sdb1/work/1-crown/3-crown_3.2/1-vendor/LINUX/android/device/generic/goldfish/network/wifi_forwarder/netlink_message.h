/*
 * Copyright 2019, The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include "result.h"

#include <netlink/netlink.h>

#include <type_traits>

class NetlinkMessage {
public:
    NetlinkMessage();
    NetlinkMessage(NetlinkMessage&& other);
    ~NetlinkMessage();

    NetlinkMessage& operator=(NetlinkMessage&& other);

    bool initGeneric(int family, uint8_t command, int version);

    template<typename T,
             typename = typename std::enable_if<!std::is_pointer<T>::value>::type>
    bool addAttribute(int attribute, const T& value) {
       return nla_put(mMessage, attribute, sizeof(T), &value) == 0;
    }
    template<typename T,
             typename = typename std::enable_if<std::is_pointer<T>::value>::type>
    bool addAttribute(int attribute, T data, size_t numElements) {
        return nla_put(mMessage,
                       attribute,
                       sizeof(*data) * numElements,
                       data) == 0;
    }

    uint32_t getSeqNum() const;

    struct nl_msg* get() { return mMessage; }

private:
    NetlinkMessage(const NetlinkMessage&) = delete;
    NetlinkMessage& operator=(const NetlinkMessage&) = delete;

    struct nl_msg* mMessage = nullptr;
};


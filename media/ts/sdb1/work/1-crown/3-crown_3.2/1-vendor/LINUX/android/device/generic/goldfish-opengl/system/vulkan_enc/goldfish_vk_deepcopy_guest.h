// Copyright (C) 2018 The Android Open Source Project
// Copyright (C) 2018 Google Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Autogenerated module goldfish_vk_deepcopy_guest
// (header) generated by android/android-emugl/host/libs/libOpenglRender/vulkan-registry/xml/genvk.py -registry android/android-emugl/host/libs/libOpenglRender/vulkan-registry/xml/vk.xml cereal -o android/android-emugl/host/libs/libOpenglRender/vulkan/cereal
// Please do not modify directly;
// re-run android/scripts/generate-vulkan-sources.sh,
// or directly from Python by defining:
// VULKAN_REGISTRY_XML_DIR : Directory containing genvk.py and vk.xml
// CEREAL_OUTPUT_DIR: Where to put the generated sources.
// python3 $VULKAN_REGISTRY_XML_DIR/genvk.py -registry $VULKAN_REGISTRY_XML_DIR/vk.xml cereal -o $CEREAL_OUTPUT_DIR

#pragma once

#include <vulkan/vulkan.h>


#include "vk_platform_compat.h"

#include "goldfish_vk_private_defs.h"
#include "android/base/Pool.h"
using android::base::Pool;
// Stuff we are not going to use but if included,
// will cause compile errors. These are Android Vulkan
// required extensions, but the approach will be to
// implement them completely on the guest side.
#undef VK_KHR_android_surface
#undef VK_ANDROID_external_memory_android_hardware_buffer


namespace goldfish_vk {

#ifdef VK_VERSION_1_0
void deepcopy_VkApplicationInfo(
    Pool* pool,
    const VkApplicationInfo* from,
    VkApplicationInfo* to);

void deepcopy_VkInstanceCreateInfo(
    Pool* pool,
    const VkInstanceCreateInfo* from,
    VkInstanceCreateInfo* to);

void deepcopy_VkAllocationCallbacks(
    Pool* pool,
    const VkAllocationCallbacks* from,
    VkAllocationCallbacks* to);

void deepcopy_VkPhysicalDeviceFeatures(
    Pool* pool,
    const VkPhysicalDeviceFeatures* from,
    VkPhysicalDeviceFeatures* to);

void deepcopy_VkFormatProperties(
    Pool* pool,
    const VkFormatProperties* from,
    VkFormatProperties* to);

void deepcopy_VkExtent3D(
    Pool* pool,
    const VkExtent3D* from,
    VkExtent3D* to);

void deepcopy_VkImageFormatProperties(
    Pool* pool,
    const VkImageFormatProperties* from,
    VkImageFormatProperties* to);

void deepcopy_VkPhysicalDeviceLimits(
    Pool* pool,
    const VkPhysicalDeviceLimits* from,
    VkPhysicalDeviceLimits* to);

void deepcopy_VkPhysicalDeviceSparseProperties(
    Pool* pool,
    const VkPhysicalDeviceSparseProperties* from,
    VkPhysicalDeviceSparseProperties* to);

void deepcopy_VkPhysicalDeviceProperties(
    Pool* pool,
    const VkPhysicalDeviceProperties* from,
    VkPhysicalDeviceProperties* to);

void deepcopy_VkQueueFamilyProperties(
    Pool* pool,
    const VkQueueFamilyProperties* from,
    VkQueueFamilyProperties* to);

void deepcopy_VkMemoryType(
    Pool* pool,
    const VkMemoryType* from,
    VkMemoryType* to);

void deepcopy_VkMemoryHeap(
    Pool* pool,
    const VkMemoryHeap* from,
    VkMemoryHeap* to);

void deepcopy_VkPhysicalDeviceMemoryProperties(
    Pool* pool,
    const VkPhysicalDeviceMemoryProperties* from,
    VkPhysicalDeviceMemoryProperties* to);

void deepcopy_VkDeviceQueueCreateInfo(
    Pool* pool,
    const VkDeviceQueueCreateInfo* from,
    VkDeviceQueueCreateInfo* to);

void deepcopy_VkDeviceCreateInfo(
    Pool* pool,
    const VkDeviceCreateInfo* from,
    VkDeviceCreateInfo* to);

void deepcopy_VkExtensionProperties(
    Pool* pool,
    const VkExtensionProperties* from,
    VkExtensionProperties* to);

void deepcopy_VkLayerProperties(
    Pool* pool,
    const VkLayerProperties* from,
    VkLayerProperties* to);

void deepcopy_VkSubmitInfo(
    Pool* pool,
    const VkSubmitInfo* from,
    VkSubmitInfo* to);

void deepcopy_VkMemoryAllocateInfo(
    Pool* pool,
    const VkMemoryAllocateInfo* from,
    VkMemoryAllocateInfo* to);

void deepcopy_VkMappedMemoryRange(
    Pool* pool,
    const VkMappedMemoryRange* from,
    VkMappedMemoryRange* to);

void deepcopy_VkMemoryRequirements(
    Pool* pool,
    const VkMemoryRequirements* from,
    VkMemoryRequirements* to);

void deepcopy_VkSparseImageFormatProperties(
    Pool* pool,
    const VkSparseImageFormatProperties* from,
    VkSparseImageFormatProperties* to);

void deepcopy_VkSparseImageMemoryRequirements(
    Pool* pool,
    const VkSparseImageMemoryRequirements* from,
    VkSparseImageMemoryRequirements* to);

void deepcopy_VkSparseMemoryBind(
    Pool* pool,
    const VkSparseMemoryBind* from,
    VkSparseMemoryBind* to);

void deepcopy_VkSparseBufferMemoryBindInfo(
    Pool* pool,
    const VkSparseBufferMemoryBindInfo* from,
    VkSparseBufferMemoryBindInfo* to);

void deepcopy_VkSparseImageOpaqueMemoryBindInfo(
    Pool* pool,
    const VkSparseImageOpaqueMemoryBindInfo* from,
    VkSparseImageOpaqueMemoryBindInfo* to);

void deepcopy_VkImageSubresource(
    Pool* pool,
    const VkImageSubresource* from,
    VkImageSubresource* to);

void deepcopy_VkOffset3D(
    Pool* pool,
    const VkOffset3D* from,
    VkOffset3D* to);

void deepcopy_VkSparseImageMemoryBind(
    Pool* pool,
    const VkSparseImageMemoryBind* from,
    VkSparseImageMemoryBind* to);

void deepcopy_VkSparseImageMemoryBindInfo(
    Pool* pool,
    const VkSparseImageMemoryBindInfo* from,
    VkSparseImageMemoryBindInfo* to);

void deepcopy_VkBindSparseInfo(
    Pool* pool,
    const VkBindSparseInfo* from,
    VkBindSparseInfo* to);

void deepcopy_VkFenceCreateInfo(
    Pool* pool,
    const VkFenceCreateInfo* from,
    VkFenceCreateInfo* to);

void deepcopy_VkSemaphoreCreateInfo(
    Pool* pool,
    const VkSemaphoreCreateInfo* from,
    VkSemaphoreCreateInfo* to);

void deepcopy_VkEventCreateInfo(
    Pool* pool,
    const VkEventCreateInfo* from,
    VkEventCreateInfo* to);

void deepcopy_VkQueryPoolCreateInfo(
    Pool* pool,
    const VkQueryPoolCreateInfo* from,
    VkQueryPoolCreateInfo* to);

void deepcopy_VkBufferCreateInfo(
    Pool* pool,
    const VkBufferCreateInfo* from,
    VkBufferCreateInfo* to);

void deepcopy_VkBufferViewCreateInfo(
    Pool* pool,
    const VkBufferViewCreateInfo* from,
    VkBufferViewCreateInfo* to);

void deepcopy_VkImageCreateInfo(
    Pool* pool,
    const VkImageCreateInfo* from,
    VkImageCreateInfo* to);

void deepcopy_VkSubresourceLayout(
    Pool* pool,
    const VkSubresourceLayout* from,
    VkSubresourceLayout* to);

void deepcopy_VkComponentMapping(
    Pool* pool,
    const VkComponentMapping* from,
    VkComponentMapping* to);

void deepcopy_VkImageSubresourceRange(
    Pool* pool,
    const VkImageSubresourceRange* from,
    VkImageSubresourceRange* to);

void deepcopy_VkImageViewCreateInfo(
    Pool* pool,
    const VkImageViewCreateInfo* from,
    VkImageViewCreateInfo* to);

void deepcopy_VkShaderModuleCreateInfo(
    Pool* pool,
    const VkShaderModuleCreateInfo* from,
    VkShaderModuleCreateInfo* to);

void deepcopy_VkPipelineCacheCreateInfo(
    Pool* pool,
    const VkPipelineCacheCreateInfo* from,
    VkPipelineCacheCreateInfo* to);

void deepcopy_VkSpecializationMapEntry(
    Pool* pool,
    const VkSpecializationMapEntry* from,
    VkSpecializationMapEntry* to);

void deepcopy_VkSpecializationInfo(
    Pool* pool,
    const VkSpecializationInfo* from,
    VkSpecializationInfo* to);

void deepcopy_VkPipelineShaderStageCreateInfo(
    Pool* pool,
    const VkPipelineShaderStageCreateInfo* from,
    VkPipelineShaderStageCreateInfo* to);

void deepcopy_VkVertexInputBindingDescription(
    Pool* pool,
    const VkVertexInputBindingDescription* from,
    VkVertexInputBindingDescription* to);

void deepcopy_VkVertexInputAttributeDescription(
    Pool* pool,
    const VkVertexInputAttributeDescription* from,
    VkVertexInputAttributeDescription* to);

void deepcopy_VkPipelineVertexInputStateCreateInfo(
    Pool* pool,
    const VkPipelineVertexInputStateCreateInfo* from,
    VkPipelineVertexInputStateCreateInfo* to);

void deepcopy_VkPipelineInputAssemblyStateCreateInfo(
    Pool* pool,
    const VkPipelineInputAssemblyStateCreateInfo* from,
    VkPipelineInputAssemblyStateCreateInfo* to);

void deepcopy_VkPipelineTessellationStateCreateInfo(
    Pool* pool,
    const VkPipelineTessellationStateCreateInfo* from,
    VkPipelineTessellationStateCreateInfo* to);

void deepcopy_VkViewport(
    Pool* pool,
    const VkViewport* from,
    VkViewport* to);

void deepcopy_VkOffset2D(
    Pool* pool,
    const VkOffset2D* from,
    VkOffset2D* to);

void deepcopy_VkExtent2D(
    Pool* pool,
    const VkExtent2D* from,
    VkExtent2D* to);

void deepcopy_VkRect2D(
    Pool* pool,
    const VkRect2D* from,
    VkRect2D* to);

void deepcopy_VkPipelineViewportStateCreateInfo(
    Pool* pool,
    const VkPipelineViewportStateCreateInfo* from,
    VkPipelineViewportStateCreateInfo* to);

void deepcopy_VkPipelineRasterizationStateCreateInfo(
    Pool* pool,
    const VkPipelineRasterizationStateCreateInfo* from,
    VkPipelineRasterizationStateCreateInfo* to);

void deepcopy_VkPipelineMultisampleStateCreateInfo(
    Pool* pool,
    const VkPipelineMultisampleStateCreateInfo* from,
    VkPipelineMultisampleStateCreateInfo* to);

void deepcopy_VkStencilOpState(
    Pool* pool,
    const VkStencilOpState* from,
    VkStencilOpState* to);

void deepcopy_VkPipelineDepthStencilStateCreateInfo(
    Pool* pool,
    const VkPipelineDepthStencilStateCreateInfo* from,
    VkPipelineDepthStencilStateCreateInfo* to);

void deepcopy_VkPipelineColorBlendAttachmentState(
    Pool* pool,
    const VkPipelineColorBlendAttachmentState* from,
    VkPipelineColorBlendAttachmentState* to);

void deepcopy_VkPipelineColorBlendStateCreateInfo(
    Pool* pool,
    const VkPipelineColorBlendStateCreateInfo* from,
    VkPipelineColorBlendStateCreateInfo* to);

void deepcopy_VkPipelineDynamicStateCreateInfo(
    Pool* pool,
    const VkPipelineDynamicStateCreateInfo* from,
    VkPipelineDynamicStateCreateInfo* to);

void deepcopy_VkGraphicsPipelineCreateInfo(
    Pool* pool,
    const VkGraphicsPipelineCreateInfo* from,
    VkGraphicsPipelineCreateInfo* to);

void deepcopy_VkComputePipelineCreateInfo(
    Pool* pool,
    const VkComputePipelineCreateInfo* from,
    VkComputePipelineCreateInfo* to);

void deepcopy_VkPushConstantRange(
    Pool* pool,
    const VkPushConstantRange* from,
    VkPushConstantRange* to);

void deepcopy_VkPipelineLayoutCreateInfo(
    Pool* pool,
    const VkPipelineLayoutCreateInfo* from,
    VkPipelineLayoutCreateInfo* to);

void deepcopy_VkSamplerCreateInfo(
    Pool* pool,
    const VkSamplerCreateInfo* from,
    VkSamplerCreateInfo* to);

void deepcopy_VkDescriptorSetLayoutBinding(
    Pool* pool,
    const VkDescriptorSetLayoutBinding* from,
    VkDescriptorSetLayoutBinding* to);

void deepcopy_VkDescriptorSetLayoutCreateInfo(
    Pool* pool,
    const VkDescriptorSetLayoutCreateInfo* from,
    VkDescriptorSetLayoutCreateInfo* to);

void deepcopy_VkDescriptorPoolSize(
    Pool* pool,
    const VkDescriptorPoolSize* from,
    VkDescriptorPoolSize* to);

void deepcopy_VkDescriptorPoolCreateInfo(
    Pool* pool,
    const VkDescriptorPoolCreateInfo* from,
    VkDescriptorPoolCreateInfo* to);

void deepcopy_VkDescriptorSetAllocateInfo(
    Pool* pool,
    const VkDescriptorSetAllocateInfo* from,
    VkDescriptorSetAllocateInfo* to);

void deepcopy_VkDescriptorImageInfo(
    Pool* pool,
    const VkDescriptorImageInfo* from,
    VkDescriptorImageInfo* to);

void deepcopy_VkDescriptorBufferInfo(
    Pool* pool,
    const VkDescriptorBufferInfo* from,
    VkDescriptorBufferInfo* to);

void deepcopy_VkWriteDescriptorSet(
    Pool* pool,
    const VkWriteDescriptorSet* from,
    VkWriteDescriptorSet* to);

void deepcopy_VkCopyDescriptorSet(
    Pool* pool,
    const VkCopyDescriptorSet* from,
    VkCopyDescriptorSet* to);

void deepcopy_VkFramebufferCreateInfo(
    Pool* pool,
    const VkFramebufferCreateInfo* from,
    VkFramebufferCreateInfo* to);

void deepcopy_VkAttachmentDescription(
    Pool* pool,
    const VkAttachmentDescription* from,
    VkAttachmentDescription* to);

void deepcopy_VkAttachmentReference(
    Pool* pool,
    const VkAttachmentReference* from,
    VkAttachmentReference* to);

void deepcopy_VkSubpassDescription(
    Pool* pool,
    const VkSubpassDescription* from,
    VkSubpassDescription* to);

void deepcopy_VkSubpassDependency(
    Pool* pool,
    const VkSubpassDependency* from,
    VkSubpassDependency* to);

void deepcopy_VkRenderPassCreateInfo(
    Pool* pool,
    const VkRenderPassCreateInfo* from,
    VkRenderPassCreateInfo* to);

void deepcopy_VkCommandPoolCreateInfo(
    Pool* pool,
    const VkCommandPoolCreateInfo* from,
    VkCommandPoolCreateInfo* to);

void deepcopy_VkCommandBufferAllocateInfo(
    Pool* pool,
    const VkCommandBufferAllocateInfo* from,
    VkCommandBufferAllocateInfo* to);

void deepcopy_VkCommandBufferInheritanceInfo(
    Pool* pool,
    const VkCommandBufferInheritanceInfo* from,
    VkCommandBufferInheritanceInfo* to);

void deepcopy_VkCommandBufferBeginInfo(
    Pool* pool,
    const VkCommandBufferBeginInfo* from,
    VkCommandBufferBeginInfo* to);

void deepcopy_VkBufferCopy(
    Pool* pool,
    const VkBufferCopy* from,
    VkBufferCopy* to);

void deepcopy_VkImageSubresourceLayers(
    Pool* pool,
    const VkImageSubresourceLayers* from,
    VkImageSubresourceLayers* to);

void deepcopy_VkImageCopy(
    Pool* pool,
    const VkImageCopy* from,
    VkImageCopy* to);

void deepcopy_VkImageBlit(
    Pool* pool,
    const VkImageBlit* from,
    VkImageBlit* to);

void deepcopy_VkBufferImageCopy(
    Pool* pool,
    const VkBufferImageCopy* from,
    VkBufferImageCopy* to);

void deepcopy_VkClearColorValue(
    Pool* pool,
    const VkClearColorValue* from,
    VkClearColorValue* to);

void deepcopy_VkClearDepthStencilValue(
    Pool* pool,
    const VkClearDepthStencilValue* from,
    VkClearDepthStencilValue* to);

void deepcopy_VkClearValue(
    Pool* pool,
    const VkClearValue* from,
    VkClearValue* to);

void deepcopy_VkClearAttachment(
    Pool* pool,
    const VkClearAttachment* from,
    VkClearAttachment* to);

void deepcopy_VkClearRect(
    Pool* pool,
    const VkClearRect* from,
    VkClearRect* to);

void deepcopy_VkImageResolve(
    Pool* pool,
    const VkImageResolve* from,
    VkImageResolve* to);

void deepcopy_VkMemoryBarrier(
    Pool* pool,
    const VkMemoryBarrier* from,
    VkMemoryBarrier* to);

void deepcopy_VkBufferMemoryBarrier(
    Pool* pool,
    const VkBufferMemoryBarrier* from,
    VkBufferMemoryBarrier* to);

void deepcopy_VkImageMemoryBarrier(
    Pool* pool,
    const VkImageMemoryBarrier* from,
    VkImageMemoryBarrier* to);

void deepcopy_VkRenderPassBeginInfo(
    Pool* pool,
    const VkRenderPassBeginInfo* from,
    VkRenderPassBeginInfo* to);

void deepcopy_VkDispatchIndirectCommand(
    Pool* pool,
    const VkDispatchIndirectCommand* from,
    VkDispatchIndirectCommand* to);

void deepcopy_VkDrawIndexedIndirectCommand(
    Pool* pool,
    const VkDrawIndexedIndirectCommand* from,
    VkDrawIndexedIndirectCommand* to);

void deepcopy_VkDrawIndirectCommand(
    Pool* pool,
    const VkDrawIndirectCommand* from,
    VkDrawIndirectCommand* to);

void deepcopy_VkBaseOutStructure(
    Pool* pool,
    const VkBaseOutStructure* from,
    VkBaseOutStructure* to);

void deepcopy_VkBaseInStructure(
    Pool* pool,
    const VkBaseInStructure* from,
    VkBaseInStructure* to);

#endif
#ifdef VK_VERSION_1_1
void deepcopy_VkPhysicalDeviceSubgroupProperties(
    Pool* pool,
    const VkPhysicalDeviceSubgroupProperties* from,
    VkPhysicalDeviceSubgroupProperties* to);

void deepcopy_VkBindBufferMemoryInfo(
    Pool* pool,
    const VkBindBufferMemoryInfo* from,
    VkBindBufferMemoryInfo* to);

void deepcopy_VkBindImageMemoryInfo(
    Pool* pool,
    const VkBindImageMemoryInfo* from,
    VkBindImageMemoryInfo* to);

void deepcopy_VkPhysicalDevice16BitStorageFeatures(
    Pool* pool,
    const VkPhysicalDevice16BitStorageFeatures* from,
    VkPhysicalDevice16BitStorageFeatures* to);

void deepcopy_VkMemoryDedicatedRequirements(
    Pool* pool,
    const VkMemoryDedicatedRequirements* from,
    VkMemoryDedicatedRequirements* to);

void deepcopy_VkMemoryDedicatedAllocateInfo(
    Pool* pool,
    const VkMemoryDedicatedAllocateInfo* from,
    VkMemoryDedicatedAllocateInfo* to);

void deepcopy_VkMemoryAllocateFlagsInfo(
    Pool* pool,
    const VkMemoryAllocateFlagsInfo* from,
    VkMemoryAllocateFlagsInfo* to);

void deepcopy_VkDeviceGroupRenderPassBeginInfo(
    Pool* pool,
    const VkDeviceGroupRenderPassBeginInfo* from,
    VkDeviceGroupRenderPassBeginInfo* to);

void deepcopy_VkDeviceGroupCommandBufferBeginInfo(
    Pool* pool,
    const VkDeviceGroupCommandBufferBeginInfo* from,
    VkDeviceGroupCommandBufferBeginInfo* to);

void deepcopy_VkDeviceGroupSubmitInfo(
    Pool* pool,
    const VkDeviceGroupSubmitInfo* from,
    VkDeviceGroupSubmitInfo* to);

void deepcopy_VkDeviceGroupBindSparseInfo(
    Pool* pool,
    const VkDeviceGroupBindSparseInfo* from,
    VkDeviceGroupBindSparseInfo* to);

void deepcopy_VkBindBufferMemoryDeviceGroupInfo(
    Pool* pool,
    const VkBindBufferMemoryDeviceGroupInfo* from,
    VkBindBufferMemoryDeviceGroupInfo* to);

void deepcopy_VkBindImageMemoryDeviceGroupInfo(
    Pool* pool,
    const VkBindImageMemoryDeviceGroupInfo* from,
    VkBindImageMemoryDeviceGroupInfo* to);

void deepcopy_VkPhysicalDeviceGroupProperties(
    Pool* pool,
    const VkPhysicalDeviceGroupProperties* from,
    VkPhysicalDeviceGroupProperties* to);

void deepcopy_VkDeviceGroupDeviceCreateInfo(
    Pool* pool,
    const VkDeviceGroupDeviceCreateInfo* from,
    VkDeviceGroupDeviceCreateInfo* to);

void deepcopy_VkBufferMemoryRequirementsInfo2(
    Pool* pool,
    const VkBufferMemoryRequirementsInfo2* from,
    VkBufferMemoryRequirementsInfo2* to);

void deepcopy_VkImageMemoryRequirementsInfo2(
    Pool* pool,
    const VkImageMemoryRequirementsInfo2* from,
    VkImageMemoryRequirementsInfo2* to);

void deepcopy_VkImageSparseMemoryRequirementsInfo2(
    Pool* pool,
    const VkImageSparseMemoryRequirementsInfo2* from,
    VkImageSparseMemoryRequirementsInfo2* to);

void deepcopy_VkMemoryRequirements2(
    Pool* pool,
    const VkMemoryRequirements2* from,
    VkMemoryRequirements2* to);

void deepcopy_VkSparseImageMemoryRequirements2(
    Pool* pool,
    const VkSparseImageMemoryRequirements2* from,
    VkSparseImageMemoryRequirements2* to);

void deepcopy_VkPhysicalDeviceFeatures2(
    Pool* pool,
    const VkPhysicalDeviceFeatures2* from,
    VkPhysicalDeviceFeatures2* to);

void deepcopy_VkPhysicalDeviceProperties2(
    Pool* pool,
    const VkPhysicalDeviceProperties2* from,
    VkPhysicalDeviceProperties2* to);

void deepcopy_VkFormatProperties2(
    Pool* pool,
    const VkFormatProperties2* from,
    VkFormatProperties2* to);

void deepcopy_VkImageFormatProperties2(
    Pool* pool,
    const VkImageFormatProperties2* from,
    VkImageFormatProperties2* to);

void deepcopy_VkPhysicalDeviceImageFormatInfo2(
    Pool* pool,
    const VkPhysicalDeviceImageFormatInfo2* from,
    VkPhysicalDeviceImageFormatInfo2* to);

void deepcopy_VkQueueFamilyProperties2(
    Pool* pool,
    const VkQueueFamilyProperties2* from,
    VkQueueFamilyProperties2* to);

void deepcopy_VkPhysicalDeviceMemoryProperties2(
    Pool* pool,
    const VkPhysicalDeviceMemoryProperties2* from,
    VkPhysicalDeviceMemoryProperties2* to);

void deepcopy_VkSparseImageFormatProperties2(
    Pool* pool,
    const VkSparseImageFormatProperties2* from,
    VkSparseImageFormatProperties2* to);

void deepcopy_VkPhysicalDeviceSparseImageFormatInfo2(
    Pool* pool,
    const VkPhysicalDeviceSparseImageFormatInfo2* from,
    VkPhysicalDeviceSparseImageFormatInfo2* to);

void deepcopy_VkPhysicalDevicePointClippingProperties(
    Pool* pool,
    const VkPhysicalDevicePointClippingProperties* from,
    VkPhysicalDevicePointClippingProperties* to);

void deepcopy_VkInputAttachmentAspectReference(
    Pool* pool,
    const VkInputAttachmentAspectReference* from,
    VkInputAttachmentAspectReference* to);

void deepcopy_VkRenderPassInputAttachmentAspectCreateInfo(
    Pool* pool,
    const VkRenderPassInputAttachmentAspectCreateInfo* from,
    VkRenderPassInputAttachmentAspectCreateInfo* to);

void deepcopy_VkImageViewUsageCreateInfo(
    Pool* pool,
    const VkImageViewUsageCreateInfo* from,
    VkImageViewUsageCreateInfo* to);

void deepcopy_VkPipelineTessellationDomainOriginStateCreateInfo(
    Pool* pool,
    const VkPipelineTessellationDomainOriginStateCreateInfo* from,
    VkPipelineTessellationDomainOriginStateCreateInfo* to);

void deepcopy_VkRenderPassMultiviewCreateInfo(
    Pool* pool,
    const VkRenderPassMultiviewCreateInfo* from,
    VkRenderPassMultiviewCreateInfo* to);

void deepcopy_VkPhysicalDeviceMultiviewFeatures(
    Pool* pool,
    const VkPhysicalDeviceMultiviewFeatures* from,
    VkPhysicalDeviceMultiviewFeatures* to);

void deepcopy_VkPhysicalDeviceMultiviewProperties(
    Pool* pool,
    const VkPhysicalDeviceMultiviewProperties* from,
    VkPhysicalDeviceMultiviewProperties* to);

void deepcopy_VkPhysicalDeviceVariablePointerFeatures(
    Pool* pool,
    const VkPhysicalDeviceVariablePointerFeatures* from,
    VkPhysicalDeviceVariablePointerFeatures* to);

void deepcopy_VkPhysicalDeviceProtectedMemoryFeatures(
    Pool* pool,
    const VkPhysicalDeviceProtectedMemoryFeatures* from,
    VkPhysicalDeviceProtectedMemoryFeatures* to);

void deepcopy_VkPhysicalDeviceProtectedMemoryProperties(
    Pool* pool,
    const VkPhysicalDeviceProtectedMemoryProperties* from,
    VkPhysicalDeviceProtectedMemoryProperties* to);

void deepcopy_VkDeviceQueueInfo2(
    Pool* pool,
    const VkDeviceQueueInfo2* from,
    VkDeviceQueueInfo2* to);

void deepcopy_VkProtectedSubmitInfo(
    Pool* pool,
    const VkProtectedSubmitInfo* from,
    VkProtectedSubmitInfo* to);

void deepcopy_VkSamplerYcbcrConversionCreateInfo(
    Pool* pool,
    const VkSamplerYcbcrConversionCreateInfo* from,
    VkSamplerYcbcrConversionCreateInfo* to);

void deepcopy_VkSamplerYcbcrConversionInfo(
    Pool* pool,
    const VkSamplerYcbcrConversionInfo* from,
    VkSamplerYcbcrConversionInfo* to);

void deepcopy_VkBindImagePlaneMemoryInfo(
    Pool* pool,
    const VkBindImagePlaneMemoryInfo* from,
    VkBindImagePlaneMemoryInfo* to);

void deepcopy_VkImagePlaneMemoryRequirementsInfo(
    Pool* pool,
    const VkImagePlaneMemoryRequirementsInfo* from,
    VkImagePlaneMemoryRequirementsInfo* to);

void deepcopy_VkPhysicalDeviceSamplerYcbcrConversionFeatures(
    Pool* pool,
    const VkPhysicalDeviceSamplerYcbcrConversionFeatures* from,
    VkPhysicalDeviceSamplerYcbcrConversionFeatures* to);

void deepcopy_VkSamplerYcbcrConversionImageFormatProperties(
    Pool* pool,
    const VkSamplerYcbcrConversionImageFormatProperties* from,
    VkSamplerYcbcrConversionImageFormatProperties* to);

void deepcopy_VkDescriptorUpdateTemplateEntry(
    Pool* pool,
    const VkDescriptorUpdateTemplateEntry* from,
    VkDescriptorUpdateTemplateEntry* to);

void deepcopy_VkDescriptorUpdateTemplateCreateInfo(
    Pool* pool,
    const VkDescriptorUpdateTemplateCreateInfo* from,
    VkDescriptorUpdateTemplateCreateInfo* to);

void deepcopy_VkExternalMemoryProperties(
    Pool* pool,
    const VkExternalMemoryProperties* from,
    VkExternalMemoryProperties* to);

void deepcopy_VkPhysicalDeviceExternalImageFormatInfo(
    Pool* pool,
    const VkPhysicalDeviceExternalImageFormatInfo* from,
    VkPhysicalDeviceExternalImageFormatInfo* to);

void deepcopy_VkExternalImageFormatProperties(
    Pool* pool,
    const VkExternalImageFormatProperties* from,
    VkExternalImageFormatProperties* to);

void deepcopy_VkPhysicalDeviceExternalBufferInfo(
    Pool* pool,
    const VkPhysicalDeviceExternalBufferInfo* from,
    VkPhysicalDeviceExternalBufferInfo* to);

void deepcopy_VkExternalBufferProperties(
    Pool* pool,
    const VkExternalBufferProperties* from,
    VkExternalBufferProperties* to);

void deepcopy_VkPhysicalDeviceIDProperties(
    Pool* pool,
    const VkPhysicalDeviceIDProperties* from,
    VkPhysicalDeviceIDProperties* to);

void deepcopy_VkExternalMemoryImageCreateInfo(
    Pool* pool,
    const VkExternalMemoryImageCreateInfo* from,
    VkExternalMemoryImageCreateInfo* to);

void deepcopy_VkExternalMemoryBufferCreateInfo(
    Pool* pool,
    const VkExternalMemoryBufferCreateInfo* from,
    VkExternalMemoryBufferCreateInfo* to);

void deepcopy_VkExportMemoryAllocateInfo(
    Pool* pool,
    const VkExportMemoryAllocateInfo* from,
    VkExportMemoryAllocateInfo* to);

void deepcopy_VkPhysicalDeviceExternalFenceInfo(
    Pool* pool,
    const VkPhysicalDeviceExternalFenceInfo* from,
    VkPhysicalDeviceExternalFenceInfo* to);

void deepcopy_VkExternalFenceProperties(
    Pool* pool,
    const VkExternalFenceProperties* from,
    VkExternalFenceProperties* to);

void deepcopy_VkExportFenceCreateInfo(
    Pool* pool,
    const VkExportFenceCreateInfo* from,
    VkExportFenceCreateInfo* to);

void deepcopy_VkExportSemaphoreCreateInfo(
    Pool* pool,
    const VkExportSemaphoreCreateInfo* from,
    VkExportSemaphoreCreateInfo* to);

void deepcopy_VkPhysicalDeviceExternalSemaphoreInfo(
    Pool* pool,
    const VkPhysicalDeviceExternalSemaphoreInfo* from,
    VkPhysicalDeviceExternalSemaphoreInfo* to);

void deepcopy_VkExternalSemaphoreProperties(
    Pool* pool,
    const VkExternalSemaphoreProperties* from,
    VkExternalSemaphoreProperties* to);

void deepcopy_VkPhysicalDeviceMaintenance3Properties(
    Pool* pool,
    const VkPhysicalDeviceMaintenance3Properties* from,
    VkPhysicalDeviceMaintenance3Properties* to);

void deepcopy_VkDescriptorSetLayoutSupport(
    Pool* pool,
    const VkDescriptorSetLayoutSupport* from,
    VkDescriptorSetLayoutSupport* to);

void deepcopy_VkPhysicalDeviceShaderDrawParameterFeatures(
    Pool* pool,
    const VkPhysicalDeviceShaderDrawParameterFeatures* from,
    VkPhysicalDeviceShaderDrawParameterFeatures* to);

#endif
#ifdef VK_KHR_surface
void deepcopy_VkSurfaceCapabilitiesKHR(
    Pool* pool,
    const VkSurfaceCapabilitiesKHR* from,
    VkSurfaceCapabilitiesKHR* to);

void deepcopy_VkSurfaceFormatKHR(
    Pool* pool,
    const VkSurfaceFormatKHR* from,
    VkSurfaceFormatKHR* to);

#endif
#ifdef VK_KHR_swapchain
void deepcopy_VkSwapchainCreateInfoKHR(
    Pool* pool,
    const VkSwapchainCreateInfoKHR* from,
    VkSwapchainCreateInfoKHR* to);

void deepcopy_VkPresentInfoKHR(
    Pool* pool,
    const VkPresentInfoKHR* from,
    VkPresentInfoKHR* to);

void deepcopy_VkImageSwapchainCreateInfoKHR(
    Pool* pool,
    const VkImageSwapchainCreateInfoKHR* from,
    VkImageSwapchainCreateInfoKHR* to);

void deepcopy_VkBindImageMemorySwapchainInfoKHR(
    Pool* pool,
    const VkBindImageMemorySwapchainInfoKHR* from,
    VkBindImageMemorySwapchainInfoKHR* to);

void deepcopy_VkAcquireNextImageInfoKHR(
    Pool* pool,
    const VkAcquireNextImageInfoKHR* from,
    VkAcquireNextImageInfoKHR* to);

void deepcopy_VkDeviceGroupPresentCapabilitiesKHR(
    Pool* pool,
    const VkDeviceGroupPresentCapabilitiesKHR* from,
    VkDeviceGroupPresentCapabilitiesKHR* to);

void deepcopy_VkDeviceGroupPresentInfoKHR(
    Pool* pool,
    const VkDeviceGroupPresentInfoKHR* from,
    VkDeviceGroupPresentInfoKHR* to);

void deepcopy_VkDeviceGroupSwapchainCreateInfoKHR(
    Pool* pool,
    const VkDeviceGroupSwapchainCreateInfoKHR* from,
    VkDeviceGroupSwapchainCreateInfoKHR* to);

#endif
#ifdef VK_KHR_display
void deepcopy_VkDisplayPropertiesKHR(
    Pool* pool,
    const VkDisplayPropertiesKHR* from,
    VkDisplayPropertiesKHR* to);

void deepcopy_VkDisplayModeParametersKHR(
    Pool* pool,
    const VkDisplayModeParametersKHR* from,
    VkDisplayModeParametersKHR* to);

void deepcopy_VkDisplayModePropertiesKHR(
    Pool* pool,
    const VkDisplayModePropertiesKHR* from,
    VkDisplayModePropertiesKHR* to);

void deepcopy_VkDisplayModeCreateInfoKHR(
    Pool* pool,
    const VkDisplayModeCreateInfoKHR* from,
    VkDisplayModeCreateInfoKHR* to);

void deepcopy_VkDisplayPlaneCapabilitiesKHR(
    Pool* pool,
    const VkDisplayPlaneCapabilitiesKHR* from,
    VkDisplayPlaneCapabilitiesKHR* to);

void deepcopy_VkDisplayPlanePropertiesKHR(
    Pool* pool,
    const VkDisplayPlanePropertiesKHR* from,
    VkDisplayPlanePropertiesKHR* to);

void deepcopy_VkDisplaySurfaceCreateInfoKHR(
    Pool* pool,
    const VkDisplaySurfaceCreateInfoKHR* from,
    VkDisplaySurfaceCreateInfoKHR* to);

#endif
#ifdef VK_KHR_display_swapchain
void deepcopy_VkDisplayPresentInfoKHR(
    Pool* pool,
    const VkDisplayPresentInfoKHR* from,
    VkDisplayPresentInfoKHR* to);

#endif
#ifdef VK_KHR_xlib_surface
void deepcopy_VkXlibSurfaceCreateInfoKHR(
    Pool* pool,
    const VkXlibSurfaceCreateInfoKHR* from,
    VkXlibSurfaceCreateInfoKHR* to);

#endif
#ifdef VK_KHR_xcb_surface
void deepcopy_VkXcbSurfaceCreateInfoKHR(
    Pool* pool,
    const VkXcbSurfaceCreateInfoKHR* from,
    VkXcbSurfaceCreateInfoKHR* to);

#endif
#ifdef VK_KHR_wayland_surface
void deepcopy_VkWaylandSurfaceCreateInfoKHR(
    Pool* pool,
    const VkWaylandSurfaceCreateInfoKHR* from,
    VkWaylandSurfaceCreateInfoKHR* to);

#endif
#ifdef VK_KHR_mir_surface
void deepcopy_VkMirSurfaceCreateInfoKHR(
    Pool* pool,
    const VkMirSurfaceCreateInfoKHR* from,
    VkMirSurfaceCreateInfoKHR* to);

#endif
#ifdef VK_KHR_android_surface
void deepcopy_VkAndroidSurfaceCreateInfoKHR(
    Pool* pool,
    const VkAndroidSurfaceCreateInfoKHR* from,
    VkAndroidSurfaceCreateInfoKHR* to);

#endif
#ifdef VK_KHR_win32_surface
void deepcopy_VkWin32SurfaceCreateInfoKHR(
    Pool* pool,
    const VkWin32SurfaceCreateInfoKHR* from,
    VkWin32SurfaceCreateInfoKHR* to);

#endif
#ifdef VK_KHR_sampler_mirror_clamp_to_edge
#endif
#ifdef VK_KHR_multiview
#endif
#ifdef VK_KHR_get_physical_device_properties2
#endif
#ifdef VK_KHR_device_group
#endif
#ifdef VK_KHR_shader_draw_parameters
#endif
#ifdef VK_KHR_maintenance1
#endif
#ifdef VK_KHR_device_group_creation
#endif
#ifdef VK_KHR_external_memory_capabilities
#endif
#ifdef VK_KHR_external_memory
#endif
#ifdef VK_KHR_external_memory_win32
void deepcopy_VkImportMemoryWin32HandleInfoKHR(
    Pool* pool,
    const VkImportMemoryWin32HandleInfoKHR* from,
    VkImportMemoryWin32HandleInfoKHR* to);

void deepcopy_VkExportMemoryWin32HandleInfoKHR(
    Pool* pool,
    const VkExportMemoryWin32HandleInfoKHR* from,
    VkExportMemoryWin32HandleInfoKHR* to);

void deepcopy_VkMemoryWin32HandlePropertiesKHR(
    Pool* pool,
    const VkMemoryWin32HandlePropertiesKHR* from,
    VkMemoryWin32HandlePropertiesKHR* to);

void deepcopy_VkMemoryGetWin32HandleInfoKHR(
    Pool* pool,
    const VkMemoryGetWin32HandleInfoKHR* from,
    VkMemoryGetWin32HandleInfoKHR* to);

#endif
#ifdef VK_KHR_external_memory_fd
void deepcopy_VkImportMemoryFdInfoKHR(
    Pool* pool,
    const VkImportMemoryFdInfoKHR* from,
    VkImportMemoryFdInfoKHR* to);

void deepcopy_VkMemoryFdPropertiesKHR(
    Pool* pool,
    const VkMemoryFdPropertiesKHR* from,
    VkMemoryFdPropertiesKHR* to);

void deepcopy_VkMemoryGetFdInfoKHR(
    Pool* pool,
    const VkMemoryGetFdInfoKHR* from,
    VkMemoryGetFdInfoKHR* to);

#endif
#ifdef VK_KHR_win32_keyed_mutex
void deepcopy_VkWin32KeyedMutexAcquireReleaseInfoKHR(
    Pool* pool,
    const VkWin32KeyedMutexAcquireReleaseInfoKHR* from,
    VkWin32KeyedMutexAcquireReleaseInfoKHR* to);

#endif
#ifdef VK_KHR_external_semaphore_capabilities
#endif
#ifdef VK_KHR_external_semaphore
#endif
#ifdef VK_KHR_external_semaphore_win32
void deepcopy_VkImportSemaphoreWin32HandleInfoKHR(
    Pool* pool,
    const VkImportSemaphoreWin32HandleInfoKHR* from,
    VkImportSemaphoreWin32HandleInfoKHR* to);

void deepcopy_VkExportSemaphoreWin32HandleInfoKHR(
    Pool* pool,
    const VkExportSemaphoreWin32HandleInfoKHR* from,
    VkExportSemaphoreWin32HandleInfoKHR* to);

void deepcopy_VkD3D12FenceSubmitInfoKHR(
    Pool* pool,
    const VkD3D12FenceSubmitInfoKHR* from,
    VkD3D12FenceSubmitInfoKHR* to);

void deepcopy_VkSemaphoreGetWin32HandleInfoKHR(
    Pool* pool,
    const VkSemaphoreGetWin32HandleInfoKHR* from,
    VkSemaphoreGetWin32HandleInfoKHR* to);

#endif
#ifdef VK_KHR_external_semaphore_fd
void deepcopy_VkImportSemaphoreFdInfoKHR(
    Pool* pool,
    const VkImportSemaphoreFdInfoKHR* from,
    VkImportSemaphoreFdInfoKHR* to);

void deepcopy_VkSemaphoreGetFdInfoKHR(
    Pool* pool,
    const VkSemaphoreGetFdInfoKHR* from,
    VkSemaphoreGetFdInfoKHR* to);

#endif
#ifdef VK_KHR_push_descriptor
void deepcopy_VkPhysicalDevicePushDescriptorPropertiesKHR(
    Pool* pool,
    const VkPhysicalDevicePushDescriptorPropertiesKHR* from,
    VkPhysicalDevicePushDescriptorPropertiesKHR* to);

#endif
#ifdef VK_KHR_16bit_storage
#endif
#ifdef VK_KHR_incremental_present
void deepcopy_VkRectLayerKHR(
    Pool* pool,
    const VkRectLayerKHR* from,
    VkRectLayerKHR* to);

void deepcopy_VkPresentRegionKHR(
    Pool* pool,
    const VkPresentRegionKHR* from,
    VkPresentRegionKHR* to);

void deepcopy_VkPresentRegionsKHR(
    Pool* pool,
    const VkPresentRegionsKHR* from,
    VkPresentRegionsKHR* to);

#endif
#ifdef VK_KHR_descriptor_update_template
#endif
#ifdef VK_KHR_create_renderpass2
void deepcopy_VkAttachmentDescription2KHR(
    Pool* pool,
    const VkAttachmentDescription2KHR* from,
    VkAttachmentDescription2KHR* to);

void deepcopy_VkAttachmentReference2KHR(
    Pool* pool,
    const VkAttachmentReference2KHR* from,
    VkAttachmentReference2KHR* to);

void deepcopy_VkSubpassDescription2KHR(
    Pool* pool,
    const VkSubpassDescription2KHR* from,
    VkSubpassDescription2KHR* to);

void deepcopy_VkSubpassDependency2KHR(
    Pool* pool,
    const VkSubpassDependency2KHR* from,
    VkSubpassDependency2KHR* to);

void deepcopy_VkRenderPassCreateInfo2KHR(
    Pool* pool,
    const VkRenderPassCreateInfo2KHR* from,
    VkRenderPassCreateInfo2KHR* to);

void deepcopy_VkSubpassBeginInfoKHR(
    Pool* pool,
    const VkSubpassBeginInfoKHR* from,
    VkSubpassBeginInfoKHR* to);

void deepcopy_VkSubpassEndInfoKHR(
    Pool* pool,
    const VkSubpassEndInfoKHR* from,
    VkSubpassEndInfoKHR* to);

#endif
#ifdef VK_KHR_shared_presentable_image
void deepcopy_VkSharedPresentSurfaceCapabilitiesKHR(
    Pool* pool,
    const VkSharedPresentSurfaceCapabilitiesKHR* from,
    VkSharedPresentSurfaceCapabilitiesKHR* to);

#endif
#ifdef VK_KHR_external_fence_capabilities
#endif
#ifdef VK_KHR_external_fence
#endif
#ifdef VK_KHR_external_fence_win32
void deepcopy_VkImportFenceWin32HandleInfoKHR(
    Pool* pool,
    const VkImportFenceWin32HandleInfoKHR* from,
    VkImportFenceWin32HandleInfoKHR* to);

void deepcopy_VkExportFenceWin32HandleInfoKHR(
    Pool* pool,
    const VkExportFenceWin32HandleInfoKHR* from,
    VkExportFenceWin32HandleInfoKHR* to);

void deepcopy_VkFenceGetWin32HandleInfoKHR(
    Pool* pool,
    const VkFenceGetWin32HandleInfoKHR* from,
    VkFenceGetWin32HandleInfoKHR* to);

#endif
#ifdef VK_KHR_external_fence_fd
void deepcopy_VkImportFenceFdInfoKHR(
    Pool* pool,
    const VkImportFenceFdInfoKHR* from,
    VkImportFenceFdInfoKHR* to);

void deepcopy_VkFenceGetFdInfoKHR(
    Pool* pool,
    const VkFenceGetFdInfoKHR* from,
    VkFenceGetFdInfoKHR* to);

#endif
#ifdef VK_KHR_maintenance2
#endif
#ifdef VK_KHR_get_surface_capabilities2
void deepcopy_VkPhysicalDeviceSurfaceInfo2KHR(
    Pool* pool,
    const VkPhysicalDeviceSurfaceInfo2KHR* from,
    VkPhysicalDeviceSurfaceInfo2KHR* to);

void deepcopy_VkSurfaceCapabilities2KHR(
    Pool* pool,
    const VkSurfaceCapabilities2KHR* from,
    VkSurfaceCapabilities2KHR* to);

void deepcopy_VkSurfaceFormat2KHR(
    Pool* pool,
    const VkSurfaceFormat2KHR* from,
    VkSurfaceFormat2KHR* to);

#endif
#ifdef VK_KHR_variable_pointers
#endif
#ifdef VK_KHR_get_display_properties2
void deepcopy_VkDisplayProperties2KHR(
    Pool* pool,
    const VkDisplayProperties2KHR* from,
    VkDisplayProperties2KHR* to);

void deepcopy_VkDisplayPlaneProperties2KHR(
    Pool* pool,
    const VkDisplayPlaneProperties2KHR* from,
    VkDisplayPlaneProperties2KHR* to);

void deepcopy_VkDisplayModeProperties2KHR(
    Pool* pool,
    const VkDisplayModeProperties2KHR* from,
    VkDisplayModeProperties2KHR* to);

void deepcopy_VkDisplayPlaneInfo2KHR(
    Pool* pool,
    const VkDisplayPlaneInfo2KHR* from,
    VkDisplayPlaneInfo2KHR* to);

void deepcopy_VkDisplayPlaneCapabilities2KHR(
    Pool* pool,
    const VkDisplayPlaneCapabilities2KHR* from,
    VkDisplayPlaneCapabilities2KHR* to);

#endif
#ifdef VK_KHR_dedicated_allocation
#endif
#ifdef VK_KHR_storage_buffer_storage_class
#endif
#ifdef VK_KHR_relaxed_block_layout
#endif
#ifdef VK_KHR_get_memory_requirements2
#endif
#ifdef VK_KHR_image_format_list
void deepcopy_VkImageFormatListCreateInfoKHR(
    Pool* pool,
    const VkImageFormatListCreateInfoKHR* from,
    VkImageFormatListCreateInfoKHR* to);

#endif
#ifdef VK_KHR_sampler_ycbcr_conversion
#endif
#ifdef VK_KHR_bind_memory2
#endif
#ifdef VK_KHR_maintenance3
#endif
#ifdef VK_KHR_draw_indirect_count
#endif
#ifdef VK_KHR_8bit_storage
void deepcopy_VkPhysicalDevice8BitStorageFeaturesKHR(
    Pool* pool,
    const VkPhysicalDevice8BitStorageFeaturesKHR* from,
    VkPhysicalDevice8BitStorageFeaturesKHR* to);

#endif
#ifdef VK_ANDROID_native_buffer
void deepcopy_VkNativeBufferANDROID(
    Pool* pool,
    const VkNativeBufferANDROID* from,
    VkNativeBufferANDROID* to);

#endif
#ifdef VK_EXT_debug_report
void deepcopy_VkDebugReportCallbackCreateInfoEXT(
    Pool* pool,
    const VkDebugReportCallbackCreateInfoEXT* from,
    VkDebugReportCallbackCreateInfoEXT* to);

#endif
#ifdef VK_NV_glsl_shader
#endif
#ifdef VK_EXT_depth_range_unrestricted
#endif
#ifdef VK_IMG_filter_cubic
#endif
#ifdef VK_AMD_rasterization_order
void deepcopy_VkPipelineRasterizationStateRasterizationOrderAMD(
    Pool* pool,
    const VkPipelineRasterizationStateRasterizationOrderAMD* from,
    VkPipelineRasterizationStateRasterizationOrderAMD* to);

#endif
#ifdef VK_AMD_shader_trinary_minmax
#endif
#ifdef VK_AMD_shader_explicit_vertex_parameter
#endif
#ifdef VK_EXT_debug_marker
void deepcopy_VkDebugMarkerObjectNameInfoEXT(
    Pool* pool,
    const VkDebugMarkerObjectNameInfoEXT* from,
    VkDebugMarkerObjectNameInfoEXT* to);

void deepcopy_VkDebugMarkerObjectTagInfoEXT(
    Pool* pool,
    const VkDebugMarkerObjectTagInfoEXT* from,
    VkDebugMarkerObjectTagInfoEXT* to);

void deepcopy_VkDebugMarkerMarkerInfoEXT(
    Pool* pool,
    const VkDebugMarkerMarkerInfoEXT* from,
    VkDebugMarkerMarkerInfoEXT* to);

#endif
#ifdef VK_AMD_gcn_shader
#endif
#ifdef VK_NV_dedicated_allocation
void deepcopy_VkDedicatedAllocationImageCreateInfoNV(
    Pool* pool,
    const VkDedicatedAllocationImageCreateInfoNV* from,
    VkDedicatedAllocationImageCreateInfoNV* to);

void deepcopy_VkDedicatedAllocationBufferCreateInfoNV(
    Pool* pool,
    const VkDedicatedAllocationBufferCreateInfoNV* from,
    VkDedicatedAllocationBufferCreateInfoNV* to);

void deepcopy_VkDedicatedAllocationMemoryAllocateInfoNV(
    Pool* pool,
    const VkDedicatedAllocationMemoryAllocateInfoNV* from,
    VkDedicatedAllocationMemoryAllocateInfoNV* to);

#endif
#ifdef VK_AMD_draw_indirect_count
#endif
#ifdef VK_AMD_negative_viewport_height
#endif
#ifdef VK_AMD_gpu_shader_half_float
#endif
#ifdef VK_AMD_shader_ballot
#endif
#ifdef VK_AMD_texture_gather_bias_lod
void deepcopy_VkTextureLODGatherFormatPropertiesAMD(
    Pool* pool,
    const VkTextureLODGatherFormatPropertiesAMD* from,
    VkTextureLODGatherFormatPropertiesAMD* to);

#endif
#ifdef VK_AMD_shader_info
void deepcopy_VkShaderResourceUsageAMD(
    Pool* pool,
    const VkShaderResourceUsageAMD* from,
    VkShaderResourceUsageAMD* to);

void deepcopy_VkShaderStatisticsInfoAMD(
    Pool* pool,
    const VkShaderStatisticsInfoAMD* from,
    VkShaderStatisticsInfoAMD* to);

#endif
#ifdef VK_AMD_shader_image_load_store_lod
#endif
#ifdef VK_IMG_format_pvrtc
#endif
#ifdef VK_NV_external_memory_capabilities
void deepcopy_VkExternalImageFormatPropertiesNV(
    Pool* pool,
    const VkExternalImageFormatPropertiesNV* from,
    VkExternalImageFormatPropertiesNV* to);

#endif
#ifdef VK_NV_external_memory
void deepcopy_VkExternalMemoryImageCreateInfoNV(
    Pool* pool,
    const VkExternalMemoryImageCreateInfoNV* from,
    VkExternalMemoryImageCreateInfoNV* to);

void deepcopy_VkExportMemoryAllocateInfoNV(
    Pool* pool,
    const VkExportMemoryAllocateInfoNV* from,
    VkExportMemoryAllocateInfoNV* to);

#endif
#ifdef VK_NV_external_memory_win32
void deepcopy_VkImportMemoryWin32HandleInfoNV(
    Pool* pool,
    const VkImportMemoryWin32HandleInfoNV* from,
    VkImportMemoryWin32HandleInfoNV* to);

void deepcopy_VkExportMemoryWin32HandleInfoNV(
    Pool* pool,
    const VkExportMemoryWin32HandleInfoNV* from,
    VkExportMemoryWin32HandleInfoNV* to);

#endif
#ifdef VK_NV_win32_keyed_mutex
void deepcopy_VkWin32KeyedMutexAcquireReleaseInfoNV(
    Pool* pool,
    const VkWin32KeyedMutexAcquireReleaseInfoNV* from,
    VkWin32KeyedMutexAcquireReleaseInfoNV* to);

#endif
#ifdef VK_EXT_validation_flags
void deepcopy_VkValidationFlagsEXT(
    Pool* pool,
    const VkValidationFlagsEXT* from,
    VkValidationFlagsEXT* to);

#endif
#ifdef VK_NN_vi_surface
void deepcopy_VkViSurfaceCreateInfoNN(
    Pool* pool,
    const VkViSurfaceCreateInfoNN* from,
    VkViSurfaceCreateInfoNN* to);

#endif
#ifdef VK_EXT_shader_subgroup_ballot
#endif
#ifdef VK_EXT_shader_subgroup_vote
#endif
#ifdef VK_EXT_conditional_rendering
void deepcopy_VkConditionalRenderingBeginInfoEXT(
    Pool* pool,
    const VkConditionalRenderingBeginInfoEXT* from,
    VkConditionalRenderingBeginInfoEXT* to);

void deepcopy_VkPhysicalDeviceConditionalRenderingFeaturesEXT(
    Pool* pool,
    const VkPhysicalDeviceConditionalRenderingFeaturesEXT* from,
    VkPhysicalDeviceConditionalRenderingFeaturesEXT* to);

void deepcopy_VkCommandBufferInheritanceConditionalRenderingInfoEXT(
    Pool* pool,
    const VkCommandBufferInheritanceConditionalRenderingInfoEXT* from,
    VkCommandBufferInheritanceConditionalRenderingInfoEXT* to);

#endif
#ifdef VK_NVX_device_generated_commands
void deepcopy_VkDeviceGeneratedCommandsFeaturesNVX(
    Pool* pool,
    const VkDeviceGeneratedCommandsFeaturesNVX* from,
    VkDeviceGeneratedCommandsFeaturesNVX* to);

void deepcopy_VkDeviceGeneratedCommandsLimitsNVX(
    Pool* pool,
    const VkDeviceGeneratedCommandsLimitsNVX* from,
    VkDeviceGeneratedCommandsLimitsNVX* to);

void deepcopy_VkIndirectCommandsTokenNVX(
    Pool* pool,
    const VkIndirectCommandsTokenNVX* from,
    VkIndirectCommandsTokenNVX* to);

void deepcopy_VkIndirectCommandsLayoutTokenNVX(
    Pool* pool,
    const VkIndirectCommandsLayoutTokenNVX* from,
    VkIndirectCommandsLayoutTokenNVX* to);

void deepcopy_VkIndirectCommandsLayoutCreateInfoNVX(
    Pool* pool,
    const VkIndirectCommandsLayoutCreateInfoNVX* from,
    VkIndirectCommandsLayoutCreateInfoNVX* to);

void deepcopy_VkCmdProcessCommandsInfoNVX(
    Pool* pool,
    const VkCmdProcessCommandsInfoNVX* from,
    VkCmdProcessCommandsInfoNVX* to);

void deepcopy_VkCmdReserveSpaceForCommandsInfoNVX(
    Pool* pool,
    const VkCmdReserveSpaceForCommandsInfoNVX* from,
    VkCmdReserveSpaceForCommandsInfoNVX* to);

void deepcopy_VkObjectTableCreateInfoNVX(
    Pool* pool,
    const VkObjectTableCreateInfoNVX* from,
    VkObjectTableCreateInfoNVX* to);

void deepcopy_VkObjectTableEntryNVX(
    Pool* pool,
    const VkObjectTableEntryNVX* from,
    VkObjectTableEntryNVX* to);

void deepcopy_VkObjectTablePipelineEntryNVX(
    Pool* pool,
    const VkObjectTablePipelineEntryNVX* from,
    VkObjectTablePipelineEntryNVX* to);

void deepcopy_VkObjectTableDescriptorSetEntryNVX(
    Pool* pool,
    const VkObjectTableDescriptorSetEntryNVX* from,
    VkObjectTableDescriptorSetEntryNVX* to);

void deepcopy_VkObjectTableVertexBufferEntryNVX(
    Pool* pool,
    const VkObjectTableVertexBufferEntryNVX* from,
    VkObjectTableVertexBufferEntryNVX* to);

void deepcopy_VkObjectTableIndexBufferEntryNVX(
    Pool* pool,
    const VkObjectTableIndexBufferEntryNVX* from,
    VkObjectTableIndexBufferEntryNVX* to);

void deepcopy_VkObjectTablePushConstantEntryNVX(
    Pool* pool,
    const VkObjectTablePushConstantEntryNVX* from,
    VkObjectTablePushConstantEntryNVX* to);

#endif
#ifdef VK_NV_clip_space_w_scaling
void deepcopy_VkViewportWScalingNV(
    Pool* pool,
    const VkViewportWScalingNV* from,
    VkViewportWScalingNV* to);

void deepcopy_VkPipelineViewportWScalingStateCreateInfoNV(
    Pool* pool,
    const VkPipelineViewportWScalingStateCreateInfoNV* from,
    VkPipelineViewportWScalingStateCreateInfoNV* to);

#endif
#ifdef VK_EXT_direct_mode_display
#endif
#ifdef VK_EXT_acquire_xlib_display
#endif
#ifdef VK_EXT_display_surface_counter
void deepcopy_VkSurfaceCapabilities2EXT(
    Pool* pool,
    const VkSurfaceCapabilities2EXT* from,
    VkSurfaceCapabilities2EXT* to);

#endif
#ifdef VK_EXT_display_control
void deepcopy_VkDisplayPowerInfoEXT(
    Pool* pool,
    const VkDisplayPowerInfoEXT* from,
    VkDisplayPowerInfoEXT* to);

void deepcopy_VkDeviceEventInfoEXT(
    Pool* pool,
    const VkDeviceEventInfoEXT* from,
    VkDeviceEventInfoEXT* to);

void deepcopy_VkDisplayEventInfoEXT(
    Pool* pool,
    const VkDisplayEventInfoEXT* from,
    VkDisplayEventInfoEXT* to);

void deepcopy_VkSwapchainCounterCreateInfoEXT(
    Pool* pool,
    const VkSwapchainCounterCreateInfoEXT* from,
    VkSwapchainCounterCreateInfoEXT* to);

#endif
#ifdef VK_GOOGLE_display_timing
void deepcopy_VkRefreshCycleDurationGOOGLE(
    Pool* pool,
    const VkRefreshCycleDurationGOOGLE* from,
    VkRefreshCycleDurationGOOGLE* to);

void deepcopy_VkPastPresentationTimingGOOGLE(
    Pool* pool,
    const VkPastPresentationTimingGOOGLE* from,
    VkPastPresentationTimingGOOGLE* to);

void deepcopy_VkPresentTimeGOOGLE(
    Pool* pool,
    const VkPresentTimeGOOGLE* from,
    VkPresentTimeGOOGLE* to);

void deepcopy_VkPresentTimesInfoGOOGLE(
    Pool* pool,
    const VkPresentTimesInfoGOOGLE* from,
    VkPresentTimesInfoGOOGLE* to);

#endif
#ifdef VK_NV_sample_mask_override_coverage
#endif
#ifdef VK_NV_geometry_shader_passthrough
#endif
#ifdef VK_NV_viewport_array2
#endif
#ifdef VK_NVX_multiview_per_view_attributes
void deepcopy_VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX(
    Pool* pool,
    const VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX* from,
    VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX* to);

#endif
#ifdef VK_NV_viewport_swizzle
void deepcopy_VkViewportSwizzleNV(
    Pool* pool,
    const VkViewportSwizzleNV* from,
    VkViewportSwizzleNV* to);

void deepcopy_VkPipelineViewportSwizzleStateCreateInfoNV(
    Pool* pool,
    const VkPipelineViewportSwizzleStateCreateInfoNV* from,
    VkPipelineViewportSwizzleStateCreateInfoNV* to);

#endif
#ifdef VK_EXT_discard_rectangles
void deepcopy_VkPhysicalDeviceDiscardRectanglePropertiesEXT(
    Pool* pool,
    const VkPhysicalDeviceDiscardRectanglePropertiesEXT* from,
    VkPhysicalDeviceDiscardRectanglePropertiesEXT* to);

void deepcopy_VkPipelineDiscardRectangleStateCreateInfoEXT(
    Pool* pool,
    const VkPipelineDiscardRectangleStateCreateInfoEXT* from,
    VkPipelineDiscardRectangleStateCreateInfoEXT* to);

#endif
#ifdef VK_EXT_conservative_rasterization
void deepcopy_VkPhysicalDeviceConservativeRasterizationPropertiesEXT(
    Pool* pool,
    const VkPhysicalDeviceConservativeRasterizationPropertiesEXT* from,
    VkPhysicalDeviceConservativeRasterizationPropertiesEXT* to);

void deepcopy_VkPipelineRasterizationConservativeStateCreateInfoEXT(
    Pool* pool,
    const VkPipelineRasterizationConservativeStateCreateInfoEXT* from,
    VkPipelineRasterizationConservativeStateCreateInfoEXT* to);

#endif
#ifdef VK_EXT_swapchain_colorspace
#endif
#ifdef VK_EXT_hdr_metadata
void deepcopy_VkXYColorEXT(
    Pool* pool,
    const VkXYColorEXT* from,
    VkXYColorEXT* to);

void deepcopy_VkHdrMetadataEXT(
    Pool* pool,
    const VkHdrMetadataEXT* from,
    VkHdrMetadataEXT* to);

#endif
#ifdef VK_MVK_ios_surface
void deepcopy_VkIOSSurfaceCreateInfoMVK(
    Pool* pool,
    const VkIOSSurfaceCreateInfoMVK* from,
    VkIOSSurfaceCreateInfoMVK* to);

#endif
#ifdef VK_MVK_macos_surface
void deepcopy_VkMacOSSurfaceCreateInfoMVK(
    Pool* pool,
    const VkMacOSSurfaceCreateInfoMVK* from,
    VkMacOSSurfaceCreateInfoMVK* to);

#endif
#ifdef VK_EXT_external_memory_dma_buf
#endif
#ifdef VK_EXT_queue_family_foreign
#endif
#ifdef VK_EXT_debug_utils
void deepcopy_VkDebugUtilsObjectNameInfoEXT(
    Pool* pool,
    const VkDebugUtilsObjectNameInfoEXT* from,
    VkDebugUtilsObjectNameInfoEXT* to);

void deepcopy_VkDebugUtilsObjectTagInfoEXT(
    Pool* pool,
    const VkDebugUtilsObjectTagInfoEXT* from,
    VkDebugUtilsObjectTagInfoEXT* to);

void deepcopy_VkDebugUtilsLabelEXT(
    Pool* pool,
    const VkDebugUtilsLabelEXT* from,
    VkDebugUtilsLabelEXT* to);

void deepcopy_VkDebugUtilsMessengerCallbackDataEXT(
    Pool* pool,
    const VkDebugUtilsMessengerCallbackDataEXT* from,
    VkDebugUtilsMessengerCallbackDataEXT* to);

void deepcopy_VkDebugUtilsMessengerCreateInfoEXT(
    Pool* pool,
    const VkDebugUtilsMessengerCreateInfoEXT* from,
    VkDebugUtilsMessengerCreateInfoEXT* to);

#endif
#ifdef VK_ANDROID_external_memory_android_hardware_buffer
void deepcopy_VkAndroidHardwareBufferUsageANDROID(
    Pool* pool,
    const VkAndroidHardwareBufferUsageANDROID* from,
    VkAndroidHardwareBufferUsageANDROID* to);

void deepcopy_VkAndroidHardwareBufferPropertiesANDROID(
    Pool* pool,
    const VkAndroidHardwareBufferPropertiesANDROID* from,
    VkAndroidHardwareBufferPropertiesANDROID* to);

void deepcopy_VkAndroidHardwareBufferFormatPropertiesANDROID(
    Pool* pool,
    const VkAndroidHardwareBufferFormatPropertiesANDROID* from,
    VkAndroidHardwareBufferFormatPropertiesANDROID* to);

void deepcopy_VkImportAndroidHardwareBufferInfoANDROID(
    Pool* pool,
    const VkImportAndroidHardwareBufferInfoANDROID* from,
    VkImportAndroidHardwareBufferInfoANDROID* to);

void deepcopy_VkMemoryGetAndroidHardwareBufferInfoANDROID(
    Pool* pool,
    const VkMemoryGetAndroidHardwareBufferInfoANDROID* from,
    VkMemoryGetAndroidHardwareBufferInfoANDROID* to);

void deepcopy_VkExternalFormatANDROID(
    Pool* pool,
    const VkExternalFormatANDROID* from,
    VkExternalFormatANDROID* to);

#endif
#ifdef VK_EXT_sampler_filter_minmax
void deepcopy_VkSamplerReductionModeCreateInfoEXT(
    Pool* pool,
    const VkSamplerReductionModeCreateInfoEXT* from,
    VkSamplerReductionModeCreateInfoEXT* to);

void deepcopy_VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT(
    Pool* pool,
    const VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT* from,
    VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT* to);

#endif
#ifdef VK_AMD_gpu_shader_int16
#endif
#ifdef VK_AMD_mixed_attachment_samples
#endif
#ifdef VK_AMD_shader_fragment_mask
#endif
#ifdef VK_EXT_shader_stencil_export
#endif
#ifdef VK_EXT_sample_locations
void deepcopy_VkSampleLocationEXT(
    Pool* pool,
    const VkSampleLocationEXT* from,
    VkSampleLocationEXT* to);

void deepcopy_VkSampleLocationsInfoEXT(
    Pool* pool,
    const VkSampleLocationsInfoEXT* from,
    VkSampleLocationsInfoEXT* to);

void deepcopy_VkAttachmentSampleLocationsEXT(
    Pool* pool,
    const VkAttachmentSampleLocationsEXT* from,
    VkAttachmentSampleLocationsEXT* to);

void deepcopy_VkSubpassSampleLocationsEXT(
    Pool* pool,
    const VkSubpassSampleLocationsEXT* from,
    VkSubpassSampleLocationsEXT* to);

void deepcopy_VkRenderPassSampleLocationsBeginInfoEXT(
    Pool* pool,
    const VkRenderPassSampleLocationsBeginInfoEXT* from,
    VkRenderPassSampleLocationsBeginInfoEXT* to);

void deepcopy_VkPipelineSampleLocationsStateCreateInfoEXT(
    Pool* pool,
    const VkPipelineSampleLocationsStateCreateInfoEXT* from,
    VkPipelineSampleLocationsStateCreateInfoEXT* to);

void deepcopy_VkPhysicalDeviceSampleLocationsPropertiesEXT(
    Pool* pool,
    const VkPhysicalDeviceSampleLocationsPropertiesEXT* from,
    VkPhysicalDeviceSampleLocationsPropertiesEXT* to);

void deepcopy_VkMultisamplePropertiesEXT(
    Pool* pool,
    const VkMultisamplePropertiesEXT* from,
    VkMultisamplePropertiesEXT* to);

#endif
#ifdef VK_EXT_blend_operation_advanced
void deepcopy_VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT(
    Pool* pool,
    const VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT* from,
    VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT* to);

void deepcopy_VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT(
    Pool* pool,
    const VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT* from,
    VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT* to);

void deepcopy_VkPipelineColorBlendAdvancedStateCreateInfoEXT(
    Pool* pool,
    const VkPipelineColorBlendAdvancedStateCreateInfoEXT* from,
    VkPipelineColorBlendAdvancedStateCreateInfoEXT* to);

#endif
#ifdef VK_NV_fragment_coverage_to_color
void deepcopy_VkPipelineCoverageToColorStateCreateInfoNV(
    Pool* pool,
    const VkPipelineCoverageToColorStateCreateInfoNV* from,
    VkPipelineCoverageToColorStateCreateInfoNV* to);

#endif
#ifdef VK_NV_framebuffer_mixed_samples
void deepcopy_VkPipelineCoverageModulationStateCreateInfoNV(
    Pool* pool,
    const VkPipelineCoverageModulationStateCreateInfoNV* from,
    VkPipelineCoverageModulationStateCreateInfoNV* to);

#endif
#ifdef VK_NV_fill_rectangle
#endif
#ifdef VK_EXT_post_depth_coverage
#endif
#ifdef VK_EXT_validation_cache
void deepcopy_VkValidationCacheCreateInfoEXT(
    Pool* pool,
    const VkValidationCacheCreateInfoEXT* from,
    VkValidationCacheCreateInfoEXT* to);

void deepcopy_VkShaderModuleValidationCacheCreateInfoEXT(
    Pool* pool,
    const VkShaderModuleValidationCacheCreateInfoEXT* from,
    VkShaderModuleValidationCacheCreateInfoEXT* to);

#endif
#ifdef VK_EXT_descriptor_indexing
void deepcopy_VkDescriptorSetLayoutBindingFlagsCreateInfoEXT(
    Pool* pool,
    const VkDescriptorSetLayoutBindingFlagsCreateInfoEXT* from,
    VkDescriptorSetLayoutBindingFlagsCreateInfoEXT* to);

void deepcopy_VkPhysicalDeviceDescriptorIndexingFeaturesEXT(
    Pool* pool,
    const VkPhysicalDeviceDescriptorIndexingFeaturesEXT* from,
    VkPhysicalDeviceDescriptorIndexingFeaturesEXT* to);

void deepcopy_VkPhysicalDeviceDescriptorIndexingPropertiesEXT(
    Pool* pool,
    const VkPhysicalDeviceDescriptorIndexingPropertiesEXT* from,
    VkPhysicalDeviceDescriptorIndexingPropertiesEXT* to);

void deepcopy_VkDescriptorSetVariableDescriptorCountAllocateInfoEXT(
    Pool* pool,
    const VkDescriptorSetVariableDescriptorCountAllocateInfoEXT* from,
    VkDescriptorSetVariableDescriptorCountAllocateInfoEXT* to);

void deepcopy_VkDescriptorSetVariableDescriptorCountLayoutSupportEXT(
    Pool* pool,
    const VkDescriptorSetVariableDescriptorCountLayoutSupportEXT* from,
    VkDescriptorSetVariableDescriptorCountLayoutSupportEXT* to);

#endif
#ifdef VK_EXT_shader_viewport_index_layer
#endif
#ifdef VK_EXT_global_priority
void deepcopy_VkDeviceQueueGlobalPriorityCreateInfoEXT(
    Pool* pool,
    const VkDeviceQueueGlobalPriorityCreateInfoEXT* from,
    VkDeviceQueueGlobalPriorityCreateInfoEXT* to);

#endif
#ifdef VK_EXT_external_memory_host
void deepcopy_VkImportMemoryHostPointerInfoEXT(
    Pool* pool,
    const VkImportMemoryHostPointerInfoEXT* from,
    VkImportMemoryHostPointerInfoEXT* to);

void deepcopy_VkMemoryHostPointerPropertiesEXT(
    Pool* pool,
    const VkMemoryHostPointerPropertiesEXT* from,
    VkMemoryHostPointerPropertiesEXT* to);

void deepcopy_VkPhysicalDeviceExternalMemoryHostPropertiesEXT(
    Pool* pool,
    const VkPhysicalDeviceExternalMemoryHostPropertiesEXT* from,
    VkPhysicalDeviceExternalMemoryHostPropertiesEXT* to);

#endif
#ifdef VK_AMD_buffer_marker
#endif
#ifdef VK_AMD_shader_core_properties
void deepcopy_VkPhysicalDeviceShaderCorePropertiesAMD(
    Pool* pool,
    const VkPhysicalDeviceShaderCorePropertiesAMD* from,
    VkPhysicalDeviceShaderCorePropertiesAMD* to);

#endif
#ifdef VK_EXT_vertex_attribute_divisor
void deepcopy_VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT(
    Pool* pool,
    const VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT* from,
    VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT* to);

void deepcopy_VkVertexInputBindingDivisorDescriptionEXT(
    Pool* pool,
    const VkVertexInputBindingDivisorDescriptionEXT* from,
    VkVertexInputBindingDivisorDescriptionEXT* to);

void deepcopy_VkPipelineVertexInputDivisorStateCreateInfoEXT(
    Pool* pool,
    const VkPipelineVertexInputDivisorStateCreateInfoEXT* from,
    VkPipelineVertexInputDivisorStateCreateInfoEXT* to);

#endif
#ifdef VK_NV_shader_subgroup_partitioned
#endif
#ifdef VK_NV_device_diagnostic_checkpoints
void deepcopy_VkQueueFamilyCheckpointPropertiesNV(
    Pool* pool,
    const VkQueueFamilyCheckpointPropertiesNV* from,
    VkQueueFamilyCheckpointPropertiesNV* to);

void deepcopy_VkCheckpointDataNV(
    Pool* pool,
    const VkCheckpointDataNV* from,
    VkCheckpointDataNV* to);

#endif
#ifdef VK_GOOGLE_address_space
#endif
#ifdef VK_GOOGLE_color_buffer
void deepcopy_VkImportColorBufferGOOGLE(
    Pool* pool,
    const VkImportColorBufferGOOGLE* from,
    VkImportColorBufferGOOGLE* to);

void deepcopy_VkImportPhysicalAddressGOOGLE(
    Pool* pool,
    const VkImportPhysicalAddressGOOGLE* from,
    VkImportPhysicalAddressGOOGLE* to);

#endif
#ifdef VK_GOOGLE_sized_descriptor_update_template
#endif
#ifdef VK_GOOGLE_async_command_buffers
#endif
#ifdef VK_GOOGLE_create_resources_with_requirements
#endif
#ifdef VK_GOOGLE_address_space_info
#endif
#ifdef VK_GOOGLE_free_memory_sync
#endif

} // namespace goldfish_vk

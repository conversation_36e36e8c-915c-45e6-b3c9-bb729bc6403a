//
// Copyright (C) 2016 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

NANOTOOL_VERSION = "1.2.0"

cc_binary {
    name: "nanotool",

    srcs: [
        "androidcontexthub.cpp",
        "apptohostevent.cpp",
        "calibrationfile.cpp",
        "contexthub.cpp",
        "log.cpp",
        "logevent.cpp",
        "nanomessage.cpp",
        "nanotool.cpp",
        "resetreasonevent.cpp",
        "sensorevent.cpp",
    ],

    // JSON file handling from chinook
    static_libs: ["libhubutilcommon"],
    shared_libs: [
        "liblog",
        "libstagefright_foundation",
        "libutils",
    ],

    cpp_std: "c++11",
    cflags: [
        "-Wall",
        "-Werror",
        "-Wextra",

        "-DNANOTOOL_VERSION_STR=\"version " + NANOTOOL_VERSION + "\"",
    ],

    owner: "google",
    vendor: true,
}

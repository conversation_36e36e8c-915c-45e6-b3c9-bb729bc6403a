shared_library("libvulkan_goldfish") {
  sources = [
    "android-emu/android/base/AlignedBuf.cpp",
    "android-emu/android/base/AlignedBuf.h",
    "android-emu/android/base/Pool.cpp",
    "android-emu/android/base/Pool.h",
    "android-emu/android/base/ring_buffer.c",
    "android-emu/android/base/AndroidSubAllocator.cpp",
    "android-emu/android/base/AndroidSubAllocator.h",
    "android-emu/android/base/files/MemStream.cpp",
    "android-emu/android/base/files/MemStream.h",
    "android-emu/android/base/files/Stream.cpp",
    "android-emu/android/base/files/Stream.h",
    "android-emu/android/base/files/StreamSerializing.cpp",
    "android-emu/android/base/files/StreamSerializing.h",
    "android-emu/android/base/synchronization/AndroidConditionVariable.h",
    "android-emu/android/base/synchronization/AndroidLock.h",
    "android-emu/android/base/synchronization/AndroidMessageChannel.h",
    "android-emu/android/base/synchronization/AndroidMessageChannel.cpp",
    "android-emu/android/base/threads/AndroidFunctorThread.h",
    "android-emu/android/base/threads/AndroidThread.h",
    "android-emu/android/base/threads/AndroidThreadStore.h",
    "android-emu/android/base/threads/AndroidThreadTypes.h",
    "android-emu/android/base/threads/AndroidWorkPool.h",
    "android-emu/android/base/threads/AndroidFunctorThread.cpp",
    "android-emu/android/base/threads/AndroidThreadStore.h",
    "android-emu/android/base/threads/AndroidThread_pthread.cpp",
    "android-emu/android/base/threads/AndroidWorkPool.cpp",
    "android-emu/android/base/Tracing.cpp",
    "android-emu/android/base/Tracing.h",
    "shared/GoldfishAddressSpace/goldfish_address_space.cpp",
    "shared/GoldfishAddressSpace/goldfish_address_space.h",
    "shared/OpenglCodecCommon/ChecksumCalculator.cpp",
    "shared/OpenglCodecCommon/ChecksumCalculator.h",
    "shared/OpenglCodecCommon/glUtils.cpp",
    "shared/OpenglCodecCommon/glUtils.h",
    "shared/OpenglCodecCommon/goldfish_dma.cpp",
    "shared/OpenglCodecCommon/goldfish_dma.h",
    "shared/gralloc_cb/include/gralloc_cb_bp.h",
    "shared/qemupipe/include/qemu_pipe_bp.h",
    "shared/qemupipe/include-types/qemu_pipe_types_bp.h",
    "shared/qemupipe/qemu_pipe_common.cpp",
    "shared/qemupipe/qemu_pipe_guest.cpp",
    "system/OpenglSystemCommon/AddressSpaceStream.cpp",
    "system/OpenglSystemCommon/HostConnection.cpp",
    "system/OpenglSystemCommon/HostConnection.h",
    "system/OpenglSystemCommon/ProcessPipe.cpp",
    "system/OpenglSystemCommon/ProcessPipe.h",
    "system/OpenglSystemCommon/QemuPipeStream.cpp",
    "system/OpenglSystemCommon/QemuPipeStream.h",
    "system/OpenglSystemCommon/ThreadInfo.cpp",
    "system/OpenglSystemCommon/ThreadInfo.h",
    "system/renderControl_enc/renderControl_enc.cpp",
    "system/renderControl_enc/renderControl_enc.h",
    "system/vulkan/func_table.cpp",
    "system/vulkan/func_table.h",
    "system/vulkan/goldfish_vulkan.cpp",
    "system/vulkan_enc/HostVisibleMemoryVirtualization.cpp",
    "system/vulkan_enc/HostVisibleMemoryVirtualization.h",
    "system/vulkan_enc/ResourceTracker.cpp",
    "system/vulkan_enc/ResourceTracker.h",
    "system/vulkan_enc/Resources.cpp",
    "system/vulkan_enc/Resources.h",
    "system/vulkan_enc/Validation.cpp",
    "system/vulkan_enc/Validation.h",
    "system/vulkan_enc/VkEncoder.cpp",
    "system/vulkan_enc/VkEncoder.h",
    "system/vulkan_enc/VulkanHandleMapping.cpp",
    "system/vulkan_enc/VulkanHandleMapping.h",
    "system/vulkan_enc/VulkanStreamGuest.cpp",
    "system/vulkan_enc/VulkanStreamGuest.h",
    "system/vulkan_enc/goldfish_vk_deepcopy_guest.cpp",
    "system/vulkan_enc/goldfish_vk_deepcopy_guest.h",
    "system/vulkan_enc/goldfish_vk_extension_structs_guest.cpp",
    "system/vulkan_enc/goldfish_vk_extension_structs_guest.h",
    "system/vulkan_enc/goldfish_vk_marshaling_guest.cpp",
    "system/vulkan_enc/goldfish_vk_marshaling_guest.h",
    "system/vulkan_enc/goldfish_vk_transform_guest.cpp",
    "system/vulkan_enc/goldfish_vk_transform_guest.h",
  ]

  include_dirs = [
    "android-emu",
    "host/include/libOpenglRender",
    "shared/GoldfishAddressSpace/include",
    "shared/OpenglCodecCommon",
    "shared/gralloc_cb/include",
    "shared/qemupipe/include",
    "shared/qemupipe/include-types",
    "system/OpenglSystemCommon",
    "system/renderControl_enc",
    "system/vulkan_enc",
    "system/include",
  ]

  defines = [
    "LOG_TAG=\"goldfish_vulkan\"",
    "GOLDFISH_VULKAN",
    "GOLDFISH_NO_GL",
    "VK_USE_PLATFORM_FUCHSIA",
    "PLATFORM_SDK_VERSION=1",
    "PAGE_SIZE=4096",
  ]

  cflags_c = [
    "-Wno-missing-field-initializers",
    "-Wno-newline-eof",
    "-Wno-unused-function",
    "-Wno-unused-value",
    "-Wno-unused-variable",
  ]

  cflags_cc = [
    "-Wno-missing-field-initializers",
    "-Wno-newline-eof",
    "-Wno-unused-function",
    "-Wno-unused-value",
    "-Wno-unused-variable",
  ]

  ldflags = [ "-static-libstdc++" ]

  if (target_os == "fuchsia") {
    sources -= [ "system/OpenglSystemCommon/QemuPipeStream.cpp" ]
    sources += [
      "fuchsia/fuchsia_stdio.cc",
      "fuchsia/port.cc",
      "fuchsia/service_connector.cc",
      "system/OpenglSystemCommon/QemuPipeStreamFuchsia.cpp",
    ]

    include_dirs += [
      "fuchsia/include",
      "//third_party/Vulkan-Headers/include"
    ]

    libs = [
      "zircon"
    ]

    deps = [
      "//sdk/fidl/fuchsia.hardware.goldfish",
      "//sdk/fidl/fuchsia.logger:fuchsia.logger_llcpp",
      "//sdk/fidl/fuchsia.sysmem",
      "//zircon/public/lib/zx",
      "//zircon/public/lib/zxio",
      "//zircon/system/ulib/syslog:syslog-static",
      "//zircon/system/ulib/trace:trace-with-static-engine",
    ]

    defines += [
      "QEMU_PIPE_PATH=\"/dev/class/goldfish-pipe/000\"",
      "GOLDFISH_ADDRESS_SPACE_DEVICE_NAME=\"/dev/class/goldfish-address-space/000\"",
    ]
  }
}

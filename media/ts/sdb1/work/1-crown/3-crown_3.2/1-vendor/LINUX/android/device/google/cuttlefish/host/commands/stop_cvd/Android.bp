//
// Copyright (C) 2018 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

cc_binary_host {
    name: "stop_cvd",
    srcs: [
        "main.cc",
    ],
    shared_libs: [
        "libbase",
        "libcuttlefish_fs",
        "libcuttlefish_utils",
    ],
    static_libs: [
        "libcuttlefish_host_config",
        "libcuttlefish_vm_manager",
        "libjsoncpp",
        "libgflags",
        "libxml2",
    ],
    defaults: ["cuttlefish_host_only", "cuttlefish_libicuuc"],
}

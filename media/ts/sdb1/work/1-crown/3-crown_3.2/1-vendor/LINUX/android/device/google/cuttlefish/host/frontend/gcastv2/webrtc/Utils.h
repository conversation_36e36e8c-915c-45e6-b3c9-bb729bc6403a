/*
 * Copyright (C) 2019 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include <cstdint>
#include <string>
#include <vector>

std::vector<std::string> SplitString(const std::string &s, char c);

std::vector<std::string> SplitString(
        const std::string &s, const std::string &separator);

bool StartsWith(const std::string &s, const std::string &prefix);

void SET_U16(void *_dst, uint16_t x);
void SET_U32(void *_dst, uint32_t x);

uint32_t computeCrc32(const void *_data, size_t size);

std::string StringPrintf(const char *format, ...);

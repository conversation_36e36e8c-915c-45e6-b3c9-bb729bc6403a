//
// Copyright (C) 2019 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

cc_library_host_shared {
    name: "cdisk_spec",
    srcs: [
        "cdisk_spec.proto",
    ],
    proto: {
        type: "full",
        export_proto_headers: true,
        include_dirs: [
            "external/protobuf/src",
        ],
    },
    defaults: ["cuttlefish_host_only"],
}

cc_binary_host {
    name: "assemble_cvd",
    srcs: [
        "assemble_cvd.cc",
        "boot_image_unpacker.cc",
        "data_image.cc",
        "flags.cc",
        "image_aggregator.cc",
        "misc_info.cc",
        "super_image_mixer.cc",
    ],
    header_libs: [
        "bootimg_headers",
    ],
    shared_libs: [
        "cdisk_spec",
        "libcuttlefish_fs",
        "libcuttlefish_utils",
        "libbase",
        "libnl",
        "libprotobuf-cpp-full",
        "libziparchive",
        "libz",
    ],
    static_libs: [
        "libsparse",
        "libcuttlefish_host_config",
        "libcuttlefish_vm_manager",
        "libgflags",
        "libxml2",
        "libjsoncpp",
    ],
    defaults: ["cuttlefish_host_only", "cuttlefish_libicuuc"],
}

python_binary_host {
    name: "cf_bpttool",
    srcs: [ "cf_bpttool.py" ],
    defaults: ["py2_only"],
}

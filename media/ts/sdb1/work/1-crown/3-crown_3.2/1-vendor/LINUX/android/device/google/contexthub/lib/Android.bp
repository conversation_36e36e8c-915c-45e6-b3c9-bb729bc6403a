//
// Copyright (C) 2016 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

cc_library_headers {
    name: "libnanohub_common_headers",
    vendor_available: true,
    export_include_dirs: ["include"],
}

cc_library_static {
    name: "libnanohub_common",
    host_supported: true,

    srcs: ["nanohub/nanoapp.c"],

    export_include_dirs: ["include"],

    target: {
        host: {
            cflags: [
                "-DHOST_BUILD",
                "-DRSA_SUPPORT_PRIV_OP_BIGRAM",
            ],

            srcs: [
                "nanohub/aes.c",
                "nanohub/rsa.c",
                "nanohub/sha2.c",

                "nanohub/softcrc.c",
            ],
        },
    },
}

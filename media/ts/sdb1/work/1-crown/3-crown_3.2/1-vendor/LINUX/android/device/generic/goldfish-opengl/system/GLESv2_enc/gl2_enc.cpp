// Generated Code - DO NOT EDIT !!
// generated by 'emugen'


#include <string.h>
#include "gl2_opcodes.h"

#include "gl2_enc.h"


#include <vector>

#include <stdio.h>

namespace {

void enc_unsupported()
{
	ALOGE("Function is unsupported\n");
}

void glActiveTexture_enc(void *self , GLenum texture)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glActiveTexture;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &texture, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glAttachShader_enc(void *self , GLuint program, GLuint shader)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glAttachShader;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &shader, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBindAttribLocation_enc(void *self , GLuint program, GLuint index, const GLchar* name)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_name =  (strlen(name) + 1);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_name + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBindAttribLocation;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &index, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_name; ptr += 4;
	memcpy(ptr, name, __size_name);ptr += __size_name;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBindBuffer_enc(void *self , GLenum target, GLuint buffer)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBindBuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &buffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBindFramebuffer_enc(void *self , GLenum target, GLuint framebuffer)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBindFramebuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &framebuffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBindRenderbuffer_enc(void *self , GLenum target, GLuint renderbuffer)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBindRenderbuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &renderbuffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBindTexture_enc(void *self , GLenum target, GLuint texture)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBindTexture;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &texture, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBlendColor_enc(void *self , GLclampf red, GLclampf green, GLclampf blue, GLclampf alpha)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBlendColor;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &red, 4); ptr += 4;
		memcpy(ptr, &green, 4); ptr += 4;
		memcpy(ptr, &blue, 4); ptr += 4;
		memcpy(ptr, &alpha, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBlendEquation_enc(void *self , GLenum mode)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBlendEquation;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBlendEquationSeparate_enc(void *self , GLenum modeRGB, GLenum modeAlpha)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBlendEquationSeparate;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &modeRGB, 4); ptr += 4;
		memcpy(ptr, &modeAlpha, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBlendFunc_enc(void *self , GLenum sfactor, GLenum dfactor)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBlendFunc;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &sfactor, 4); ptr += 4;
		memcpy(ptr, &dfactor, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBlendFuncSeparate_enc(void *self , GLenum srcRGB, GLenum dstRGB, GLenum srcAlpha, GLenum dstAlpha)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBlendFuncSeparate;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &srcRGB, 4); ptr += 4;
		memcpy(ptr, &dstRGB, 4); ptr += 4;
		memcpy(ptr, &srcAlpha, 4); ptr += 4;
		memcpy(ptr, &dstAlpha, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBufferData_enc(void *self , GLenum target, GLsizeiptr size, const GLvoid* data, GLenum usage)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data = ((data != NULL) ?  size : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_data + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(8 + 4 + 4);
	ptr = buf;
	int tmp = OP_glBufferData;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &size, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	stream->flush();
	stream->writeFully(&__size_data,4);
	if (useChecksum) checksumCalculator->addBuffer(&__size_data,4);
	if (data != NULL) {
		stream->writeFully(data, __size_data);
		if (useChecksum) checksumCalculator->addBuffer(data, __size_data);
	}
	buf = stream->alloc(4);
	ptr = buf;
		memcpy(ptr, &usage, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	buf = stream->alloc(checksumSize);
	if (useChecksum) checksumCalculator->writeChecksum(buf, checksumSize);

}

void glBufferSubData_enc(void *self , GLenum target, GLintptr offset, GLsizeiptr size, const GLvoid* data)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data = ((data != NULL) ?  size : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_data + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(8 + 4 + 4 + 4);
	ptr = buf;
	int tmp = OP_glBufferSubData;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;
		memcpy(ptr, &size, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	stream->flush();
	stream->writeFully(&__size_data,4);
	if (useChecksum) checksumCalculator->addBuffer(&__size_data,4);
	if (data != NULL) {
		stream->writeFully(data, __size_data);
		if (useChecksum) checksumCalculator->addBuffer(data, __size_data);
	}
	buf = stream->alloc(checksumSize);
	if (useChecksum) checksumCalculator->writeChecksum(buf, checksumSize);

}

GLenum glCheckFramebufferStatus_enc(void *self , GLenum target)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCheckFramebufferStatus;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLenum retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glCheckFramebufferStatus: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glClear_enc(void *self , GLbitfield mask)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClear;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mask, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glClearColor_enc(void *self , GLclampf red, GLclampf green, GLclampf blue, GLclampf alpha)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClearColor;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &red, 4); ptr += 4;
		memcpy(ptr, &green, 4); ptr += 4;
		memcpy(ptr, &blue, 4); ptr += 4;
		memcpy(ptr, &alpha, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glClearDepthf_enc(void *self , GLclampf depth)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClearDepthf;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &depth, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glClearStencil_enc(void *self , GLint s)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClearStencil;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &s, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glColorMask_enc(void *self , GLboolean red, GLboolean green, GLboolean blue, GLboolean alpha)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 1 + 1 + 1 + 1;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glColorMask;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &red, 1); ptr += 1;
		memcpy(ptr, &green, 1); ptr += 1;
		memcpy(ptr, &blue, 1); ptr += 1;
		memcpy(ptr, &alpha, 1); ptr += 1;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glCompileShader_enc(void *self , GLuint shader)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCompileShader;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &shader, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glCompressedTexImage2D_enc(void *self , GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLint border, GLsizei imageSize, const GLvoid* data)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data = ((data != NULL) ?  imageSize : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + __size_data + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(8 + 4 + 4 + 4 + 4 + 4 + 4 + 4);
	ptr = buf;
	int tmp = OP_glCompressedTexImage2D;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &internalformat, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &border, 4); ptr += 4;
		memcpy(ptr, &imageSize, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	stream->flush();
	stream->writeFully(&__size_data,4);
	if (useChecksum) checksumCalculator->addBuffer(&__size_data,4);
	if (data != NULL) {
		stream->writeFully(data, __size_data);
		if (useChecksum) checksumCalculator->addBuffer(data, __size_data);
	}
	buf = stream->alloc(checksumSize);
	if (useChecksum) checksumCalculator->writeChecksum(buf, checksumSize);

}

void glCompressedTexSubImage2D_enc(void *self , GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLsizei imageSize, const GLvoid* data)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data = ((data != NULL) ?  imageSize : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + __size_data + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4);
	ptr = buf;
	int tmp = OP_glCompressedTexSubImage2D;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &xoffset, 4); ptr += 4;
		memcpy(ptr, &yoffset, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &imageSize, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	stream->flush();
	stream->writeFully(&__size_data,4);
	if (useChecksum) checksumCalculator->addBuffer(&__size_data,4);
	if (data != NULL) {
		stream->writeFully(data, __size_data);
		if (useChecksum) checksumCalculator->addBuffer(data, __size_data);
	}
	buf = stream->alloc(checksumSize);
	if (useChecksum) checksumCalculator->writeChecksum(buf, checksumSize);

}

void glCopyTexImage2D_enc(void *self , GLenum target, GLint level, GLenum internalformat, GLint x, GLint y, GLsizei width, GLsizei height, GLint border)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCopyTexImage2D;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &internalformat, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &border, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glCopyTexSubImage2D_enc(void *self , GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint x, GLint y, GLsizei width, GLsizei height)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCopyTexSubImage2D;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &xoffset, 4); ptr += 4;
		memcpy(ptr, &yoffset, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

GLuint glCreateProgram_enc(void *self )
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCreateProgram;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;


	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLuint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glCreateProgram: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

GLuint glCreateShader_enc(void *self , GLenum type)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCreateShader;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &type, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLuint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glCreateShader: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glCullFace_enc(void *self , GLenum mode)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCullFace;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDeleteBuffers_enc(void *self , GLsizei n, const GLuint* buffers)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_buffers =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_buffers + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDeleteBuffers;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_buffers; ptr += 4;
	memcpy(ptr, buffers, __size_buffers);ptr += __size_buffers;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDeleteFramebuffers_enc(void *self , GLsizei n, const GLuint* framebuffers)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_framebuffers =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_framebuffers + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDeleteFramebuffers;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_framebuffers; ptr += 4;
	memcpy(ptr, framebuffers, __size_framebuffers);ptr += __size_framebuffers;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDeleteProgram_enc(void *self , GLuint program)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDeleteProgram;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDeleteRenderbuffers_enc(void *self , GLsizei n, const GLuint* renderbuffers)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_renderbuffers =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_renderbuffers + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDeleteRenderbuffers;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_renderbuffers; ptr += 4;
	memcpy(ptr, renderbuffers, __size_renderbuffers);ptr += __size_renderbuffers;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDeleteShader_enc(void *self , GLuint shader)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDeleteShader;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &shader, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDeleteTextures_enc(void *self , GLsizei n, const GLuint* textures)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_textures =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_textures + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDeleteTextures;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_textures; ptr += 4;
	memcpy(ptr, textures, __size_textures);ptr += __size_textures;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDepthFunc_enc(void *self , GLenum func)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDepthFunc;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &func, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDepthMask_enc(void *self , GLboolean flag)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 1;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDepthMask;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &flag, 1); ptr += 1;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDepthRangef_enc(void *self , GLclampf zNear, GLclampf zFar)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDepthRangef;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &zNear, 4); ptr += 4;
		memcpy(ptr, &zFar, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDetachShader_enc(void *self , GLuint program, GLuint shader)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDetachShader;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &shader, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDisable_enc(void *self , GLenum cap)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDisable;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &cap, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDisableVertexAttribArray_enc(void *self , GLuint index)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDisableVertexAttribArray;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &index, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawArrays_enc(void *self , GLenum mode, GLint first, GLsizei count)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawArrays;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;
		memcpy(ptr, &first, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glEnable_enc(void *self , GLenum cap)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glEnable;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &cap, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glEnableVertexAttribArray_enc(void *self , GLuint index)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glEnableVertexAttribArray;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &index, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFinish_enc(void *self )
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFinish;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;


	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFlush_enc(void *self )
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFlush;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;


	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFramebufferRenderbuffer_enc(void *self , GLenum target, GLenum attachment, GLenum renderbuffertarget, GLuint renderbuffer)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFramebufferRenderbuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &attachment, 4); ptr += 4;
		memcpy(ptr, &renderbuffertarget, 4); ptr += 4;
		memcpy(ptr, &renderbuffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFramebufferTexture2D_enc(void *self , GLenum target, GLenum attachment, GLenum textarget, GLuint texture, GLint level)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFramebufferTexture2D;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &attachment, 4); ptr += 4;
		memcpy(ptr, &textarget, 4); ptr += 4;
		memcpy(ptr, &texture, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFrontFace_enc(void *self , GLenum mode)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFrontFace;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGenBuffers_enc(void *self , GLsizei n, GLuint* buffers)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_buffers =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGenBuffers;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_buffers; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(buffers, __size_buffers);
	if (useChecksum) checksumCalculator->addBuffer(buffers, __size_buffers);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGenBuffers: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGenerateMipmap_enc(void *self , GLenum target)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGenerateMipmap;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGenFramebuffers_enc(void *self , GLsizei n, GLuint* framebuffers)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_framebuffers =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGenFramebuffers;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_framebuffers; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(framebuffers, __size_framebuffers);
	if (useChecksum) checksumCalculator->addBuffer(framebuffers, __size_framebuffers);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGenFramebuffers: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGenRenderbuffers_enc(void *self , GLsizei n, GLuint* renderbuffers)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_renderbuffers =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGenRenderbuffers;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_renderbuffers; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(renderbuffers, __size_renderbuffers);
	if (useChecksum) checksumCalculator->addBuffer(renderbuffers, __size_renderbuffers);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGenRenderbuffers: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGenTextures_enc(void *self , GLsizei n, GLuint* textures)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_textures =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGenTextures;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_textures; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(textures, __size_textures);
	if (useChecksum) checksumCalculator->addBuffer(textures, __size_textures);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGenTextures: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetActiveAttrib_enc(void *self , GLuint program, GLuint index, GLsizei bufsize, GLsizei* length, GLint* size, GLenum* type, GLchar* name)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_length = ((length != NULL) ?  (sizeof(GLsizei)) : 0);
	const unsigned int __size_size = ((size != NULL) ?  (sizeof(GLint)) : 0);
	const unsigned int __size_type = ((type != NULL) ?  (sizeof(GLenum)) : 0);
	const unsigned int __size_name = ((name != NULL) ?  bufsize : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 0 + 0 + 0 + 0 + 4*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetActiveAttrib;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &index, 4); ptr += 4;
		memcpy(ptr, &bufsize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_length; ptr += 4;
	*(unsigned int *)(ptr) = __size_size; ptr += 4;
	*(unsigned int *)(ptr) = __size_type; ptr += 4;
	*(unsigned int *)(ptr) = __size_name; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	if (length != NULL) {
		stream->readback(length, __size_length);
		if (useChecksum) checksumCalculator->addBuffer(length, __size_length);
	}
	if (size != NULL) {
		stream->readback(size, __size_size);
		if (useChecksum) checksumCalculator->addBuffer(size, __size_size);
	}
	if (type != NULL) {
		stream->readback(type, __size_type);
		if (useChecksum) checksumCalculator->addBuffer(type, __size_type);
	}
	if (name != NULL) {
		stream->readback(name, __size_name);
		if (useChecksum) checksumCalculator->addBuffer(name, __size_name);
	}
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetActiveAttrib: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetActiveUniform_enc(void *self , GLuint program, GLuint index, GLsizei bufsize, GLsizei* length, GLint* size, GLenum* type, GLchar* name)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_length = ((length != NULL) ?  (sizeof(GLsizei)) : 0);
	const unsigned int __size_size = ((size != NULL) ?  (sizeof(GLint)) : 0);
	const unsigned int __size_type = ((type != NULL) ?  (sizeof(GLenum)) : 0);
	const unsigned int __size_name = ((name != NULL) ?  bufsize : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 0 + 0 + 0 + 0 + 4*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetActiveUniform;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &index, 4); ptr += 4;
		memcpy(ptr, &bufsize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_length; ptr += 4;
	*(unsigned int *)(ptr) = __size_size; ptr += 4;
	*(unsigned int *)(ptr) = __size_type; ptr += 4;
	*(unsigned int *)(ptr) = __size_name; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	if (length != NULL) {
		stream->readback(length, __size_length);
		if (useChecksum) checksumCalculator->addBuffer(length, __size_length);
	}
	if (size != NULL) {
		stream->readback(size, __size_size);
		if (useChecksum) checksumCalculator->addBuffer(size, __size_size);
	}
	if (type != NULL) {
		stream->readback(type, __size_type);
		if (useChecksum) checksumCalculator->addBuffer(type, __size_type);
	}
	if (name != NULL) {
		stream->readback(name, __size_name);
		if (useChecksum) checksumCalculator->addBuffer(name, __size_name);
	}
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetActiveUniform: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetAttachedShaders_enc(void *self , GLuint program, GLsizei maxcount, GLsizei* count, GLuint* shaders)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_count = ((count != NULL) ?  (sizeof(GLsizei)) : 0);
	const unsigned int __size_shaders =  (maxcount*sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetAttachedShaders;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &maxcount, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_count; ptr += 4;
	*(unsigned int *)(ptr) = __size_shaders; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	if (count != NULL) {
		stream->readback(count, __size_count);
		if (useChecksum) checksumCalculator->addBuffer(count, __size_count);
	}
	stream->readback(shaders, __size_shaders);
	if (useChecksum) checksumCalculator->addBuffer(shaders, __size_shaders);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetAttachedShaders: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

int glGetAttribLocation_enc(void *self , GLuint program, const GLchar* name)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_name =  (strlen(name) + 1);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_name + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetAttribLocation;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_name; ptr += 4;
	memcpy(ptr, name, __size_name);ptr += __size_name;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	int retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetAttribLocation: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glGetBooleanv_enc(void *self , GLenum pname, GLboolean* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLboolean));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetBooleanv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetBooleanv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetBufferParameteriv_enc(void *self , GLenum target, GLenum pname, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetBufferParameteriv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetBufferParameteriv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

GLenum glGetError_enc(void *self )
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetError;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;


	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLenum retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetError: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glGetFloatv_enc(void *self , GLenum pname, GLfloat* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetFloatv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetFloatv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetFramebufferAttachmentParameteriv_enc(void *self , GLenum target, GLenum attachment, GLenum pname, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetFramebufferAttachmentParameteriv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &attachment, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetFramebufferAttachmentParameteriv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetIntegerv_enc(void *self , GLenum pname, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetIntegerv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetIntegerv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetProgramiv_enc(void *self , GLuint program, GLenum pname, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetProgramiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetProgramiv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetProgramInfoLog_enc(void *self , GLuint program, GLsizei bufsize, GLsizei* length, GLchar* infolog)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_length = ((length != NULL) ?  sizeof(GLsizei) : 0);
	const unsigned int __size_infolog =  bufsize;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetProgramInfoLog;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &bufsize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_length; ptr += 4;
	*(unsigned int *)(ptr) = __size_infolog; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	if (length != NULL) {
		stream->readback(length, __size_length);
		if (useChecksum) checksumCalculator->addBuffer(length, __size_length);
	}
	stream->readback(infolog, __size_infolog);
	if (useChecksum) checksumCalculator->addBuffer(infolog, __size_infolog);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetProgramInfoLog: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetRenderbufferParameteriv_enc(void *self , GLenum target, GLenum pname, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  sizeof(GLint);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetRenderbufferParameteriv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetRenderbufferParameteriv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetShaderiv_enc(void *self , GLuint shader, GLenum pname, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  sizeof(GLint);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetShaderiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &shader, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetShaderiv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetShaderInfoLog_enc(void *self , GLuint shader, GLsizei bufsize, GLsizei* length, GLchar* infolog)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_length = ((length != NULL) ?  (sizeof(GLsizei)) : 0);
	const unsigned int __size_infolog =  bufsize;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetShaderInfoLog;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &shader, 4); ptr += 4;
		memcpy(ptr, &bufsize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_length; ptr += 4;
	*(unsigned int *)(ptr) = __size_infolog; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	if (length != NULL) {
		stream->readback(length, __size_length);
		if (useChecksum) checksumCalculator->addBuffer(length, __size_length);
	}
	stream->readback(infolog, __size_infolog);
	if (useChecksum) checksumCalculator->addBuffer(infolog, __size_infolog);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetShaderInfoLog: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetShaderPrecisionFormat_enc(void *self , GLenum shadertype, GLenum precisiontype, GLint* range, GLint* precision)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_range =  (2 * sizeof(GLint));
	const unsigned int __size_precision =  (sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetShaderPrecisionFormat;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &shadertype, 4); ptr += 4;
		memcpy(ptr, &precisiontype, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_range; ptr += 4;
	*(unsigned int *)(ptr) = __size_precision; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(range, __size_range);
	if (useChecksum) checksumCalculator->addBuffer(range, __size_range);
	stream->readback(precision, __size_precision);
	if (useChecksum) checksumCalculator->addBuffer(precision, __size_precision);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetShaderPrecisionFormat: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetShaderSource_enc(void *self , GLuint shader, GLsizei bufsize, GLsizei* length, GLchar* source)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_length = ((length != NULL) ?  (sizeof(GLsizei)) : 0);
	const unsigned int __size_source =  bufsize;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetShaderSource;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &shader, 4); ptr += 4;
		memcpy(ptr, &bufsize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_length; ptr += 4;
	*(unsigned int *)(ptr) = __size_source; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	if (length != NULL) {
		stream->readback(length, __size_length);
		if (useChecksum) checksumCalculator->addBuffer(length, __size_length);
	}
	stream->readback(source, __size_source);
	if (useChecksum) checksumCalculator->addBuffer(source, __size_source);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetShaderSource: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetTexParameterfv_enc(void *self , GLenum target, GLenum pname, GLfloat* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetTexParameterfv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetTexParameterfv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetTexParameteriv_enc(void *self , GLenum target, GLenum pname, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetTexParameteriv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetTexParameteriv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetUniformfv_enc(void *self , GLuint program, GLint location, GLfloat* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  glSizeof(glesv2_enc::uniformType(self, program, location));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetUniformfv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetUniformfv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetUniformiv_enc(void *self , GLuint program, GLint location, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  glSizeof(glesv2_enc::uniformType(self, program, location));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetUniformiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetUniformiv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

int glGetUniformLocation_enc(void *self , GLuint program, const GLchar* name)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_name =  (strlen(name) + 1);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_name + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetUniformLocation;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_name; ptr += 4;
	memcpy(ptr, name, __size_name);ptr += __size_name;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	int retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetUniformLocation: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glGetVertexAttribfv_enc(void *self , GLuint index, GLenum pname, GLfloat* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetVertexAttribfv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &index, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetVertexAttribfv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetVertexAttribiv_enc(void *self , GLuint index, GLenum pname, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetVertexAttribiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &index, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetVertexAttribiv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glHint_enc(void *self , GLenum target, GLenum mode)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glHint;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &mode, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

GLboolean glIsBuffer_enc(void *self , GLuint buffer)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsBuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &buffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsBuffer: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

GLboolean glIsEnabled_enc(void *self , GLenum cap)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsEnabled;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &cap, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsEnabled: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

GLboolean glIsFramebuffer_enc(void *self , GLuint framebuffer)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsFramebuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &framebuffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsFramebuffer: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

GLboolean glIsProgram_enc(void *self , GLuint program)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsProgram;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsProgram: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

GLboolean glIsRenderbuffer_enc(void *self , GLuint renderbuffer)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsRenderbuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &renderbuffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsRenderbuffer: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

GLboolean glIsShader_enc(void *self , GLuint shader)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsShader;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &shader, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsShader: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

GLboolean glIsTexture_enc(void *self , GLuint texture)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsTexture;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &texture, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsTexture: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glLineWidth_enc(void *self , GLfloat width)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLineWidth;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &width, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glLinkProgram_enc(void *self , GLuint program)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glLinkProgram;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glPixelStorei_enc(void *self , GLenum pname, GLint param)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glPixelStorei;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glPolygonOffset_enc(void *self , GLfloat factor, GLfloat units)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glPolygonOffset;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &factor, 4); ptr += 4;
		memcpy(ptr, &units, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glReadPixels_enc(void *self , GLint x, GLint y, GLsizei width, GLsizei height, GLenum format, GLenum type, GLvoid* pixels)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_pixels =  glesv2_enc::pixelDataSize(self, width, height, format, type, 1);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glReadPixels;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_pixels; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	 stream->readbackPixels(self, width, height, format, type, pixels);
	if (useChecksum) checksumCalculator->addBuffer(pixels, __size_pixels);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glReadPixels: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glReleaseShaderCompiler_enc(void *self )
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glReleaseShaderCompiler;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;


	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glRenderbufferStorage_enc(void *self , GLenum target, GLenum internalformat, GLsizei width, GLsizei height)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glRenderbufferStorage;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &internalformat, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glSampleCoverage_enc(void *self , GLclampf value, GLboolean invert)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 1;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glSampleCoverage;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &value, 4); ptr += 4;
		memcpy(ptr, &invert, 1); ptr += 1;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glScissor_enc(void *self , GLint x, GLint y, GLsizei width, GLsizei height)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glScissor;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glStencilFunc_enc(void *self , GLenum func, GLint ref, GLuint mask)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glStencilFunc;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &func, 4); ptr += 4;
		memcpy(ptr, &ref, 4); ptr += 4;
		memcpy(ptr, &mask, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glStencilFuncSeparate_enc(void *self , GLenum face, GLenum func, GLint ref, GLuint mask)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glStencilFuncSeparate;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &face, 4); ptr += 4;
		memcpy(ptr, &func, 4); ptr += 4;
		memcpy(ptr, &ref, 4); ptr += 4;
		memcpy(ptr, &mask, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glStencilMask_enc(void *self , GLuint mask)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glStencilMask;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mask, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glStencilMaskSeparate_enc(void *self , GLenum face, GLuint mask)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glStencilMaskSeparate;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &face, 4); ptr += 4;
		memcpy(ptr, &mask, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glStencilOp_enc(void *self , GLenum fail, GLenum zfail, GLenum zpass)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glStencilOp;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &fail, 4); ptr += 4;
		memcpy(ptr, &zfail, 4); ptr += 4;
		memcpy(ptr, &zpass, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glStencilOpSeparate_enc(void *self , GLenum face, GLenum fail, GLenum zfail, GLenum zpass)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glStencilOpSeparate;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &face, 4); ptr += 4;
		memcpy(ptr, &fail, 4); ptr += 4;
		memcpy(ptr, &zfail, 4); ptr += 4;
		memcpy(ptr, &zpass, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexImage2D_enc(void *self , GLenum target, GLint level, GLint internalformat, GLsizei width, GLsizei height, GLint border, GLenum format, GLenum type, const GLvoid* pixels)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_pixels = ((pixels != NULL) ?  glesv2_enc::pixelDataSize(self, width, height, format, type, 0) : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + __size_pixels + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4);
	ptr = buf;
	int tmp = OP_glTexImage2D;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &internalformat, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &border, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	stream->flush();
	stream->writeFully(&__size_pixels,4);
	if (useChecksum) checksumCalculator->addBuffer(&__size_pixels,4);
	if (pixels != NULL) {
		 stream->uploadPixels(self, width, height, 1, format, type, pixels);
		if (useChecksum) checksumCalculator->addBuffer(pixels, __size_pixels);
	}
	buf = stream->alloc(checksumSize);
	if (useChecksum) checksumCalculator->writeChecksum(buf, checksumSize);

}

void glTexParameterf_enc(void *self , GLenum target, GLenum pname, GLfloat param)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexParameterf;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexParameterfv_enc(void *self , GLenum target, GLenum pname, const GLfloat* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexParameterfv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexParameteri_enc(void *self , GLenum target, GLenum pname, GLint param)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexParameteri;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexParameteriv_enc(void *self , GLenum target, GLenum pname, const GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexParameteriv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexSubImage2D_enc(void *self , GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLenum type, const GLvoid* pixels)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_pixels = ((pixels != NULL) ?  glesv2_enc::pixelDataSize(self, width, height, format, type, 0) : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + __size_pixels + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4);
	ptr = buf;
	int tmp = OP_glTexSubImage2D;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &xoffset, 4); ptr += 4;
		memcpy(ptr, &yoffset, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	stream->flush();
	stream->writeFully(&__size_pixels,4);
	if (useChecksum) checksumCalculator->addBuffer(&__size_pixels,4);
	if (pixels != NULL) {
		 stream->uploadPixels(self, width, height, 1, format, type, pixels);
		if (useChecksum) checksumCalculator->addBuffer(pixels, __size_pixels);
	}
	buf = stream->alloc(checksumSize);
	if (useChecksum) checksumCalculator->writeChecksum(buf, checksumSize);

}

void glUniform1f_enc(void *self , GLint location, GLfloat x)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform1f;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform1fv_enc(void *self , GLint location, GLsizei count, const GLfloat* v)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_v =  (count * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_v + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform1fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_v; ptr += 4;
	memcpy(ptr, v, __size_v);ptr += __size_v;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform1i_enc(void *self , GLint location, GLint x)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform1i;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform1iv_enc(void *self , GLint location, GLsizei count, const GLint* v)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_v =  (count * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_v + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform1iv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_v; ptr += 4;
	memcpy(ptr, v, __size_v);ptr += __size_v;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform2f_enc(void *self , GLint location, GLfloat x, GLfloat y)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform2f;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform2fv_enc(void *self , GLint location, GLsizei count, const GLfloat* v)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_v =  (count * 2 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_v + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform2fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_v; ptr += 4;
	memcpy(ptr, v, __size_v);ptr += __size_v;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform2i_enc(void *self , GLint location, GLint x, GLint y)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform2i;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform2iv_enc(void *self , GLint location, GLsizei count, const GLint* v)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_v =  (count * 2 * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_v + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform2iv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_v; ptr += 4;
	memcpy(ptr, v, __size_v);ptr += __size_v;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform3f_enc(void *self , GLint location, GLfloat x, GLfloat y, GLfloat z)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform3f;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &z, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform3fv_enc(void *self , GLint location, GLsizei count, const GLfloat* v)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_v =  (count * 3 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_v + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform3fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_v; ptr += 4;
	memcpy(ptr, v, __size_v);ptr += __size_v;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform3i_enc(void *self , GLint location, GLint x, GLint y, GLint z)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform3i;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &z, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform3iv_enc(void *self , GLint location, GLsizei count, const GLint* v)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_v =  (3 * count * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_v + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform3iv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_v; ptr += 4;
	memcpy(ptr, v, __size_v);ptr += __size_v;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform4f_enc(void *self , GLint location, GLfloat x, GLfloat y, GLfloat z, GLfloat w)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform4f;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &z, 4); ptr += 4;
		memcpy(ptr, &w, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform4fv_enc(void *self , GLint location, GLsizei count, const GLfloat* v)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_v =  (4 * count * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_v + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform4fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_v; ptr += 4;
	memcpy(ptr, v, __size_v);ptr += __size_v;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform4i_enc(void *self , GLint location, GLint x, GLint y, GLint z, GLint w)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform4i;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &z, 4); ptr += 4;
		memcpy(ptr, &w, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform4iv_enc(void *self , GLint location, GLsizei count, const GLint* v)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_v =  (4 * count * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_v + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform4iv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_v; ptr += 4;
	memcpy(ptr, v, __size_v);ptr += __size_v;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniformMatrix2fv_enc(void *self , GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 4 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 1 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniformMatrix2fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &transpose, 1); ptr += 1;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniformMatrix3fv_enc(void *self , GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 9 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 1 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniformMatrix3fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &transpose, 1); ptr += 1;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniformMatrix4fv_enc(void *self , GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 16 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 1 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniformMatrix4fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &transpose, 1); ptr += 1;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUseProgram_enc(void *self , GLuint program)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUseProgram;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glValidateProgram_enc(void *self , GLuint program)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glValidateProgram;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexAttrib1f_enc(void *self , GLuint indx, GLfloat x)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexAttrib1f;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &indx, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexAttrib1fv_enc(void *self , GLuint indx, const GLfloat* values)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_values =  (sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_values + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexAttrib1fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &indx, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_values; ptr += 4;
	memcpy(ptr, values, __size_values);ptr += __size_values;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexAttrib2f_enc(void *self , GLuint indx, GLfloat x, GLfloat y)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexAttrib2f;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &indx, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexAttrib2fv_enc(void *self , GLuint indx, const GLfloat* values)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_values =  (2 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_values + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexAttrib2fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &indx, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_values; ptr += 4;
	memcpy(ptr, values, __size_values);ptr += __size_values;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexAttrib3f_enc(void *self , GLuint indx, GLfloat x, GLfloat y, GLfloat z)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexAttrib3f;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &indx, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &z, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexAttrib3fv_enc(void *self , GLuint indx, const GLfloat* values)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_values =  (3 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_values + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexAttrib3fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &indx, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_values; ptr += 4;
	memcpy(ptr, values, __size_values);ptr += __size_values;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexAttrib4f_enc(void *self , GLuint indx, GLfloat x, GLfloat y, GLfloat z, GLfloat w)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexAttrib4f;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &indx, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &z, 4); ptr += 4;
		memcpy(ptr, &w, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexAttrib4fv_enc(void *self , GLuint indx, const GLfloat* values)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_values =  (4 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_values + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexAttrib4fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &indx, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_values; ptr += 4;
	memcpy(ptr, values, __size_values);ptr += __size_values;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glViewport_enc(void *self , GLint x, GLint y, GLsizei width, GLsizei height)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glViewport;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glEGLImageTargetTexture2DOES_enc(void *self , GLenum target, GLeglImageOES image)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glEGLImageTargetTexture2DOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &image, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glEGLImageTargetRenderbufferStorageOES_enc(void *self , GLenum target, GLeglImageOES image)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glEGLImageTargetRenderbufferStorageOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &image, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

GLboolean glUnmapBufferOES_enc(void *self , GLenum target)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUnmapBufferOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glUnmapBufferOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glTexImage3DOES_enc(void *self , GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLenum format, GLenum type, const GLvoid* pixels)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_pixels = ((pixels != NULL) ?  glesv2_enc::pixelDataSize3D(self, width, height, depth, format, type, 0) : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + __size_pixels + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4);
	ptr = buf;
	int tmp = OP_glTexImage3DOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &internalformat, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &depth, 4); ptr += 4;
		memcpy(ptr, &border, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	stream->flush();
	stream->writeFully(&__size_pixels,4);
	if (useChecksum) checksumCalculator->addBuffer(&__size_pixels,4);
	if (pixels != NULL) {
		stream->writeFully(pixels, __size_pixels);
		if (useChecksum) checksumCalculator->addBuffer(pixels, __size_pixels);
	}
	buf = stream->alloc(checksumSize);
	if (useChecksum) checksumCalculator->writeChecksum(buf, checksumSize);

}

void glTexSubImage3DOES_enc(void *self , GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLenum type, const GLvoid* pixels)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_pixels = ((pixels != NULL) ?  glesv2_enc::pixelDataSize3D(self, width, height, depth, format, type, 0) : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + __size_pixels + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4);
	ptr = buf;
	int tmp = OP_glTexSubImage3DOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &xoffset, 4); ptr += 4;
		memcpy(ptr, &yoffset, 4); ptr += 4;
		memcpy(ptr, &zoffset, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &depth, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	stream->flush();
	stream->writeFully(&__size_pixels,4);
	if (useChecksum) checksumCalculator->addBuffer(&__size_pixels,4);
	if (pixels != NULL) {
		stream->writeFully(pixels, __size_pixels);
		if (useChecksum) checksumCalculator->addBuffer(pixels, __size_pixels);
	}
	buf = stream->alloc(checksumSize);
	if (useChecksum) checksumCalculator->writeChecksum(buf, checksumSize);

}

void glCopyTexSubImage3DOES_enc(void *self , GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLint x, GLint y, GLsizei width, GLsizei height)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCopyTexSubImage3DOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &xoffset, 4); ptr += 4;
		memcpy(ptr, &yoffset, 4); ptr += 4;
		memcpy(ptr, &zoffset, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glCompressedTexImage3DOES_enc(void *self , GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLsizei imageSize, const GLvoid* data)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data = ((data != NULL) ?  imageSize : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + __size_data + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4);
	ptr = buf;
	int tmp = OP_glCompressedTexImage3DOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &internalformat, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &depth, 4); ptr += 4;
		memcpy(ptr, &border, 4); ptr += 4;
		memcpy(ptr, &imageSize, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	stream->flush();
	stream->writeFully(&__size_data,4);
	if (useChecksum) checksumCalculator->addBuffer(&__size_data,4);
	if (data != NULL) {
		stream->writeFully(data, __size_data);
		if (useChecksum) checksumCalculator->addBuffer(data, __size_data);
	}
	buf = stream->alloc(checksumSize);
	if (useChecksum) checksumCalculator->writeChecksum(buf, checksumSize);

}

void glCompressedTexSubImage3DOES_enc(void *self , GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLsizei imageSize, const GLvoid* data)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data = ((data != NULL) ?  imageSize : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + __size_data + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4);
	ptr = buf;
	int tmp = OP_glCompressedTexSubImage3DOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &xoffset, 4); ptr += 4;
		memcpy(ptr, &yoffset, 4); ptr += 4;
		memcpy(ptr, &zoffset, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &depth, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &imageSize, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	stream->flush();
	stream->writeFully(&__size_data,4);
	if (useChecksum) checksumCalculator->addBuffer(&__size_data,4);
	if (data != NULL) {
		stream->writeFully(data, __size_data);
		if (useChecksum) checksumCalculator->addBuffer(data, __size_data);
	}
	buf = stream->alloc(checksumSize);
	if (useChecksum) checksumCalculator->writeChecksum(buf, checksumSize);

}

void glFramebufferTexture3DOES_enc(void *self , GLenum target, GLenum attachment, GLenum textarget, GLuint texture, GLint level, GLint zoffset)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFramebufferTexture3DOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &attachment, 4); ptr += 4;
		memcpy(ptr, &textarget, 4); ptr += 4;
		memcpy(ptr, &texture, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &zoffset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBindVertexArrayOES_enc(void *self , GLuint array)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBindVertexArrayOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &array, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDeleteVertexArraysOES_enc(void *self , GLsizei n, const GLuint* arrays)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_arrays =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_arrays + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDeleteVertexArraysOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_arrays; ptr += 4;
	memcpy(ptr, arrays, __size_arrays);ptr += __size_arrays;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGenVertexArraysOES_enc(void *self , GLsizei n, GLuint* arrays)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_arrays =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGenVertexArraysOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_arrays; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(arrays, __size_arrays);
	if (useChecksum) checksumCalculator->addBuffer(arrays, __size_arrays);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGenVertexArraysOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

GLboolean glIsVertexArrayOES_enc(void *self , GLuint array)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsVertexArrayOES;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &array, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsVertexArrayOES: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glDiscardFramebufferEXT_enc(void *self , GLenum target, GLsizei numAttachments, const GLenum* attachments)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_attachments =  (numAttachments * sizeof(GLenum));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_attachments + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDiscardFramebufferEXT;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &numAttachments, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_attachments; ptr += 4;
	memcpy(ptr, attachments, __size_attachments);ptr += __size_attachments;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexAttribPointerData_enc(void *self , GLuint indx, GLint size, GLenum type, GLboolean normalized, GLsizei stride, void* data, GLuint datalen)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data =  datalen;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 1 + 4 + __size_data + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexAttribPointerData;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &indx, 4); ptr += 4;
		memcpy(ptr, &size, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &normalized, 1); ptr += 1;
		memcpy(ptr, &stride, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;
	 glUtilsPackPointerData((unsigned char *)ptr, (unsigned char *)data, size, type, stride, datalen);ptr += __size_data;
		memcpy(ptr, &datalen, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexAttribPointerOffset_enc(void *self , GLuint indx, GLint size, GLenum type, GLboolean normalized, GLsizei stride, GLuint offset)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 1 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexAttribPointerOffset;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &indx, 4); ptr += 4;
		memcpy(ptr, &size, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &normalized, 1); ptr += 1;
		memcpy(ptr, &stride, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawElementsOffset_enc(void *self , GLenum mode, GLsizei count, GLenum type, GLuint offset)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawElementsOffset;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawElementsData_enc(void *self , GLenum mode, GLsizei count, GLenum type, void* data, GLuint datalen)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data =  datalen;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_data + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawElementsData;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;
	memcpy(ptr, data, __size_data);ptr += __size_data;
		memcpy(ptr, &datalen, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetCompressedTextureFormats_enc(void *self , int count, GLint* formats)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_formats =  (count * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetCompressedTextureFormats;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_formats; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(formats, __size_formats);
	if (useChecksum) checksumCalculator->addBuffer(formats, __size_formats);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetCompressedTextureFormats: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glShaderString_enc(void *self , GLuint shader, const GLchar* string, GLsizei len)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_string =  len;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_string + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glShaderString;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &shader, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_string; ptr += 4;
	memcpy(ptr, string, __size_string);ptr += __size_string;
		memcpy(ptr, &len, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

int glFinishRoundTrip_enc(void *self )
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFinishRoundTrip;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;


	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	int retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glFinishRoundTrip: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glGenVertexArrays_enc(void *self , GLsizei n, GLuint* arrays)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_arrays =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGenVertexArrays;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_arrays; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(arrays, __size_arrays);
	if (useChecksum) checksumCalculator->addBuffer(arrays, __size_arrays);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGenVertexArrays: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glBindVertexArray_enc(void *self , GLuint array)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBindVertexArray;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &array, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDeleteVertexArrays_enc(void *self , GLsizei n, const GLuint* arrays)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_arrays =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_arrays + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDeleteVertexArrays;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_arrays; ptr += 4;
	memcpy(ptr, arrays, __size_arrays);ptr += __size_arrays;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

GLboolean glIsVertexArray_enc(void *self , GLuint array)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsVertexArray;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &array, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsVertexArray: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glMapBufferRangeAEMU_enc(void *self , GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, void* mapped)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_mapped = ((mapped != NULL) ?  length : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glMapBufferRangeAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;
		memcpy(ptr, &length, 4); ptr += 4;
		memcpy(ptr, &access, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_mapped; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	if (mapped != NULL) {
		stream->readback(mapped, __size_mapped);
		if (useChecksum) checksumCalculator->addBuffer(mapped, __size_mapped);
	}
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glMapBufferRangeAEMU: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glUnmapBufferAEMU_enc(void *self , GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, void* guest_buffer, GLboolean* out_res)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_guest_buffer = ((guest_buffer != NULL) ?  length : 0);
	const unsigned int __size_out_res =  (sizeof(GLboolean));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + __size_guest_buffer + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUnmapBufferAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;
		memcpy(ptr, &length, 4); ptr += 4;
		memcpy(ptr, &access, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_guest_buffer; ptr += 4;
	if (guest_buffer != NULL) memcpy(ptr, guest_buffer, __size_guest_buffer);ptr += __size_guest_buffer;
	*(unsigned int *)(ptr) = __size_out_res; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(out_res, __size_out_res);
	if (useChecksum) checksumCalculator->addBuffer(out_res, __size_out_res);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glUnmapBufferAEMU: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glFlushMappedBufferRangeAEMU_enc(void *self , GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, void* guest_buffer)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_guest_buffer = ((guest_buffer != NULL) ?  length : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + __size_guest_buffer + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFlushMappedBufferRangeAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;
		memcpy(ptr, &length, 4); ptr += 4;
		memcpy(ptr, &access, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_guest_buffer; ptr += 4;
	if (guest_buffer != NULL) memcpy(ptr, guest_buffer, __size_guest_buffer);ptr += __size_guest_buffer;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glReadPixelsOffsetAEMU_enc(void *self , GLint x, GLint y, GLsizei width, GLsizei height, GLenum format, GLenum type, GLuint offset)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glReadPixelsOffsetAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glCompressedTexImage2DOffsetAEMU_enc(void *self , GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLint border, GLsizei imageSize, GLuint offset)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCompressedTexImage2DOffsetAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &internalformat, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &border, 4); ptr += 4;
		memcpy(ptr, &imageSize, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glCompressedTexSubImage2DOffsetAEMU_enc(void *self , GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLsizei imageSize, GLuint offset)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCompressedTexSubImage2DOffsetAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &xoffset, 4); ptr += 4;
		memcpy(ptr, &yoffset, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &imageSize, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexImage2DOffsetAEMU_enc(void *self , GLenum target, GLint level, GLint internalformat, GLsizei width, GLsizei height, GLint border, GLenum format, GLenum type, GLuint offset)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexImage2DOffsetAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &internalformat, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &border, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexSubImage2DOffsetAEMU_enc(void *self , GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLenum type, GLuint offset)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexSubImage2DOffsetAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &xoffset, 4); ptr += 4;
		memcpy(ptr, &yoffset, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBindBufferRange_enc(void *self , GLenum target, GLuint index, GLuint buffer, GLintptr offset, GLsizeiptr size)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBindBufferRange;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &index, 4); ptr += 4;
		memcpy(ptr, &buffer, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;
		memcpy(ptr, &size, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBindBufferBase_enc(void *self , GLenum target, GLuint index, GLuint buffer)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBindBufferBase;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &index, 4); ptr += 4;
		memcpy(ptr, &buffer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glCopyBufferSubData_enc(void *self , GLenum readtarget, GLenum writetarget, GLintptr readoffset, GLintptr writeoffset, GLsizeiptr size)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCopyBufferSubData;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &readtarget, 4); ptr += 4;
		memcpy(ptr, &writetarget, 4); ptr += 4;
		memcpy(ptr, &readoffset, 4); ptr += 4;
		memcpy(ptr, &writeoffset, 4); ptr += 4;
		memcpy(ptr, &size, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->flush();
}

void glClearBufferiv_enc(void *self , GLenum buffer, GLint drawBuffer, const GLint* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (sizeof(GLint) * glesv2_enc::clearBufferNumElts(self, buffer));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClearBufferiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &buffer, 4); ptr += 4;
		memcpy(ptr, &drawBuffer, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glClearBufferuiv_enc(void *self , GLenum buffer, GLint drawBuffer, const GLuint* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (sizeof(GLuint) * glesv2_enc::clearBufferNumElts(self, buffer));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClearBufferuiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &buffer, 4); ptr += 4;
		memcpy(ptr, &drawBuffer, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glClearBufferfv_enc(void *self , GLenum buffer, GLint drawBuffer, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (sizeof(GLfloat) * glesv2_enc::clearBufferNumElts(self, buffer));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClearBufferfv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &buffer, 4); ptr += 4;
		memcpy(ptr, &drawBuffer, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glClearBufferfi_enc(void *self , GLenum buffer, GLint drawBuffer, GLfloat depth, GLint stencil)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClearBufferfi;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &buffer, 4); ptr += 4;
		memcpy(ptr, &drawBuffer, 4); ptr += 4;
		memcpy(ptr, &depth, 4); ptr += 4;
		memcpy(ptr, &stencil, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniformBlockBinding_enc(void *self , GLuint program, GLuint uniformBlockIndex, GLuint uniformBlockBinding)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniformBlockBinding;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &uniformBlockIndex, 4); ptr += 4;
		memcpy(ptr, &uniformBlockBinding, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

GLuint glGetUniformBlockIndex_enc(void *self , GLuint program, const GLchar* uniformBlockName)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_uniformBlockName =  (strlen(uniformBlockName) + 1);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_uniformBlockName + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetUniformBlockIndex;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_uniformBlockName; ptr += 4;
	memcpy(ptr, uniformBlockName, __size_uniformBlockName);ptr += __size_uniformBlockName;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLuint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetUniformBlockIndex: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glGetUniformIndicesAEMU_enc(void *self , GLuint program, GLsizei uniformCount, const GLchar* packedUniformNames, GLsizei packedLen, GLuint* uniformIndices)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_packedUniformNames =  packedLen;
	const unsigned int __size_uniformIndices =  (uniformCount * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_packedUniformNames + 4 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetUniformIndicesAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &uniformCount, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_packedUniformNames; ptr += 4;
	memcpy(ptr, packedUniformNames, __size_packedUniformNames);ptr += __size_packedUniformNames;
		memcpy(ptr, &packedLen, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_uniformIndices; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(uniformIndices, __size_uniformIndices);
	if (useChecksum) checksumCalculator->addBuffer(uniformIndices, __size_uniformIndices);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetUniformIndicesAEMU: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetActiveUniformBlockiv_enc(void *self , GLuint program, GLuint uniformBlockIndex, GLenum pname, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glesv2_enc::glActiveUniformBlockivParamSize(self, program, uniformBlockIndex, pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetActiveUniformBlockiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &uniformBlockIndex, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetActiveUniformBlockiv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetActiveUniformBlockName_enc(void *self , GLuint program, GLuint uniformBlockIndex, GLsizei bufSize, GLsizei* length, GLchar* uniformBlockName)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_length = ((length != NULL) ?  (sizeof(GLsizei)) : 0);
	const unsigned int __size_uniformBlockName = ((uniformBlockName != NULL) ?  bufSize : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 0 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetActiveUniformBlockName;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &uniformBlockIndex, 4); ptr += 4;
		memcpy(ptr, &bufSize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_length; ptr += 4;
	*(unsigned int *)(ptr) = __size_uniformBlockName; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	if (length != NULL) {
		stream->readback(length, __size_length);
		if (useChecksum) checksumCalculator->addBuffer(length, __size_length);
	}
	if (uniformBlockName != NULL) {
		stream->readback(uniformBlockName, __size_uniformBlockName);
		if (useChecksum) checksumCalculator->addBuffer(uniformBlockName, __size_uniformBlockName);
	}
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetActiveUniformBlockName: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glUniform1ui_enc(void *self , GLint location, GLuint v0)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform1ui;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &v0, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform2ui_enc(void *self , GLint location, GLuint v0, GLuint v1)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform2ui;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &v0, 4); ptr += 4;
		memcpy(ptr, &v1, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform3ui_enc(void *self , GLint location, GLuint v0, GLuint v1, GLuint v2)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform3ui;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &v0, 4); ptr += 4;
		memcpy(ptr, &v1, 4); ptr += 4;
		memcpy(ptr, &v2, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform4ui_enc(void *self , GLint location, GLint v0, GLuint v1, GLuint v2, GLuint v3)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform4ui;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &v0, 4); ptr += 4;
		memcpy(ptr, &v1, 4); ptr += 4;
		memcpy(ptr, &v2, 4); ptr += 4;
		memcpy(ptr, &v3, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform1uiv_enc(void *self , GLint location, GLsizei count, const GLuint* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform1uiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform2uiv_enc(void *self , GLint location, GLsizei count, const GLuint* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 2 * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform2uiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform3uiv_enc(void *self , GLint location, GLsizei count, const GLuint* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 3 * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform3uiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniform4uiv_enc(void *self , GLint location, GLsizei count, const GLuint* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 4 * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniform4uiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniformMatrix2x3fv_enc(void *self , GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 6 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 1 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniformMatrix2x3fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &transpose, 1); ptr += 1;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniformMatrix3x2fv_enc(void *self , GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 6 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 1 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniformMatrix3x2fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &transpose, 1); ptr += 1;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniformMatrix2x4fv_enc(void *self , GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 8 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 1 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniformMatrix2x4fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &transpose, 1); ptr += 1;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniformMatrix4x2fv_enc(void *self , GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 8 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 1 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniformMatrix4x2fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &transpose, 1); ptr += 1;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniformMatrix3x4fv_enc(void *self , GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 12 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 1 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniformMatrix3x4fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &transpose, 1); ptr += 1;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUniformMatrix4x3fv_enc(void *self , GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 12 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 1 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUniformMatrix4x3fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &transpose, 1); ptr += 1;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetUniformuiv_enc(void *self , GLuint program, GLint location, GLuint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  glSizeof(glesv2_enc::uniformType(self, program, location));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetUniformuiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetUniformuiv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetActiveUniformsiv_enc(void *self , GLuint program, GLsizei uniformCount, const GLuint* uniformIndices, GLenum pname, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_uniformIndices =  (uniformCount * sizeof(GLuint));
	const unsigned int __size_params =  (uniformCount * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_uniformIndices + 4 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetActiveUniformsiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &uniformCount, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_uniformIndices; ptr += 4;
	memcpy(ptr, uniformIndices, __size_uniformIndices);ptr += __size_uniformIndices;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetActiveUniformsiv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glVertexAttribI4i_enc(void *self , GLuint index, GLint v0, GLint v1, GLint v2, GLint v3)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexAttribI4i;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &index, 4); ptr += 4;
		memcpy(ptr, &v0, 4); ptr += 4;
		memcpy(ptr, &v1, 4); ptr += 4;
		memcpy(ptr, &v2, 4); ptr += 4;
		memcpy(ptr, &v3, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexAttribI4ui_enc(void *self , GLuint index, GLuint v0, GLuint v1, GLuint v2, GLuint v3)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexAttribI4ui;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &index, 4); ptr += 4;
		memcpy(ptr, &v0, 4); ptr += 4;
		memcpy(ptr, &v1, 4); ptr += 4;
		memcpy(ptr, &v2, 4); ptr += 4;
		memcpy(ptr, &v3, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexAttribI4iv_enc(void *self , GLuint index, const GLint* v)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_v =  (4 * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_v + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexAttribI4iv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &index, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_v; ptr += 4;
	memcpy(ptr, v, __size_v);ptr += __size_v;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexAttribI4uiv_enc(void *self , GLuint index, const GLuint* v)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_v =  (4 * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_v + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexAttribI4uiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &index, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_v; ptr += 4;
	memcpy(ptr, v, __size_v);ptr += __size_v;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexAttribIPointerOffsetAEMU_enc(void *self , GLuint index, GLint size, GLenum type, GLsizei stride, GLuint offset)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexAttribIPointerOffsetAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &index, 4); ptr += 4;
		memcpy(ptr, &size, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &stride, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexAttribIPointerDataAEMU_enc(void *self , GLuint index, GLint size, GLenum type, GLsizei stride, void* data, GLuint datalen)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data =  datalen;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + __size_data + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexAttribIPointerDataAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &index, 4); ptr += 4;
		memcpy(ptr, &size, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &stride, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;
	 glUtilsPackPointerData((unsigned char *)ptr, (unsigned char *)data, size, type, stride, datalen);ptr += __size_data;
		memcpy(ptr, &datalen, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetVertexAttribIiv_enc(void *self , GLuint index, GLenum pname, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetVertexAttribIiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &index, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetVertexAttribIiv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetVertexAttribIuiv_enc(void *self , GLuint index, GLenum pname, GLuint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetVertexAttribIuiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &index, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetVertexAttribIuiv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glVertexAttribDivisor_enc(void *self , GLuint index, GLuint divisor)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexAttribDivisor;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &index, 4); ptr += 4;
		memcpy(ptr, &divisor, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawArraysInstanced_enc(void *self , GLenum mode, GLint first, GLsizei count, GLsizei primcount)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawArraysInstanced;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;
		memcpy(ptr, &first, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &primcount, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawElementsInstancedDataAEMU_enc(void *self , GLenum mode, GLsizei count, GLenum type, const void* indices, GLsizei primcount, GLsizei datalen)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_indices =  datalen;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_indices + 4 + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawElementsInstancedDataAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_indices; ptr += 4;
	memcpy(ptr, indices, __size_indices);ptr += __size_indices;
		memcpy(ptr, &primcount, 4); ptr += 4;
		memcpy(ptr, &datalen, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawElementsInstancedOffsetAEMU_enc(void *self , GLenum mode, GLsizei count, GLenum type, GLuint offset, GLsizei primcount)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawElementsInstancedOffsetAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;
		memcpy(ptr, &primcount, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawRangeElementsDataAEMU_enc(void *self , GLenum mode, GLuint start, GLuint end, GLsizei count, GLenum type, const GLvoid* indices, GLsizei datalen)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_indices =  datalen;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + __size_indices + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawRangeElementsDataAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;
		memcpy(ptr, &start, 4); ptr += 4;
		memcpy(ptr, &end, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_indices; ptr += 4;
	memcpy(ptr, indices, __size_indices);ptr += __size_indices;
		memcpy(ptr, &datalen, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawRangeElementsOffsetAEMU_enc(void *self , GLenum mode, GLuint start, GLuint end, GLsizei count, GLenum type, GLuint offset)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawRangeElementsOffsetAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;
		memcpy(ptr, &start, 4); ptr += 4;
		memcpy(ptr, &end, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

uint64_t glFenceSyncAEMU_enc(void *self , GLenum condition, GLbitfield flags)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFenceSyncAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &condition, 4); ptr += 4;
		memcpy(ptr, &flags, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	uint64_t retval;
	stream->readback(&retval, 8);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 8);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glFenceSyncAEMU: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

GLenum glClientWaitSyncAEMU_enc(void *self , uint64_t wait_on, GLbitfield flags, GLuint64 timeout)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 8 + 4 + 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glClientWaitSyncAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &wait_on, 8); ptr += 8;
		memcpy(ptr, &flags, 4); ptr += 4;
		memcpy(ptr, &timeout, 8); ptr += 8;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLenum retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glClientWaitSyncAEMU: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glWaitSyncAEMU_enc(void *self , uint64_t wait_on, GLbitfield flags, GLuint64 timeout)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 8 + 4 + 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glWaitSyncAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &wait_on, 8); ptr += 8;
		memcpy(ptr, &flags, 4); ptr += 4;
		memcpy(ptr, &timeout, 8); ptr += 8;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDeleteSyncAEMU_enc(void *self , uint64_t to_delete)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDeleteSyncAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &to_delete, 8); ptr += 8;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

GLboolean glIsSyncAEMU_enc(void *self , uint64_t sync)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsSyncAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &sync, 8); ptr += 8;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsSyncAEMU: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glGetSyncivAEMU_enc(void *self , uint64_t sync, GLenum pname, GLsizei bufSize, GLsizei* length, GLint* values)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_length = ((length != NULL) ?  (sizeof(GLsizei)) : 0);
	const unsigned int __size_values =  (bufSize * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 8 + 4 + 4 + 0 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetSyncivAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &sync, 8); ptr += 8;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &bufSize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_length; ptr += 4;
	*(unsigned int *)(ptr) = __size_values; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	if (length != NULL) {
		stream->readback(length, __size_length);
		if (useChecksum) checksumCalculator->addBuffer(length, __size_length);
	}
	stream->readback(values, __size_values);
	if (useChecksum) checksumCalculator->addBuffer(values, __size_values);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetSyncivAEMU: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glDrawBuffers_enc(void *self , GLsizei n, const GLenum* bufs)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_bufs =  (n * sizeof(GLenum));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_bufs + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawBuffers;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_bufs; ptr += 4;
	memcpy(ptr, bufs, __size_bufs);ptr += __size_bufs;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glReadBuffer_enc(void *self , GLenum src)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glReadBuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &src, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBlitFramebuffer_enc(void *self , GLint srcX0, GLint srcY0, GLint srcX1, GLint srcY1, GLint dstX0, GLint dstY0, GLint dstX1, GLint dstY1, GLbitfield mask, GLenum filter)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBlitFramebuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &srcX0, 4); ptr += 4;
		memcpy(ptr, &srcY0, 4); ptr += 4;
		memcpy(ptr, &srcX1, 4); ptr += 4;
		memcpy(ptr, &srcY1, 4); ptr += 4;
		memcpy(ptr, &dstX0, 4); ptr += 4;
		memcpy(ptr, &dstY0, 4); ptr += 4;
		memcpy(ptr, &dstX1, 4); ptr += 4;
		memcpy(ptr, &dstY1, 4); ptr += 4;
		memcpy(ptr, &mask, 4); ptr += 4;
		memcpy(ptr, &filter, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glInvalidateFramebuffer_enc(void *self , GLenum target, GLsizei numAttachments, const GLenum* attachments)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_attachments =  (numAttachments * sizeof(GLenum));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_attachments + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glInvalidateFramebuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &numAttachments, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_attachments; ptr += 4;
	memcpy(ptr, attachments, __size_attachments);ptr += __size_attachments;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glInvalidateSubFramebuffer_enc(void *self , GLenum target, GLsizei numAttachments, const GLenum* attachments, GLint x, GLint y, GLsizei width, GLsizei height)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_attachments =  (numAttachments * sizeof(GLenum));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_attachments + 4 + 4 + 4 + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glInvalidateSubFramebuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &numAttachments, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_attachments; ptr += 4;
	memcpy(ptr, attachments, __size_attachments);ptr += __size_attachments;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glFramebufferTextureLayer_enc(void *self , GLenum target, GLenum attachment, GLuint texture, GLint level, GLint layer)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFramebufferTextureLayer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &attachment, 4); ptr += 4;
		memcpy(ptr, &texture, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &layer, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glRenderbufferStorageMultisample_enc(void *self , GLenum target, GLsizei samples, GLenum internalformat, GLsizei width, GLsizei height)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glRenderbufferStorageMultisample;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &samples, 4); ptr += 4;
		memcpy(ptr, &internalformat, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexStorage2D_enc(void *self , GLenum target, GLsizei levels, GLenum internalformat, GLsizei width, GLsizei height)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexStorage2D;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &levels, 4); ptr += 4;
		memcpy(ptr, &internalformat, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetInternalformativ_enc(void *self , GLenum target, GLenum internalformat, GLenum pname, GLsizei bufSize, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (sizeof(GLint) * bufSize);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetInternalformativ;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &internalformat, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &bufSize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetInternalformativ: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glBeginTransformFeedback_enc(void *self , GLenum primitiveMode)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBeginTransformFeedback;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &primitiveMode, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glEndTransformFeedback_enc(void *self )
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glEndTransformFeedback;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;


	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGenTransformFeedbacks_enc(void *self , GLsizei n, GLuint* ids)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_ids =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGenTransformFeedbacks;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_ids; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(ids, __size_ids);
	if (useChecksum) checksumCalculator->addBuffer(ids, __size_ids);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGenTransformFeedbacks: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glDeleteTransformFeedbacks_enc(void *self , GLsizei n, const GLuint* ids)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_ids =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_ids + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDeleteTransformFeedbacks;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_ids; ptr += 4;
	memcpy(ptr, ids, __size_ids);ptr += __size_ids;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBindTransformFeedback_enc(void *self , GLenum target, GLuint id)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBindTransformFeedback;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &id, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glPauseTransformFeedback_enc(void *self )
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glPauseTransformFeedback;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;


	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glResumeTransformFeedback_enc(void *self )
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glResumeTransformFeedback;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;


	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

GLboolean glIsTransformFeedback_enc(void *self , GLuint id)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsTransformFeedback;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &id, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsTransformFeedback: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glTransformFeedbackVaryingsAEMU_enc(void *self , GLuint program, GLsizei count, const char* packedVaryings, GLuint packedVaryingsLen, GLenum bufferMode)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_packedVaryings =  packedVaryingsLen;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_packedVaryings + 4 + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTransformFeedbackVaryingsAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_packedVaryings; ptr += 4;
	memcpy(ptr, packedVaryings, __size_packedVaryings);ptr += __size_packedVaryings;
		memcpy(ptr, &packedVaryingsLen, 4); ptr += 4;
		memcpy(ptr, &bufferMode, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetTransformFeedbackVarying_enc(void *self , GLuint program, GLuint index, GLsizei bufSize, GLsizei* length, GLsizei* size, GLenum* type, char* name)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_length = ((length != NULL) ?  (sizeof(GLsizei)) : 0);
	const unsigned int __size_size =  (sizeof(GLsizei));
	const unsigned int __size_type = ((type != NULL) ?  (sizeof(GLenum)) : 0);
	const unsigned int __size_name = ((name != NULL) ?  bufSize : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 0 + 0 + 0 + 0 + 4*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetTransformFeedbackVarying;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &index, 4); ptr += 4;
		memcpy(ptr, &bufSize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_length; ptr += 4;
	*(unsigned int *)(ptr) = __size_size; ptr += 4;
	*(unsigned int *)(ptr) = __size_type; ptr += 4;
	*(unsigned int *)(ptr) = __size_name; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	if (length != NULL) {
		stream->readback(length, __size_length);
		if (useChecksum) checksumCalculator->addBuffer(length, __size_length);
	}
	stream->readback(size, __size_size);
	if (useChecksum) checksumCalculator->addBuffer(size, __size_size);
	if (type != NULL) {
		stream->readback(type, __size_type);
		if (useChecksum) checksumCalculator->addBuffer(type, __size_type);
	}
	if (name != NULL) {
		stream->readback(name, __size_name);
		if (useChecksum) checksumCalculator->addBuffer(name, __size_name);
	}
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetTransformFeedbackVarying: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGenSamplers_enc(void *self , GLsizei n, GLuint* samplers)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_samplers =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGenSamplers;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_samplers; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(samplers, __size_samplers);
	if (useChecksum) checksumCalculator->addBuffer(samplers, __size_samplers);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGenSamplers: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glDeleteSamplers_enc(void *self , GLsizei n, const GLuint* samplers)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_samplers =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_samplers + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDeleteSamplers;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_samplers; ptr += 4;
	memcpy(ptr, samplers, __size_samplers);ptr += __size_samplers;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBindSampler_enc(void *self , GLuint unit, GLuint sampler)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBindSampler;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &unit, 4); ptr += 4;
		memcpy(ptr, &sampler, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glSamplerParameterf_enc(void *self , GLuint sampler, GLenum pname, GLfloat param)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glSamplerParameterf;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &sampler, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glSamplerParameteri_enc(void *self , GLuint sampler, GLenum pname, GLint param)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glSamplerParameteri;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &sampler, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glSamplerParameterfv_enc(void *self , GLuint sampler, GLenum pname, const GLfloat* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glSamplerParameterfv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &sampler, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glSamplerParameteriv_enc(void *self , GLuint sampler, GLenum pname, const GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_params + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glSamplerParameteriv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &sampler, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;
	memcpy(ptr, params, __size_params);ptr += __size_params;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetSamplerParameterfv_enc(void *self , GLuint sampler, GLenum pname, GLfloat* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetSamplerParameterfv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &sampler, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetSamplerParameterfv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetSamplerParameteriv_enc(void *self , GLuint sampler, GLenum pname, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetSamplerParameteriv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &sampler, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetSamplerParameteriv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

GLboolean glIsSampler_enc(void *self , GLuint sampler)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsSampler;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &sampler, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsSampler: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glGenQueries_enc(void *self , GLsizei n, GLuint* queries)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_queries =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGenQueries;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_queries; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(queries, __size_queries);
	if (useChecksum) checksumCalculator->addBuffer(queries, __size_queries);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGenQueries: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glDeleteQueries_enc(void *self , GLsizei n, const GLuint* queries)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_queries =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_queries + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDeleteQueries;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_queries; ptr += 4;
	memcpy(ptr, queries, __size_queries);ptr += __size_queries;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBeginQuery_enc(void *self , GLenum target, GLuint query)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBeginQuery;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &query, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glEndQuery_enc(void *self , GLenum target)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glEndQuery;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetQueryiv_enc(void *self , GLenum target, GLenum pname, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetQueryiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetQueryiv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetQueryObjectuiv_enc(void *self , GLuint query, GLenum pname, GLuint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetQueryObjectuiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &query, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetQueryObjectuiv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

GLboolean glIsQuery_enc(void *self , GLuint query)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsQuery;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &query, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsQuery: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glProgramParameteri_enc(void *self , GLuint program, GLenum pname, GLint value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramParameteri;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &value, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramBinary_enc(void *self , GLuint program, GLenum binaryFormat, const void* binary, GLsizei length)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_binary =  length;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_binary + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramBinary;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &binaryFormat, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_binary; ptr += 4;
	memcpy(ptr, binary, __size_binary);ptr += __size_binary;
		memcpy(ptr, &length, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetProgramBinary_enc(void *self , GLuint program, GLsizei bufSize, GLsizei* length, GLenum* binaryFormat, void* binary)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_length = ((length != NULL) ?  (sizeof(GLsizei)) : 0);
	const unsigned int __size_binaryFormat =  (sizeof(GLenum));
	const unsigned int __size_binary =  bufSize;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 0 + 0 + 3*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetProgramBinary;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &bufSize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_length; ptr += 4;
	*(unsigned int *)(ptr) = __size_binaryFormat; ptr += 4;
	*(unsigned int *)(ptr) = __size_binary; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	if (length != NULL) {
		stream->readback(length, __size_length);
		if (useChecksum) checksumCalculator->addBuffer(length, __size_length);
	}
	stream->readback(binaryFormat, __size_binaryFormat);
	if (useChecksum) checksumCalculator->addBuffer(binaryFormat, __size_binaryFormat);
	stream->readback(binary, __size_binary);
	if (useChecksum) checksumCalculator->addBuffer(binary, __size_binary);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetProgramBinary: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

GLint glGetFragDataLocation_enc(void *self , GLuint program, const char* name)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_name =  (strlen(name) + 1);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_name + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetFragDataLocation;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_name; ptr += 4;
	memcpy(ptr, name, __size_name);ptr += __size_name;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetFragDataLocation: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glGetInteger64v_enc(void *self , GLenum pname, GLint64* data)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data =  (glUtilsParamSize(pname) * sizeof(GLint64));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetInteger64v;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(data, __size_data);
	if (useChecksum) checksumCalculator->addBuffer(data, __size_data);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetInteger64v: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetIntegeri_v_enc(void *self , GLenum target, GLuint index, GLint* data)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data =  (sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetIntegeri_v;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &index, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(data, __size_data);
	if (useChecksum) checksumCalculator->addBuffer(data, __size_data);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetIntegeri_v: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetInteger64i_v_enc(void *self , GLenum target, GLuint index, GLint64* data)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data =  (sizeof(GLint64));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetInteger64i_v;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &index, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(data, __size_data);
	if (useChecksum) checksumCalculator->addBuffer(data, __size_data);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetInteger64i_v: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glTexImage3D_enc(void *self , GLenum target, GLint level, GLint internalFormat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLenum format, GLenum type, const GLvoid* data)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data = ((data != NULL) ?  glesv2_enc::pixelDataSize3D(self, width, height, depth, format, type, 0) : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + __size_data + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4);
	ptr = buf;
	int tmp = OP_glTexImage3D;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &internalFormat, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &depth, 4); ptr += 4;
		memcpy(ptr, &border, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	stream->flush();
	stream->writeFully(&__size_data,4);
	if (useChecksum) checksumCalculator->addBuffer(&__size_data,4);
	if (data != NULL) {
		 stream->uploadPixels(self, width, height, depth, format, type, data);
		if (useChecksum) checksumCalculator->addBuffer(data, __size_data);
	}
	buf = stream->alloc(checksumSize);
	if (useChecksum) checksumCalculator->writeChecksum(buf, checksumSize);

}

void glTexImage3DOffsetAEMU_enc(void *self , GLenum target, GLint level, GLint internalFormat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLenum format, GLenum type, GLuint offset)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexImage3DOffsetAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &internalFormat, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &depth, 4); ptr += 4;
		memcpy(ptr, &border, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexStorage3D_enc(void *self , GLenum target, GLsizei levels, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexStorage3D;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &levels, 4); ptr += 4;
		memcpy(ptr, &internalformat, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &depth, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexSubImage3D_enc(void *self , GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLenum type, const GLvoid* data)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data = ((data != NULL) ?  glesv2_enc::pixelDataSize3D(self, width, height, depth, format, type, 0) : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + __size_data + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4);
	ptr = buf;
	int tmp = OP_glTexSubImage3D;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &xoffset, 4); ptr += 4;
		memcpy(ptr, &yoffset, 4); ptr += 4;
		memcpy(ptr, &zoffset, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &depth, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	stream->flush();
	stream->writeFully(&__size_data,4);
	if (useChecksum) checksumCalculator->addBuffer(&__size_data,4);
	if (data != NULL) {
		 stream->uploadPixels(self, width, height, depth, format, type, data);
		if (useChecksum) checksumCalculator->addBuffer(data, __size_data);
	}
	buf = stream->alloc(checksumSize);
	if (useChecksum) checksumCalculator->writeChecksum(buf, checksumSize);

}

void glTexSubImage3DOffsetAEMU_enc(void *self , GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLenum type, GLuint offset)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexSubImage3DOffsetAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &xoffset, 4); ptr += 4;
		memcpy(ptr, &yoffset, 4); ptr += 4;
		memcpy(ptr, &zoffset, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &depth, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glCompressedTexImage3D_enc(void *self , GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLsizei imageSize, const GLvoid* data)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data = ((data != NULL) ?  imageSize : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + __size_data + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4);
	ptr = buf;
	int tmp = OP_glCompressedTexImage3D;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &internalformat, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &depth, 4); ptr += 4;
		memcpy(ptr, &border, 4); ptr += 4;
		memcpy(ptr, &imageSize, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	stream->flush();
	stream->writeFully(&__size_data,4);
	if (useChecksum) checksumCalculator->addBuffer(&__size_data,4);
	if (data != NULL) {
		stream->writeFully(data, __size_data);
		if (useChecksum) checksumCalculator->addBuffer(data, __size_data);
	}
	buf = stream->alloc(checksumSize);
	if (useChecksum) checksumCalculator->writeChecksum(buf, checksumSize);

}

void glCompressedTexImage3DOffsetAEMU_enc(void *self , GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLsizei imageSize, GLuint offset)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCompressedTexImage3DOffsetAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &internalformat, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &depth, 4); ptr += 4;
		memcpy(ptr, &border, 4); ptr += 4;
		memcpy(ptr, &imageSize, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glCompressedTexSubImage3D_enc(void *self , GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLsizei imageSize, const GLvoid* data)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data = ((data != NULL) ?  imageSize : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + __size_data + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4);
	ptr = buf;
	int tmp = OP_glCompressedTexSubImage3D;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &xoffset, 4); ptr += 4;
		memcpy(ptr, &yoffset, 4); ptr += 4;
		memcpy(ptr, &zoffset, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &depth, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &imageSize, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	stream->flush();
	stream->writeFully(&__size_data,4);
	if (useChecksum) checksumCalculator->addBuffer(&__size_data,4);
	if (data != NULL) {
		stream->writeFully(data, __size_data);
		if (useChecksum) checksumCalculator->addBuffer(data, __size_data);
	}
	buf = stream->alloc(checksumSize);
	if (useChecksum) checksumCalculator->writeChecksum(buf, checksumSize);

}

void glCompressedTexSubImage3DOffsetAEMU_enc(void *self , GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLsizei imageSize, GLuint data)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCompressedTexSubImage3DOffsetAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &xoffset, 4); ptr += 4;
		memcpy(ptr, &yoffset, 4); ptr += 4;
		memcpy(ptr, &zoffset, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &depth, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &imageSize, 4); ptr += 4;
		memcpy(ptr, &data, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glCopyTexSubImage3D_enc(void *self , GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLint x, GLint y, GLsizei width, GLsizei height)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCopyTexSubImage3D;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &xoffset, 4); ptr += 4;
		memcpy(ptr, &yoffset, 4); ptr += 4;
		memcpy(ptr, &zoffset, 4); ptr += 4;
		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetBooleani_v_enc(void *self , GLenum target, GLuint index, GLboolean* data)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data =  (sizeof(GLboolean));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetBooleani_v;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &index, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(data, __size_data);
	if (useChecksum) checksumCalculator->addBuffer(data, __size_data);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetBooleani_v: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glMemoryBarrier_enc(void *self , GLbitfield barriers)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glMemoryBarrier;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &barriers, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glMemoryBarrierByRegion_enc(void *self , GLbitfield barriers)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glMemoryBarrierByRegion;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &barriers, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGenProgramPipelines_enc(void *self , GLsizei n, GLuint* pipelines)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_pipelines =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGenProgramPipelines;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_pipelines; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(pipelines, __size_pipelines);
	if (useChecksum) checksumCalculator->addBuffer(pipelines, __size_pipelines);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGenProgramPipelines: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glDeleteProgramPipelines_enc(void *self , GLsizei n, const GLuint* pipelines)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_pipelines =  (n * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_pipelines + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDeleteProgramPipelines;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &n, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_pipelines; ptr += 4;
	memcpy(ptr, pipelines, __size_pipelines);ptr += __size_pipelines;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBindProgramPipeline_enc(void *self , GLuint pipeline)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBindProgramPipeline;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pipeline, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetProgramPipelineiv_enc(void *self , GLuint pipeline, GLenum pname, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetProgramPipelineiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pipeline, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetProgramPipelineiv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetProgramPipelineInfoLog_enc(void *self , GLuint pipeline, GLsizei bufSize, GLsizei* length, GLchar* infoLog)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_length = ((length != NULL) ?  sizeof(GLsizei) : 0);
	const unsigned int __size_infoLog =  bufSize;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetProgramPipelineInfoLog;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pipeline, 4); ptr += 4;
		memcpy(ptr, &bufSize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_length; ptr += 4;
	*(unsigned int *)(ptr) = __size_infoLog; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	if (length != NULL) {
		stream->readback(length, __size_length);
		if (useChecksum) checksumCalculator->addBuffer(length, __size_length);
	}
	stream->readback(infoLog, __size_infoLog);
	if (useChecksum) checksumCalculator->addBuffer(infoLog, __size_infoLog);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetProgramPipelineInfoLog: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glValidateProgramPipeline_enc(void *self , GLuint pipeline)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glValidateProgramPipeline;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pipeline, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

GLboolean glIsProgramPipeline_enc(void *self , GLuint pipeline)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glIsProgramPipeline;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pipeline, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLboolean retval;
	stream->readback(&retval, 1);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 1);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glIsProgramPipeline: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glUseProgramStages_enc(void *self , GLuint pipeline, GLbitfield stages, GLuint program)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUseProgramStages;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pipeline, 4); ptr += 4;
		memcpy(ptr, &stages, 4); ptr += 4;
		memcpy(ptr, &program, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glActiveShaderProgram_enc(void *self , GLuint pipeline, GLuint program)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glActiveShaderProgram;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pipeline, 4); ptr += 4;
		memcpy(ptr, &program, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

GLuint glCreateShaderProgramvAEMU_enc(void *self , GLenum type, GLsizei count, const char* packedStrings, GLuint packedLen)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_packedStrings =  packedLen;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_packedStrings + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glCreateShaderProgramvAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_packedStrings; ptr += 4;
	memcpy(ptr, packedStrings, __size_packedStrings);ptr += __size_packedStrings;
		memcpy(ptr, &packedLen, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLuint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glCreateShaderProgramvAEMU: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glProgramUniform1f_enc(void *self , GLuint program, GLint location, GLfloat v0)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform1f;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &v0, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform2f_enc(void *self , GLuint program, GLint location, GLfloat v0, GLfloat v1)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform2f;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &v0, 4); ptr += 4;
		memcpy(ptr, &v1, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform3f_enc(void *self , GLuint program, GLint location, GLfloat v0, GLfloat v1, GLfloat v2)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform3f;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &v0, 4); ptr += 4;
		memcpy(ptr, &v1, 4); ptr += 4;
		memcpy(ptr, &v2, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform4f_enc(void *self , GLuint program, GLint location, GLfloat v0, GLfloat v1, GLfloat v2, GLfloat v3)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform4f;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &v0, 4); ptr += 4;
		memcpy(ptr, &v1, 4); ptr += 4;
		memcpy(ptr, &v2, 4); ptr += 4;
		memcpy(ptr, &v3, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform1i_enc(void *self , GLuint program, GLint location, GLint v0)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform1i;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &v0, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform2i_enc(void *self , GLuint program, GLint location, GLint v0, GLint v1)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform2i;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &v0, 4); ptr += 4;
		memcpy(ptr, &v1, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform3i_enc(void *self , GLuint program, GLint location, GLint v0, GLint v1, GLint v2)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform3i;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &v0, 4); ptr += 4;
		memcpy(ptr, &v1, 4); ptr += 4;
		memcpy(ptr, &v2, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform4i_enc(void *self , GLuint program, GLint location, GLint v0, GLint v1, GLint v2, GLint v3)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform4i;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &v0, 4); ptr += 4;
		memcpy(ptr, &v1, 4); ptr += 4;
		memcpy(ptr, &v2, 4); ptr += 4;
		memcpy(ptr, &v3, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform1ui_enc(void *self , GLuint program, GLint location, GLuint v0)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform1ui;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &v0, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform2ui_enc(void *self , GLuint program, GLint location, GLint v0, GLuint v1)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform2ui;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &v0, 4); ptr += 4;
		memcpy(ptr, &v1, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform3ui_enc(void *self , GLuint program, GLint location, GLint v0, GLint v1, GLuint v2)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform3ui;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &v0, 4); ptr += 4;
		memcpy(ptr, &v1, 4); ptr += 4;
		memcpy(ptr, &v2, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform4ui_enc(void *self , GLuint program, GLint location, GLint v0, GLint v1, GLint v2, GLuint v3)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform4ui;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &v0, 4); ptr += 4;
		memcpy(ptr, &v1, 4); ptr += 4;
		memcpy(ptr, &v2, 4); ptr += 4;
		memcpy(ptr, &v3, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform1fv_enc(void *self , GLuint program, GLint location, GLsizei count, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform1fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform2fv_enc(void *self , GLuint program, GLint location, GLsizei count, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 2 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform2fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform3fv_enc(void *self , GLuint program, GLint location, GLsizei count, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 3 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform3fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform4fv_enc(void *self , GLuint program, GLint location, GLsizei count, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 4 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform4fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform1iv_enc(void *self , GLuint program, GLint location, GLsizei count, const GLint* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform1iv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform2iv_enc(void *self , GLuint program, GLint location, GLsizei count, const GLint* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 2 * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform2iv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform3iv_enc(void *self , GLuint program, GLint location, GLsizei count, const GLint* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 3 * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform3iv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform4iv_enc(void *self , GLuint program, GLint location, GLsizei count, const GLint* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 4 * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform4iv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform1uiv_enc(void *self , GLuint program, GLint location, GLsizei count, const GLuint* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform1uiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform2uiv_enc(void *self , GLuint program, GLint location, GLsizei count, const GLuint* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 2 * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform2uiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform3uiv_enc(void *self , GLuint program, GLint location, GLsizei count, const GLuint* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 3 * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform3uiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniform4uiv_enc(void *self , GLuint program, GLint location, GLsizei count, const GLuint* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 4 * sizeof(GLuint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniform4uiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniformMatrix2fv_enc(void *self , GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 4 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 1 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniformMatrix2fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &transpose, 1); ptr += 1;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniformMatrix3fv_enc(void *self , GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 9 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 1 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniformMatrix3fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &transpose, 1); ptr += 1;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniformMatrix4fv_enc(void *self , GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 16 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 1 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniformMatrix4fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &transpose, 1); ptr += 1;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniformMatrix2x3fv_enc(void *self , GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 6 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 1 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniformMatrix2x3fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &transpose, 1); ptr += 1;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniformMatrix3x2fv_enc(void *self , GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 6 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 1 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniformMatrix3x2fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &transpose, 1); ptr += 1;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniformMatrix2x4fv_enc(void *self , GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 8 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 1 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniformMatrix2x4fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &transpose, 1); ptr += 1;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniformMatrix4x2fv_enc(void *self , GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 8 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 1 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniformMatrix4x2fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &transpose, 1); ptr += 1;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniformMatrix3x4fv_enc(void *self , GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 12 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 1 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniformMatrix3x4fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &transpose, 1); ptr += 1;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glProgramUniformMatrix4x3fv_enc(void *self , GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_value =  (count * 12 * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 1 + __size_value + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glProgramUniformMatrix4x3fv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &transpose, 1); ptr += 1;
	*(unsigned int *)(ptr) = __size_value; ptr += 4;
	memcpy(ptr, value, __size_value);ptr += __size_value;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetProgramInterfaceiv_enc(void *self , GLuint program, GLenum programInterface, GLenum pname, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetProgramInterfaceiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &programInterface, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetProgramInterfaceiv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetProgramResourceiv_enc(void *self , GLuint program, GLenum programInterface, GLuint index, GLsizei propCount, const GLenum* props, GLsizei bufSize, GLsizei* length, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_props =  (propCount * sizeof(GLenum));
	const unsigned int __size_length = ((length != NULL) ?  (sizeof(GLsizei)) : 0);
	const unsigned int __size_params =  (bufSize * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + __size_props + 4 + 0 + 0 + 3*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetProgramResourceiv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &programInterface, 4); ptr += 4;
		memcpy(ptr, &index, 4); ptr += 4;
		memcpy(ptr, &propCount, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_props; ptr += 4;
	memcpy(ptr, props, __size_props);ptr += __size_props;
		memcpy(ptr, &bufSize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_length; ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	if (length != NULL) {
		stream->readback(length, __size_length);
		if (useChecksum) checksumCalculator->addBuffer(length, __size_length);
	}
	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetProgramResourceiv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

GLuint glGetProgramResourceIndex_enc(void *self , GLuint program, GLenum programInterface, const char* name)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_name =  (strlen(name) + 1);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_name + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetProgramResourceIndex;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &programInterface, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_name; ptr += 4;
	memcpy(ptr, name, __size_name);ptr += __size_name;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLuint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetProgramResourceIndex: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

GLint glGetProgramResourceLocation_enc(void *self , GLuint program, GLenum programInterface, const char* name)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_name =  (strlen(name) + 1);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_name + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetProgramResourceLocation;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &programInterface, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_name; ptr += 4;
	memcpy(ptr, name, __size_name);ptr += __size_name;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLint retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetProgramResourceLocation: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glGetProgramResourceName_enc(void *self , GLuint program, GLenum programInterface, GLuint index, GLsizei bufSize, GLsizei* length, char* name)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_length = ((length != NULL) ?  (sizeof(GLsizei)) : 0);
	const unsigned int __size_name =  bufSize;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 0 + 0 + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetProgramResourceName;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &programInterface, 4); ptr += 4;
		memcpy(ptr, &index, 4); ptr += 4;
		memcpy(ptr, &bufSize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_length; ptr += 4;
	*(unsigned int *)(ptr) = __size_name; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	if (length != NULL) {
		stream->readback(length, __size_length);
		if (useChecksum) checksumCalculator->addBuffer(length, __size_length);
	}
	stream->readback(name, __size_name);
	if (useChecksum) checksumCalculator->addBuffer(name, __size_name);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetProgramResourceName: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glBindImageTexture_enc(void *self , GLuint unit, GLuint texture, GLint level, GLboolean layered, GLint layer, GLenum access, GLenum format)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 1 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBindImageTexture;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &unit, 4); ptr += 4;
		memcpy(ptr, &texture, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &layered, 1); ptr += 1;
		memcpy(ptr, &layer, 4); ptr += 4;
		memcpy(ptr, &access, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDispatchCompute_enc(void *self , GLuint num_groups_x, GLuint num_groups_y, GLuint num_groups_z)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDispatchCompute;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &num_groups_x, 4); ptr += 4;
		memcpy(ptr, &num_groups_y, 4); ptr += 4;
		memcpy(ptr, &num_groups_z, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDispatchComputeIndirect_enc(void *self , GLintptr indirect)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDispatchComputeIndirect;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &indirect, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glBindVertexBuffer_enc(void *self , GLuint bindingindex, GLuint buffer, GLintptr offset, GLintptr stride)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glBindVertexBuffer;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &bindingindex, 4); ptr += 4;
		memcpy(ptr, &buffer, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;
		memcpy(ptr, &stride, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexAttribBinding_enc(void *self , GLuint attribindex, GLuint bindingindex)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexAttribBinding;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &attribindex, 4); ptr += 4;
		memcpy(ptr, &bindingindex, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexAttribFormat_enc(void *self , GLuint attribindex, GLint size, GLenum type, GLboolean normalized, GLuint relativeoffset)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 1 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexAttribFormat;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &attribindex, 4); ptr += 4;
		memcpy(ptr, &size, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &normalized, 1); ptr += 1;
		memcpy(ptr, &relativeoffset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexAttribIFormat_enc(void *self , GLuint attribindex, GLint size, GLenum type, GLuint relativeoffset)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexAttribIFormat;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &attribindex, 4); ptr += 4;
		memcpy(ptr, &size, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &relativeoffset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glVertexBindingDivisor_enc(void *self , GLuint bindingindex, GLuint divisor)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glVertexBindingDivisor;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &bindingindex, 4); ptr += 4;
		memcpy(ptr, &divisor, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawArraysIndirectDataAEMU_enc(void *self , GLenum mode, const void* indirect, GLuint datalen)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_indirect =  datalen;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + __size_indirect + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawArraysIndirectDataAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_indirect; ptr += 4;
	memcpy(ptr, indirect, __size_indirect);ptr += __size_indirect;
		memcpy(ptr, &datalen, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawArraysIndirectOffsetAEMU_enc(void *self , GLenum mode, GLuint offset)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawArraysIndirectOffsetAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawElementsIndirectDataAEMU_enc(void *self , GLenum mode, GLenum type, const void* indirect, GLuint datalen)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_indirect =  datalen;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + __size_indirect + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawElementsIndirectDataAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_indirect; ptr += 4;
	memcpy(ptr, indirect, __size_indirect);ptr += __size_indirect;
		memcpy(ptr, &datalen, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawElementsIndirectOffsetAEMU_enc(void *self , GLenum mode, GLenum type, GLuint offset)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawElementsIndirectOffsetAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glTexStorage2DMultisample_enc(void *self , GLenum target, GLsizei samples, GLenum internalformat, GLsizei width, GLsizei height, GLboolean fixedsamplelocations)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 1;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glTexStorage2DMultisample;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &samples, 4); ptr += 4;
		memcpy(ptr, &internalformat, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &fixedsamplelocations, 1); ptr += 1;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glSampleMaski_enc(void *self , GLuint maskNumber, GLbitfield mask)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glSampleMaski;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &maskNumber, 4); ptr += 4;
		memcpy(ptr, &mask, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetMultisamplefv_enc(void *self , GLenum pname, GLuint index, GLfloat* val)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_val =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetMultisamplefv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &index, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_val; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(val, __size_val);
	if (useChecksum) checksumCalculator->addBuffer(val, __size_val);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetMultisamplefv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glFramebufferParameteri_enc(void *self , GLenum target, GLenum pname, GLint param)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFramebufferParameteri;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
		memcpy(ptr, &param, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glGetFramebufferParameteriv_enc(void *self , GLenum target, GLenum pname, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetFramebufferParameteriv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetFramebufferParameteriv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetTexLevelParameterfv_enc(void *self , GLenum target, GLint level, GLenum pname, GLfloat* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLfloat));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetTexLevelParameterfv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetTexLevelParameterfv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetTexLevelParameteriv_enc(void *self , GLenum target, GLint level, GLenum pname, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  (glUtilsParamSize(pname) * sizeof(GLint));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetTexLevelParameteriv;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &level, 4); ptr += 4;
		memcpy(ptr, &pname, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetTexLevelParameteriv: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glMapBufferRangeDMA_enc(void *self , GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, uint64_t paddr)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glMapBufferRangeDMA;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;
		memcpy(ptr, &length, 4); ptr += 4;
		memcpy(ptr, &access, 4); ptr += 4;
		memcpy(ptr, &paddr, 8); ptr += 8;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUnmapBufferDMA_enc(void *self , GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, uint64_t paddr, GLboolean* out_res)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_out_res =  (sizeof(GLboolean));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 8 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUnmapBufferDMA;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;
		memcpy(ptr, &length, 4); ptr += 4;
		memcpy(ptr, &access, 4); ptr += 4;
		memcpy(ptr, &paddr, 8); ptr += 8;
	*(unsigned int *)(ptr) = __size_out_res; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(out_res, __size_out_res);
	if (useChecksum) checksumCalculator->addBuffer(out_res, __size_out_res);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glUnmapBufferDMA: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

uint64_t glMapBufferRangeDirect_enc(void *self , GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, uint64_t paddr)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glMapBufferRangeDirect;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;
		memcpy(ptr, &length, 4); ptr += 4;
		memcpy(ptr, &access, 4); ptr += 4;
		memcpy(ptr, &paddr, 8); ptr += 8;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	uint64_t retval;
	stream->readback(&retval, 8);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 8);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glMapBufferRangeDirect: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glUnmapBufferDirect_enc(void *self , GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, uint64_t paddr, uint64_t guest_ptr, GLboolean* out_res)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_out_res =  (sizeof(GLboolean));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 8 + 8 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glUnmapBufferDirect;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;
		memcpy(ptr, &length, 4); ptr += 4;
		memcpy(ptr, &access, 4); ptr += 4;
		memcpy(ptr, &paddr, 8); ptr += 8;
		memcpy(ptr, &guest_ptr, 8); ptr += 8;
	*(unsigned int *)(ptr) = __size_out_res; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(out_res, __size_out_res);
	if (useChecksum) checksumCalculator->addBuffer(out_res, __size_out_res);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glUnmapBufferDirect: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glFlushMappedBufferRangeDirect_enc(void *self , GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glFlushMappedBufferRangeDirect;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;
		memcpy(ptr, &length, 4); ptr += 4;
		memcpy(ptr, &access, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

GLenum glGetGraphicsResetStatusEXT_enc(void *self )
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetGraphicsResetStatusEXT;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;


	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;


	GLenum retval;
	stream->readback(&retval, 4);
	if (useChecksum) checksumCalculator->addBuffer(&retval, 4);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetGraphicsResetStatusEXT: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
	return retval;
}

void glReadnPixelsEXT_enc(void *self , GLint x, GLint y, GLsizei width, GLsizei height, GLenum format, GLenum type, GLsizei bufSize, GLvoid* data)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data =  bufSize;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + 4 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glReadnPixelsEXT;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &x, 4); ptr += 4;
		memcpy(ptr, &y, 4); ptr += 4;
		memcpy(ptr, &width, 4); ptr += 4;
		memcpy(ptr, &height, 4); ptr += 4;
		memcpy(ptr, &format, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &bufSize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(data, __size_data);
	if (useChecksum) checksumCalculator->addBuffer(data, __size_data);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glReadnPixelsEXT: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetnUniformfvEXT_enc(void *self , GLuint program, GLint location, GLsizei bufSize, GLfloat* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  bufSize;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetnUniformfvEXT;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &bufSize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetnUniformfvEXT: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glGetnUniformivEXT_enc(void *self , GLuint program, GLint location, GLsizei bufSize, GLint* params)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_params =  bufSize;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 0 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glGetnUniformivEXT;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &program, 4); ptr += 4;
		memcpy(ptr, &location, 4); ptr += 4;
		memcpy(ptr, &bufSize, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_params; ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

	stream->readback(params, __size_params);
	if (useChecksum) checksumCalculator->addBuffer(params, __size_params);
	if (useChecksum) {
		unsigned char *checksumBufPtr = NULL;
		unsigned char checksumBuf[ChecksumCalculator::kMaxChecksumSize];
		if (checksumSize > 0) checksumBufPtr = &checksumBuf[0];
		stream->readback(checksumBufPtr, checksumSize);
		if (!checksumCalculator->validate(checksumBufPtr, checksumSize)) {
			ALOGE("glGetnUniformivEXT: GL communication error, please report this issue to b.android.com.\n");
			abort();
		}
	}
}

void glDrawArraysNullAEMU_enc(void *self , GLenum mode, GLint first, GLsizei count)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawArraysNullAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;
		memcpy(ptr, &first, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawElementsOffsetNullAEMU_enc(void *self , GLenum mode, GLsizei count, GLenum type, GLuint offset)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawElementsOffsetNullAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glDrawElementsDataNullAEMU_enc(void *self , GLenum mode, GLsizei count, GLenum type, void* data, GLuint datalen)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_data =  datalen;
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + __size_data + 4 + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(totalSize);
	ptr = buf;
	int tmp = OP_glDrawElementsDataNullAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &mode, 4); ptr += 4;
		memcpy(ptr, &count, 4); ptr += 4;
		memcpy(ptr, &type, 4); ptr += 4;
	*(unsigned int *)(ptr) = __size_data; ptr += 4;
	memcpy(ptr, data, __size_data);ptr += __size_data;
		memcpy(ptr, &datalen, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	if (useChecksum) checksumCalculator->writeChecksum(ptr, checksumSize); ptr += checksumSize;

}

void glUnmapBufferAsyncAEMU_enc(void *self , GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, void* guest_buffer, GLboolean* out_res)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_guest_buffer = ((guest_buffer != NULL) ?  length : 0);
	const unsigned int __size_out_res =  (sizeof(GLboolean));
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + __size_guest_buffer + __size_out_res + 2*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(8 + 4 + 4 + 4 + 4);
	ptr = buf;
	int tmp = OP_glUnmapBufferAsyncAEMU;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;
		memcpy(ptr, &length, 4); ptr += 4;
		memcpy(ptr, &access, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	stream->flush();
	stream->writeFully(&__size_guest_buffer,4);
	if (useChecksum) checksumCalculator->addBuffer(&__size_guest_buffer,4);
	if (guest_buffer != NULL) {
		stream->writeFully(guest_buffer, __size_guest_buffer);
		if (useChecksum) checksumCalculator->addBuffer(guest_buffer, __size_guest_buffer);
	}
	buf = stream->alloc(__size_out_res + 1*4);
	ptr = buf;
	*(unsigned int *)(ptr) = __size_out_res; ptr += 4;
	memcpy(ptr, out_res, __size_out_res);ptr += __size_out_res;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	buf = stream->alloc(checksumSize);
	if (useChecksum) checksumCalculator->writeChecksum(buf, checksumSize);

}

void glFlushMappedBufferRangeAEMU2_enc(void *self , GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, void* guest_buffer)
{

	gl2_encoder_context_t *ctx = (gl2_encoder_context_t *)self;
	IOStream *stream = ctx->m_stream;
	ChecksumCalculator *checksumCalculator = ctx->m_checksumCalculator;
	bool useChecksum = checksumCalculator->getVersion() > 0;

	const unsigned int __size_guest_buffer = ((guest_buffer != NULL) ?  length : 0);
	 unsigned char *ptr;
	 unsigned char *buf;
	 const size_t sizeWithoutChecksum = 8 + 4 + 4 + 4 + 4 + __size_guest_buffer + 1*4;
	 const size_t checksumSize = checksumCalculator->checksumByteSize();
	 const size_t totalSize = sizeWithoutChecksum + checksumSize;
	buf = stream->alloc(8 + 4 + 4 + 4 + 4);
	ptr = buf;
	int tmp = OP_glFlushMappedBufferRangeAEMU2;memcpy(ptr, &tmp, 4); ptr += 4;
	memcpy(ptr, &totalSize, 4);  ptr += 4;

		memcpy(ptr, &target, 4); ptr += 4;
		memcpy(ptr, &offset, 4); ptr += 4;
		memcpy(ptr, &length, 4); ptr += 4;
		memcpy(ptr, &access, 4); ptr += 4;

	if (useChecksum) checksumCalculator->addBuffer(buf, ptr-buf);
	stream->flush();
	stream->writeFully(&__size_guest_buffer,4);
	if (useChecksum) checksumCalculator->addBuffer(&__size_guest_buffer,4);
	if (guest_buffer != NULL) {
		stream->writeFully(guest_buffer, __size_guest_buffer);
		if (useChecksum) checksumCalculator->addBuffer(guest_buffer, __size_guest_buffer);
	}
	buf = stream->alloc(checksumSize);
	if (useChecksum) checksumCalculator->writeChecksum(buf, checksumSize);

}

}  // namespace

gl2_encoder_context_t::gl2_encoder_context_t(IOStream *stream, ChecksumCalculator *checksumCalculator)
{
	m_stream = stream;
	m_checksumCalculator = checksumCalculator;

	this->glActiveTexture = &glActiveTexture_enc;
	this->glAttachShader = &glAttachShader_enc;
	this->glBindAttribLocation = &glBindAttribLocation_enc;
	this->glBindBuffer = &glBindBuffer_enc;
	this->glBindFramebuffer = &glBindFramebuffer_enc;
	this->glBindRenderbuffer = &glBindRenderbuffer_enc;
	this->glBindTexture = &glBindTexture_enc;
	this->glBlendColor = &glBlendColor_enc;
	this->glBlendEquation = &glBlendEquation_enc;
	this->glBlendEquationSeparate = &glBlendEquationSeparate_enc;
	this->glBlendFunc = &glBlendFunc_enc;
	this->glBlendFuncSeparate = &glBlendFuncSeparate_enc;
	this->glBufferData = &glBufferData_enc;
	this->glBufferSubData = &glBufferSubData_enc;
	this->glCheckFramebufferStatus = &glCheckFramebufferStatus_enc;
	this->glClear = &glClear_enc;
	this->glClearColor = &glClearColor_enc;
	this->glClearDepthf = &glClearDepthf_enc;
	this->glClearStencil = &glClearStencil_enc;
	this->glColorMask = &glColorMask_enc;
	this->glCompileShader = &glCompileShader_enc;
	this->glCompressedTexImage2D = &glCompressedTexImage2D_enc;
	this->glCompressedTexSubImage2D = &glCompressedTexSubImage2D_enc;
	this->glCopyTexImage2D = &glCopyTexImage2D_enc;
	this->glCopyTexSubImage2D = &glCopyTexSubImage2D_enc;
	this->glCreateProgram = &glCreateProgram_enc;
	this->glCreateShader = &glCreateShader_enc;
	this->glCullFace = &glCullFace_enc;
	this->glDeleteBuffers = &glDeleteBuffers_enc;
	this->glDeleteFramebuffers = &glDeleteFramebuffers_enc;
	this->glDeleteProgram = &glDeleteProgram_enc;
	this->glDeleteRenderbuffers = &glDeleteRenderbuffers_enc;
	this->glDeleteShader = &glDeleteShader_enc;
	this->glDeleteTextures = &glDeleteTextures_enc;
	this->glDepthFunc = &glDepthFunc_enc;
	this->glDepthMask = &glDepthMask_enc;
	this->glDepthRangef = &glDepthRangef_enc;
	this->glDetachShader = &glDetachShader_enc;
	this->glDisable = &glDisable_enc;
	this->glDisableVertexAttribArray = &glDisableVertexAttribArray_enc;
	this->glDrawArrays = &glDrawArrays_enc;
	this->glDrawElements = (glDrawElements_client_proc_t) &enc_unsupported;
	this->glEnable = &glEnable_enc;
	this->glEnableVertexAttribArray = &glEnableVertexAttribArray_enc;
	this->glFinish = &glFinish_enc;
	this->glFlush = &glFlush_enc;
	this->glFramebufferRenderbuffer = &glFramebufferRenderbuffer_enc;
	this->glFramebufferTexture2D = &glFramebufferTexture2D_enc;
	this->glFrontFace = &glFrontFace_enc;
	this->glGenBuffers = &glGenBuffers_enc;
	this->glGenerateMipmap = &glGenerateMipmap_enc;
	this->glGenFramebuffers = &glGenFramebuffers_enc;
	this->glGenRenderbuffers = &glGenRenderbuffers_enc;
	this->glGenTextures = &glGenTextures_enc;
	this->glGetActiveAttrib = &glGetActiveAttrib_enc;
	this->glGetActiveUniform = &glGetActiveUniform_enc;
	this->glGetAttachedShaders = &glGetAttachedShaders_enc;
	this->glGetAttribLocation = &glGetAttribLocation_enc;
	this->glGetBooleanv = &glGetBooleanv_enc;
	this->glGetBufferParameteriv = &glGetBufferParameteriv_enc;
	this->glGetError = &glGetError_enc;
	this->glGetFloatv = &glGetFloatv_enc;
	this->glGetFramebufferAttachmentParameteriv = &glGetFramebufferAttachmentParameteriv_enc;
	this->glGetIntegerv = &glGetIntegerv_enc;
	this->glGetProgramiv = &glGetProgramiv_enc;
	this->glGetProgramInfoLog = &glGetProgramInfoLog_enc;
	this->glGetRenderbufferParameteriv = &glGetRenderbufferParameteriv_enc;
	this->glGetShaderiv = &glGetShaderiv_enc;
	this->glGetShaderInfoLog = &glGetShaderInfoLog_enc;
	this->glGetShaderPrecisionFormat = &glGetShaderPrecisionFormat_enc;
	this->glGetShaderSource = &glGetShaderSource_enc;
	this->glGetString = (glGetString_client_proc_t) &enc_unsupported;
	this->glGetTexParameterfv = &glGetTexParameterfv_enc;
	this->glGetTexParameteriv = &glGetTexParameteriv_enc;
	this->glGetUniformfv = &glGetUniformfv_enc;
	this->glGetUniformiv = &glGetUniformiv_enc;
	this->glGetUniformLocation = &glGetUniformLocation_enc;
	this->glGetVertexAttribfv = &glGetVertexAttribfv_enc;
	this->glGetVertexAttribiv = &glGetVertexAttribiv_enc;
	this->glGetVertexAttribPointerv = (glGetVertexAttribPointerv_client_proc_t) &enc_unsupported;
	this->glHint = &glHint_enc;
	this->glIsBuffer = &glIsBuffer_enc;
	this->glIsEnabled = &glIsEnabled_enc;
	this->glIsFramebuffer = &glIsFramebuffer_enc;
	this->glIsProgram = &glIsProgram_enc;
	this->glIsRenderbuffer = &glIsRenderbuffer_enc;
	this->glIsShader = &glIsShader_enc;
	this->glIsTexture = &glIsTexture_enc;
	this->glLineWidth = &glLineWidth_enc;
	this->glLinkProgram = &glLinkProgram_enc;
	this->glPixelStorei = &glPixelStorei_enc;
	this->glPolygonOffset = &glPolygonOffset_enc;
	this->glReadPixels = &glReadPixels_enc;
	this->glReleaseShaderCompiler = &glReleaseShaderCompiler_enc;
	this->glRenderbufferStorage = &glRenderbufferStorage_enc;
	this->glSampleCoverage = &glSampleCoverage_enc;
	this->glScissor = &glScissor_enc;
	this->glShaderBinary = (glShaderBinary_client_proc_t) &enc_unsupported;
	this->glShaderSource = (glShaderSource_client_proc_t) &enc_unsupported;
	this->glStencilFunc = &glStencilFunc_enc;
	this->glStencilFuncSeparate = &glStencilFuncSeparate_enc;
	this->glStencilMask = &glStencilMask_enc;
	this->glStencilMaskSeparate = &glStencilMaskSeparate_enc;
	this->glStencilOp = &glStencilOp_enc;
	this->glStencilOpSeparate = &glStencilOpSeparate_enc;
	this->glTexImage2D = &glTexImage2D_enc;
	this->glTexParameterf = &glTexParameterf_enc;
	this->glTexParameterfv = &glTexParameterfv_enc;
	this->glTexParameteri = &glTexParameteri_enc;
	this->glTexParameteriv = &glTexParameteriv_enc;
	this->glTexSubImage2D = &glTexSubImage2D_enc;
	this->glUniform1f = &glUniform1f_enc;
	this->glUniform1fv = &glUniform1fv_enc;
	this->glUniform1i = &glUniform1i_enc;
	this->glUniform1iv = &glUniform1iv_enc;
	this->glUniform2f = &glUniform2f_enc;
	this->glUniform2fv = &glUniform2fv_enc;
	this->glUniform2i = &glUniform2i_enc;
	this->glUniform2iv = &glUniform2iv_enc;
	this->glUniform3f = &glUniform3f_enc;
	this->glUniform3fv = &glUniform3fv_enc;
	this->glUniform3i = &glUniform3i_enc;
	this->glUniform3iv = &glUniform3iv_enc;
	this->glUniform4f = &glUniform4f_enc;
	this->glUniform4fv = &glUniform4fv_enc;
	this->glUniform4i = &glUniform4i_enc;
	this->glUniform4iv = &glUniform4iv_enc;
	this->glUniformMatrix2fv = &glUniformMatrix2fv_enc;
	this->glUniformMatrix3fv = &glUniformMatrix3fv_enc;
	this->glUniformMatrix4fv = &glUniformMatrix4fv_enc;
	this->glUseProgram = &glUseProgram_enc;
	this->glValidateProgram = &glValidateProgram_enc;
	this->glVertexAttrib1f = &glVertexAttrib1f_enc;
	this->glVertexAttrib1fv = &glVertexAttrib1fv_enc;
	this->glVertexAttrib2f = &glVertexAttrib2f_enc;
	this->glVertexAttrib2fv = &glVertexAttrib2fv_enc;
	this->glVertexAttrib3f = &glVertexAttrib3f_enc;
	this->glVertexAttrib3fv = &glVertexAttrib3fv_enc;
	this->glVertexAttrib4f = &glVertexAttrib4f_enc;
	this->glVertexAttrib4fv = &glVertexAttrib4fv_enc;
	this->glVertexAttribPointer = (glVertexAttribPointer_client_proc_t) &enc_unsupported;
	this->glViewport = &glViewport_enc;
	this->glEGLImageTargetTexture2DOES = &glEGLImageTargetTexture2DOES_enc;
	this->glEGLImageTargetRenderbufferStorageOES = &glEGLImageTargetRenderbufferStorageOES_enc;
	this->glGetProgramBinaryOES = (glGetProgramBinaryOES_client_proc_t) &enc_unsupported;
	this->glProgramBinaryOES = (glProgramBinaryOES_client_proc_t) &enc_unsupported;
	this->glMapBufferOES = (glMapBufferOES_client_proc_t) &enc_unsupported;
	this->glUnmapBufferOES = &glUnmapBufferOES_enc;
	this->glTexImage3DOES = &glTexImage3DOES_enc;
	this->glTexSubImage3DOES = &glTexSubImage3DOES_enc;
	this->glCopyTexSubImage3DOES = &glCopyTexSubImage3DOES_enc;
	this->glCompressedTexImage3DOES = &glCompressedTexImage3DOES_enc;
	this->glCompressedTexSubImage3DOES = &glCompressedTexSubImage3DOES_enc;
	this->glFramebufferTexture3DOES = &glFramebufferTexture3DOES_enc;
	this->glBindVertexArrayOES = &glBindVertexArrayOES_enc;
	this->glDeleteVertexArraysOES = &glDeleteVertexArraysOES_enc;
	this->glGenVertexArraysOES = &glGenVertexArraysOES_enc;
	this->glIsVertexArrayOES = &glIsVertexArrayOES_enc;
	this->glDiscardFramebufferEXT = &glDiscardFramebufferEXT_enc;
	this->glMultiDrawArraysEXT = (glMultiDrawArraysEXT_client_proc_t) &enc_unsupported;
	this->glMultiDrawElementsEXT = (glMultiDrawElementsEXT_client_proc_t) &enc_unsupported;
	this->glGetPerfMonitorGroupsAMD = (glGetPerfMonitorGroupsAMD_client_proc_t) &enc_unsupported;
	this->glGetPerfMonitorCountersAMD = (glGetPerfMonitorCountersAMD_client_proc_t) &enc_unsupported;
	this->glGetPerfMonitorGroupStringAMD = (glGetPerfMonitorGroupStringAMD_client_proc_t) &enc_unsupported;
	this->glGetPerfMonitorCounterStringAMD = (glGetPerfMonitorCounterStringAMD_client_proc_t) &enc_unsupported;
	this->glGetPerfMonitorCounterInfoAMD = (glGetPerfMonitorCounterInfoAMD_client_proc_t) &enc_unsupported;
	this->glGenPerfMonitorsAMD = (glGenPerfMonitorsAMD_client_proc_t) &enc_unsupported;
	this->glDeletePerfMonitorsAMD = (glDeletePerfMonitorsAMD_client_proc_t) &enc_unsupported;
	this->glSelectPerfMonitorCountersAMD = (glSelectPerfMonitorCountersAMD_client_proc_t) &enc_unsupported;
	this->glBeginPerfMonitorAMD = (glBeginPerfMonitorAMD_client_proc_t) &enc_unsupported;
	this->glEndPerfMonitorAMD = (glEndPerfMonitorAMD_client_proc_t) &enc_unsupported;
	this->glGetPerfMonitorCounterDataAMD = (glGetPerfMonitorCounterDataAMD_client_proc_t) &enc_unsupported;
	this->glRenderbufferStorageMultisampleIMG = (glRenderbufferStorageMultisampleIMG_client_proc_t) &enc_unsupported;
	this->glFramebufferTexture2DMultisampleIMG = (glFramebufferTexture2DMultisampleIMG_client_proc_t) &enc_unsupported;
	this->glDeleteFencesNV = (glDeleteFencesNV_client_proc_t) &enc_unsupported;
	this->glGenFencesNV = (glGenFencesNV_client_proc_t) &enc_unsupported;
	this->glIsFenceNV = (glIsFenceNV_client_proc_t) &enc_unsupported;
	this->glTestFenceNV = (glTestFenceNV_client_proc_t) &enc_unsupported;
	this->glGetFenceivNV = (glGetFenceivNV_client_proc_t) &enc_unsupported;
	this->glFinishFenceNV = (glFinishFenceNV_client_proc_t) &enc_unsupported;
	this->glSetFenceNV = (glSetFenceNV_client_proc_t) &enc_unsupported;
	this->glCoverageMaskNV = (glCoverageMaskNV_client_proc_t) &enc_unsupported;
	this->glCoverageOperationNV = (glCoverageOperationNV_client_proc_t) &enc_unsupported;
	this->glGetDriverControlsQCOM = (glGetDriverControlsQCOM_client_proc_t) &enc_unsupported;
	this->glGetDriverControlStringQCOM = (glGetDriverControlStringQCOM_client_proc_t) &enc_unsupported;
	this->glEnableDriverControlQCOM = (glEnableDriverControlQCOM_client_proc_t) &enc_unsupported;
	this->glDisableDriverControlQCOM = (glDisableDriverControlQCOM_client_proc_t) &enc_unsupported;
	this->glExtGetTexturesQCOM = (glExtGetTexturesQCOM_client_proc_t) &enc_unsupported;
	this->glExtGetBuffersQCOM = (glExtGetBuffersQCOM_client_proc_t) &enc_unsupported;
	this->glExtGetRenderbuffersQCOM = (glExtGetRenderbuffersQCOM_client_proc_t) &enc_unsupported;
	this->glExtGetFramebuffersQCOM = (glExtGetFramebuffersQCOM_client_proc_t) &enc_unsupported;
	this->glExtGetTexLevelParameterivQCOM = (glExtGetTexLevelParameterivQCOM_client_proc_t) &enc_unsupported;
	this->glExtTexObjectStateOverrideiQCOM = (glExtTexObjectStateOverrideiQCOM_client_proc_t) &enc_unsupported;
	this->glExtGetTexSubImageQCOM = (glExtGetTexSubImageQCOM_client_proc_t) &enc_unsupported;
	this->glExtGetBufferPointervQCOM = (glExtGetBufferPointervQCOM_client_proc_t) &enc_unsupported;
	this->glExtGetShadersQCOM = (glExtGetShadersQCOM_client_proc_t) &enc_unsupported;
	this->glExtGetProgramsQCOM = (glExtGetProgramsQCOM_client_proc_t) &enc_unsupported;
	this->glExtIsProgramBinaryQCOM = (glExtIsProgramBinaryQCOM_client_proc_t) &enc_unsupported;
	this->glExtGetProgramBinarySourceQCOM = (glExtGetProgramBinarySourceQCOM_client_proc_t) &enc_unsupported;
	this->glStartTilingQCOM = (glStartTilingQCOM_client_proc_t) &enc_unsupported;
	this->glEndTilingQCOM = (glEndTilingQCOM_client_proc_t) &enc_unsupported;
	this->glVertexAttribPointerData = &glVertexAttribPointerData_enc;
	this->glVertexAttribPointerOffset = &glVertexAttribPointerOffset_enc;
	this->glDrawElementsOffset = &glDrawElementsOffset_enc;
	this->glDrawElementsData = &glDrawElementsData_enc;
	this->glGetCompressedTextureFormats = &glGetCompressedTextureFormats_enc;
	this->glShaderString = &glShaderString_enc;
	this->glFinishRoundTrip = &glFinishRoundTrip_enc;
	this->glGenVertexArrays = &glGenVertexArrays_enc;
	this->glBindVertexArray = &glBindVertexArray_enc;
	this->glDeleteVertexArrays = &glDeleteVertexArrays_enc;
	this->glIsVertexArray = &glIsVertexArray_enc;
	this->glMapBufferRange = (glMapBufferRange_client_proc_t) &enc_unsupported;
	this->glUnmapBuffer = (glUnmapBuffer_client_proc_t) &enc_unsupported;
	this->glFlushMappedBufferRange = (glFlushMappedBufferRange_client_proc_t) &enc_unsupported;
	this->glMapBufferRangeAEMU = &glMapBufferRangeAEMU_enc;
	this->glUnmapBufferAEMU = &glUnmapBufferAEMU_enc;
	this->glFlushMappedBufferRangeAEMU = &glFlushMappedBufferRangeAEMU_enc;
	this->glReadPixelsOffsetAEMU = &glReadPixelsOffsetAEMU_enc;
	this->glCompressedTexImage2DOffsetAEMU = &glCompressedTexImage2DOffsetAEMU_enc;
	this->glCompressedTexSubImage2DOffsetAEMU = &glCompressedTexSubImage2DOffsetAEMU_enc;
	this->glTexImage2DOffsetAEMU = &glTexImage2DOffsetAEMU_enc;
	this->glTexSubImage2DOffsetAEMU = &glTexSubImage2DOffsetAEMU_enc;
	this->glBindBufferRange = &glBindBufferRange_enc;
	this->glBindBufferBase = &glBindBufferBase_enc;
	this->glCopyBufferSubData = &glCopyBufferSubData_enc;
	this->glClearBufferiv = &glClearBufferiv_enc;
	this->glClearBufferuiv = &glClearBufferuiv_enc;
	this->glClearBufferfv = &glClearBufferfv_enc;
	this->glClearBufferfi = &glClearBufferfi_enc;
	this->glGetBufferParameteri64v = (glGetBufferParameteri64v_client_proc_t) &enc_unsupported;
	this->glGetBufferPointerv = (glGetBufferPointerv_client_proc_t) &enc_unsupported;
	this->glUniformBlockBinding = &glUniformBlockBinding_enc;
	this->glGetUniformBlockIndex = &glGetUniformBlockIndex_enc;
	this->glGetUniformIndices = (glGetUniformIndices_client_proc_t) &enc_unsupported;
	this->glGetUniformIndicesAEMU = &glGetUniformIndicesAEMU_enc;
	this->glGetActiveUniformBlockiv = &glGetActiveUniformBlockiv_enc;
	this->glGetActiveUniformBlockName = &glGetActiveUniformBlockName_enc;
	this->glUniform1ui = &glUniform1ui_enc;
	this->glUniform2ui = &glUniform2ui_enc;
	this->glUniform3ui = &glUniform3ui_enc;
	this->glUniform4ui = &glUniform4ui_enc;
	this->glUniform1uiv = &glUniform1uiv_enc;
	this->glUniform2uiv = &glUniform2uiv_enc;
	this->glUniform3uiv = &glUniform3uiv_enc;
	this->glUniform4uiv = &glUniform4uiv_enc;
	this->glUniformMatrix2x3fv = &glUniformMatrix2x3fv_enc;
	this->glUniformMatrix3x2fv = &glUniformMatrix3x2fv_enc;
	this->glUniformMatrix2x4fv = &glUniformMatrix2x4fv_enc;
	this->glUniformMatrix4x2fv = &glUniformMatrix4x2fv_enc;
	this->glUniformMatrix3x4fv = &glUniformMatrix3x4fv_enc;
	this->glUniformMatrix4x3fv = &glUniformMatrix4x3fv_enc;
	this->glGetUniformuiv = &glGetUniformuiv_enc;
	this->glGetActiveUniformsiv = &glGetActiveUniformsiv_enc;
	this->glVertexAttribI4i = &glVertexAttribI4i_enc;
	this->glVertexAttribI4ui = &glVertexAttribI4ui_enc;
	this->glVertexAttribI4iv = &glVertexAttribI4iv_enc;
	this->glVertexAttribI4uiv = &glVertexAttribI4uiv_enc;
	this->glVertexAttribIPointer = (glVertexAttribIPointer_client_proc_t) &enc_unsupported;
	this->glVertexAttribIPointerOffsetAEMU = &glVertexAttribIPointerOffsetAEMU_enc;
	this->glVertexAttribIPointerDataAEMU = &glVertexAttribIPointerDataAEMU_enc;
	this->glGetVertexAttribIiv = &glGetVertexAttribIiv_enc;
	this->glGetVertexAttribIuiv = &glGetVertexAttribIuiv_enc;
	this->glVertexAttribDivisor = &glVertexAttribDivisor_enc;
	this->glDrawArraysInstanced = &glDrawArraysInstanced_enc;
	this->glDrawElementsInstanced = (glDrawElementsInstanced_client_proc_t) &enc_unsupported;
	this->glDrawElementsInstancedDataAEMU = &glDrawElementsInstancedDataAEMU_enc;
	this->glDrawElementsInstancedOffsetAEMU = &glDrawElementsInstancedOffsetAEMU_enc;
	this->glDrawRangeElements = (glDrawRangeElements_client_proc_t) &enc_unsupported;
	this->glDrawRangeElementsDataAEMU = &glDrawRangeElementsDataAEMU_enc;
	this->glDrawRangeElementsOffsetAEMU = &glDrawRangeElementsOffsetAEMU_enc;
	this->glFenceSync = (glFenceSync_client_proc_t) &enc_unsupported;
	this->glClientWaitSync = (glClientWaitSync_client_proc_t) &enc_unsupported;
	this->glWaitSync = (glWaitSync_client_proc_t) &enc_unsupported;
	this->glDeleteSync = (glDeleteSync_client_proc_t) &enc_unsupported;
	this->glIsSync = (glIsSync_client_proc_t) &enc_unsupported;
	this->glGetSynciv = (glGetSynciv_client_proc_t) &enc_unsupported;
	this->glFenceSyncAEMU = &glFenceSyncAEMU_enc;
	this->glClientWaitSyncAEMU = &glClientWaitSyncAEMU_enc;
	this->glWaitSyncAEMU = &glWaitSyncAEMU_enc;
	this->glDeleteSyncAEMU = &glDeleteSyncAEMU_enc;
	this->glIsSyncAEMU = &glIsSyncAEMU_enc;
	this->glGetSyncivAEMU = &glGetSyncivAEMU_enc;
	this->glDrawBuffers = &glDrawBuffers_enc;
	this->glReadBuffer = &glReadBuffer_enc;
	this->glBlitFramebuffer = &glBlitFramebuffer_enc;
	this->glInvalidateFramebuffer = &glInvalidateFramebuffer_enc;
	this->glInvalidateSubFramebuffer = &glInvalidateSubFramebuffer_enc;
	this->glFramebufferTextureLayer = &glFramebufferTextureLayer_enc;
	this->glRenderbufferStorageMultisample = &glRenderbufferStorageMultisample_enc;
	this->glTexStorage2D = &glTexStorage2D_enc;
	this->glGetInternalformativ = &glGetInternalformativ_enc;
	this->glBeginTransformFeedback = &glBeginTransformFeedback_enc;
	this->glEndTransformFeedback = &glEndTransformFeedback_enc;
	this->glGenTransformFeedbacks = &glGenTransformFeedbacks_enc;
	this->glDeleteTransformFeedbacks = &glDeleteTransformFeedbacks_enc;
	this->glBindTransformFeedback = &glBindTransformFeedback_enc;
	this->glPauseTransformFeedback = &glPauseTransformFeedback_enc;
	this->glResumeTransformFeedback = &glResumeTransformFeedback_enc;
	this->glIsTransformFeedback = &glIsTransformFeedback_enc;
	this->glTransformFeedbackVaryings = (glTransformFeedbackVaryings_client_proc_t) &enc_unsupported;
	this->glTransformFeedbackVaryingsAEMU = &glTransformFeedbackVaryingsAEMU_enc;
	this->glGetTransformFeedbackVarying = &glGetTransformFeedbackVarying_enc;
	this->glGenSamplers = &glGenSamplers_enc;
	this->glDeleteSamplers = &glDeleteSamplers_enc;
	this->glBindSampler = &glBindSampler_enc;
	this->glSamplerParameterf = &glSamplerParameterf_enc;
	this->glSamplerParameteri = &glSamplerParameteri_enc;
	this->glSamplerParameterfv = &glSamplerParameterfv_enc;
	this->glSamplerParameteriv = &glSamplerParameteriv_enc;
	this->glGetSamplerParameterfv = &glGetSamplerParameterfv_enc;
	this->glGetSamplerParameteriv = &glGetSamplerParameteriv_enc;
	this->glIsSampler = &glIsSampler_enc;
	this->glGenQueries = &glGenQueries_enc;
	this->glDeleteQueries = &glDeleteQueries_enc;
	this->glBeginQuery = &glBeginQuery_enc;
	this->glEndQuery = &glEndQuery_enc;
	this->glGetQueryiv = &glGetQueryiv_enc;
	this->glGetQueryObjectuiv = &glGetQueryObjectuiv_enc;
	this->glIsQuery = &glIsQuery_enc;
	this->glProgramParameteri = &glProgramParameteri_enc;
	this->glProgramBinary = &glProgramBinary_enc;
	this->glGetProgramBinary = &glGetProgramBinary_enc;
	this->glGetFragDataLocation = &glGetFragDataLocation_enc;
	this->glGetInteger64v = &glGetInteger64v_enc;
	this->glGetIntegeri_v = &glGetIntegeri_v_enc;
	this->glGetInteger64i_v = &glGetInteger64i_v_enc;
	this->glTexImage3D = &glTexImage3D_enc;
	this->glTexImage3DOffsetAEMU = &glTexImage3DOffsetAEMU_enc;
	this->glTexStorage3D = &glTexStorage3D_enc;
	this->glTexSubImage3D = &glTexSubImage3D_enc;
	this->glTexSubImage3DOffsetAEMU = &glTexSubImage3DOffsetAEMU_enc;
	this->glCompressedTexImage3D = &glCompressedTexImage3D_enc;
	this->glCompressedTexImage3DOffsetAEMU = &glCompressedTexImage3DOffsetAEMU_enc;
	this->glCompressedTexSubImage3D = &glCompressedTexSubImage3D_enc;
	this->glCompressedTexSubImage3DOffsetAEMU = &glCompressedTexSubImage3DOffsetAEMU_enc;
	this->glCopyTexSubImage3D = &glCopyTexSubImage3D_enc;
	this->glGetStringi = (glGetStringi_client_proc_t) &enc_unsupported;
	this->glGetBooleani_v = &glGetBooleani_v_enc;
	this->glMemoryBarrier = &glMemoryBarrier_enc;
	this->glMemoryBarrierByRegion = &glMemoryBarrierByRegion_enc;
	this->glGenProgramPipelines = &glGenProgramPipelines_enc;
	this->glDeleteProgramPipelines = &glDeleteProgramPipelines_enc;
	this->glBindProgramPipeline = &glBindProgramPipeline_enc;
	this->glGetProgramPipelineiv = &glGetProgramPipelineiv_enc;
	this->glGetProgramPipelineInfoLog = &glGetProgramPipelineInfoLog_enc;
	this->glValidateProgramPipeline = &glValidateProgramPipeline_enc;
	this->glIsProgramPipeline = &glIsProgramPipeline_enc;
	this->glUseProgramStages = &glUseProgramStages_enc;
	this->glActiveShaderProgram = &glActiveShaderProgram_enc;
	this->glCreateShaderProgramv = (glCreateShaderProgramv_client_proc_t) &enc_unsupported;
	this->glCreateShaderProgramvAEMU = &glCreateShaderProgramvAEMU_enc;
	this->glProgramUniform1f = &glProgramUniform1f_enc;
	this->glProgramUniform2f = &glProgramUniform2f_enc;
	this->glProgramUniform3f = &glProgramUniform3f_enc;
	this->glProgramUniform4f = &glProgramUniform4f_enc;
	this->glProgramUniform1i = &glProgramUniform1i_enc;
	this->glProgramUniform2i = &glProgramUniform2i_enc;
	this->glProgramUniform3i = &glProgramUniform3i_enc;
	this->glProgramUniform4i = &glProgramUniform4i_enc;
	this->glProgramUniform1ui = &glProgramUniform1ui_enc;
	this->glProgramUniform2ui = &glProgramUniform2ui_enc;
	this->glProgramUniform3ui = &glProgramUniform3ui_enc;
	this->glProgramUniform4ui = &glProgramUniform4ui_enc;
	this->glProgramUniform1fv = &glProgramUniform1fv_enc;
	this->glProgramUniform2fv = &glProgramUniform2fv_enc;
	this->glProgramUniform3fv = &glProgramUniform3fv_enc;
	this->glProgramUniform4fv = &glProgramUniform4fv_enc;
	this->glProgramUniform1iv = &glProgramUniform1iv_enc;
	this->glProgramUniform2iv = &glProgramUniform2iv_enc;
	this->glProgramUniform3iv = &glProgramUniform3iv_enc;
	this->glProgramUniform4iv = &glProgramUniform4iv_enc;
	this->glProgramUniform1uiv = &glProgramUniform1uiv_enc;
	this->glProgramUniform2uiv = &glProgramUniform2uiv_enc;
	this->glProgramUniform3uiv = &glProgramUniform3uiv_enc;
	this->glProgramUniform4uiv = &glProgramUniform4uiv_enc;
	this->glProgramUniformMatrix2fv = &glProgramUniformMatrix2fv_enc;
	this->glProgramUniformMatrix3fv = &glProgramUniformMatrix3fv_enc;
	this->glProgramUniformMatrix4fv = &glProgramUniformMatrix4fv_enc;
	this->glProgramUniformMatrix2x3fv = &glProgramUniformMatrix2x3fv_enc;
	this->glProgramUniformMatrix3x2fv = &glProgramUniformMatrix3x2fv_enc;
	this->glProgramUniformMatrix2x4fv = &glProgramUniformMatrix2x4fv_enc;
	this->glProgramUniformMatrix4x2fv = &glProgramUniformMatrix4x2fv_enc;
	this->glProgramUniformMatrix3x4fv = &glProgramUniformMatrix3x4fv_enc;
	this->glProgramUniformMatrix4x3fv = &glProgramUniformMatrix4x3fv_enc;
	this->glGetProgramInterfaceiv = &glGetProgramInterfaceiv_enc;
	this->glGetProgramResourceiv = &glGetProgramResourceiv_enc;
	this->glGetProgramResourceIndex = &glGetProgramResourceIndex_enc;
	this->glGetProgramResourceLocation = &glGetProgramResourceLocation_enc;
	this->glGetProgramResourceName = &glGetProgramResourceName_enc;
	this->glBindImageTexture = &glBindImageTexture_enc;
	this->glDispatchCompute = &glDispatchCompute_enc;
	this->glDispatchComputeIndirect = &glDispatchComputeIndirect_enc;
	this->glBindVertexBuffer = &glBindVertexBuffer_enc;
	this->glVertexAttribBinding = &glVertexAttribBinding_enc;
	this->glVertexAttribFormat = &glVertexAttribFormat_enc;
	this->glVertexAttribIFormat = &glVertexAttribIFormat_enc;
	this->glVertexBindingDivisor = &glVertexBindingDivisor_enc;
	this->glDrawArraysIndirect = (glDrawArraysIndirect_client_proc_t) &enc_unsupported;
	this->glDrawArraysIndirectDataAEMU = &glDrawArraysIndirectDataAEMU_enc;
	this->glDrawArraysIndirectOffsetAEMU = &glDrawArraysIndirectOffsetAEMU_enc;
	this->glDrawElementsIndirect = (glDrawElementsIndirect_client_proc_t) &enc_unsupported;
	this->glDrawElementsIndirectDataAEMU = &glDrawElementsIndirectDataAEMU_enc;
	this->glDrawElementsIndirectOffsetAEMU = &glDrawElementsIndirectOffsetAEMU_enc;
	this->glTexStorage2DMultisample = &glTexStorage2DMultisample_enc;
	this->glSampleMaski = &glSampleMaski_enc;
	this->glGetMultisamplefv = &glGetMultisamplefv_enc;
	this->glFramebufferParameteri = &glFramebufferParameteri_enc;
	this->glGetFramebufferParameteriv = &glGetFramebufferParameteriv_enc;
	this->glGetTexLevelParameterfv = &glGetTexLevelParameterfv_enc;
	this->glGetTexLevelParameteriv = &glGetTexLevelParameteriv_enc;
	this->glMapBufferRangeDMA = &glMapBufferRangeDMA_enc;
	this->glUnmapBufferDMA = &glUnmapBufferDMA_enc;
	this->glMapBufferRangeDirect = &glMapBufferRangeDirect_enc;
	this->glUnmapBufferDirect = &glUnmapBufferDirect_enc;
	this->glFlushMappedBufferRangeDirect = &glFlushMappedBufferRangeDirect_enc;
	this->glGetGraphicsResetStatusEXT = &glGetGraphicsResetStatusEXT_enc;
	this->glReadnPixelsEXT = &glReadnPixelsEXT_enc;
	this->glGetnUniformfvEXT = &glGetnUniformfvEXT_enc;
	this->glGetnUniformivEXT = &glGetnUniformivEXT_enc;
	this->glDrawArraysNullAEMU = &glDrawArraysNullAEMU_enc;
	this->glDrawElementsNullAEMU = (glDrawElementsNullAEMU_client_proc_t) &enc_unsupported;
	this->glDrawElementsOffsetNullAEMU = &glDrawElementsOffsetNullAEMU_enc;
	this->glDrawElementsDataNullAEMU = &glDrawElementsDataNullAEMU_enc;
	this->glUnmapBufferAsyncAEMU = &glUnmapBufferAsyncAEMU_enc;
	this->glFlushMappedBufferRangeAEMU2 = &glFlushMappedBufferRangeAEMU2_enc;
}


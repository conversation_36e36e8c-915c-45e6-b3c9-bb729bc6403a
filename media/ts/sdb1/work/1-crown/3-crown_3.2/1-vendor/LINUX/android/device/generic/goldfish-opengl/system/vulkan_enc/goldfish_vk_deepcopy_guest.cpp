// Copyright (C) 2018 The Android Open Source Project
// Copyright (C) 2018 Google Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Autogenerated module goldfish_vk_deepcopy_guest
// (impl) generated by android/android-emugl/host/libs/libOpenglRender/vulkan-registry/xml/genvk.py -registry android/android-emugl/host/libs/libOpenglRender/vulkan-registry/xml/vk.xml cereal -o android/android-emugl/host/libs/libOpenglRender/vulkan/cereal
// Please do not modify directly;
// re-run android/scripts/generate-vulkan-sources.sh,
// or directly from Python by defining:
// VULKAN_REGISTRY_XML_DIR : Directory containing genvk.py and vk.xml
// CEREAL_OUTPUT_DIR: Where to put the generated sources.
// python3 $VULKAN_REGISTRY_XML_DIR/genvk.py -registry $VULKAN_REGISTRY_XML_DIR/vk.xml cereal -o $CEREAL_OUTPUT_DIR

#include "goldfish_vk_deepcopy_guest.h"


#include "goldfish_vk_extension_structs_guest.h"
#include "goldfish_vk_private_defs.h"


namespace goldfish_vk {

void deepcopy_extension_struct(
    Pool* pool,
    const void* structExtension,
    void* structExtension_out);

#ifdef VK_VERSION_1_0
void deepcopy_VkApplicationInfo(
    Pool* pool,
    const VkApplicationInfo* from,
    VkApplicationInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pApplicationName = nullptr;
    if (from->pApplicationName)
    {
        to->pApplicationName = pool->strDup(from->pApplicationName);
    }
    to->pEngineName = nullptr;
    if (from->pEngineName)
    {
        to->pEngineName = pool->strDup(from->pEngineName);
    }
}

void deepcopy_VkInstanceCreateInfo(
    Pool* pool,
    const VkInstanceCreateInfo* from,
    VkInstanceCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pApplicationInfo = nullptr;
    if (from->pApplicationInfo)
    {
        to->pApplicationInfo = (VkApplicationInfo*)pool->alloc(sizeof(const VkApplicationInfo));
        deepcopy_VkApplicationInfo(pool, from->pApplicationInfo, (VkApplicationInfo*)(to->pApplicationInfo));
    }
    to->ppEnabledLayerNames = nullptr;
    if (from->ppEnabledLayerNames && from->enabledLayerCount)
    {
        to->ppEnabledLayerNames = pool->strDupArray(from->ppEnabledLayerNames, from->enabledLayerCount);
    }
    to->ppEnabledExtensionNames = nullptr;
    if (from->ppEnabledExtensionNames && from->enabledExtensionCount)
    {
        to->ppEnabledExtensionNames = pool->strDupArray(from->ppEnabledExtensionNames, from->enabledExtensionCount);
    }
}

void deepcopy_VkAllocationCallbacks(
    Pool* pool,
    const VkAllocationCallbacks* from,
    VkAllocationCallbacks* to)
{
    (void)pool;
    *to = *from;
    to->pUserData = nullptr;
    if (from->pUserData)
    {
        to->pUserData = (void*)pool->dupArray(from->pUserData, sizeof(uint8_t));
    }
}

void deepcopy_VkPhysicalDeviceFeatures(
    Pool* pool,
    const VkPhysicalDeviceFeatures* from,
    VkPhysicalDeviceFeatures* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkFormatProperties(
    Pool* pool,
    const VkFormatProperties* from,
    VkFormatProperties* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkExtent3D(
    Pool* pool,
    const VkExtent3D* from,
    VkExtent3D* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkImageFormatProperties(
    Pool* pool,
    const VkImageFormatProperties* from,
    VkImageFormatProperties* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkExtent3D(pool, &from->maxExtent, (VkExtent3D*)(&to->maxExtent));
}

void deepcopy_VkPhysicalDeviceLimits(
    Pool* pool,
    const VkPhysicalDeviceLimits* from,
    VkPhysicalDeviceLimits* to)
{
    (void)pool;
    *to = *from;
    memcpy(to->maxComputeWorkGroupCount, from->maxComputeWorkGroupCount, 3 * sizeof(uint32_t));
    memcpy(to->maxComputeWorkGroupSize, from->maxComputeWorkGroupSize, 3 * sizeof(uint32_t));
    memcpy(to->maxViewportDimensions, from->maxViewportDimensions, 2 * sizeof(uint32_t));
    memcpy(to->viewportBoundsRange, from->viewportBoundsRange, 2 * sizeof(float));
    memcpy(to->pointSizeRange, from->pointSizeRange, 2 * sizeof(float));
    memcpy(to->lineWidthRange, from->lineWidthRange, 2 * sizeof(float));
}

void deepcopy_VkPhysicalDeviceSparseProperties(
    Pool* pool,
    const VkPhysicalDeviceSparseProperties* from,
    VkPhysicalDeviceSparseProperties* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkPhysicalDeviceProperties(
    Pool* pool,
    const VkPhysicalDeviceProperties* from,
    VkPhysicalDeviceProperties* to)
{
    (void)pool;
    *to = *from;
    memcpy(to->deviceName, from->deviceName, VK_MAX_PHYSICAL_DEVICE_NAME_SIZE * sizeof(char));
    memcpy(to->pipelineCacheUUID, from->pipelineCacheUUID, VK_UUID_SIZE * sizeof(uint8_t));
    deepcopy_VkPhysicalDeviceLimits(pool, &from->limits, (VkPhysicalDeviceLimits*)(&to->limits));
    deepcopy_VkPhysicalDeviceSparseProperties(pool, &from->sparseProperties, (VkPhysicalDeviceSparseProperties*)(&to->sparseProperties));
}

void deepcopy_VkQueueFamilyProperties(
    Pool* pool,
    const VkQueueFamilyProperties* from,
    VkQueueFamilyProperties* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkExtent3D(pool, &from->minImageTransferGranularity, (VkExtent3D*)(&to->minImageTransferGranularity));
}

void deepcopy_VkMemoryType(
    Pool* pool,
    const VkMemoryType* from,
    VkMemoryType* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkMemoryHeap(
    Pool* pool,
    const VkMemoryHeap* from,
    VkMemoryHeap* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkPhysicalDeviceMemoryProperties(
    Pool* pool,
    const VkPhysicalDeviceMemoryProperties* from,
    VkPhysicalDeviceMemoryProperties* to)
{
    (void)pool;
    *to = *from;
    for (uint32_t i = 0; i < (uint32_t)VK_MAX_MEMORY_TYPES; ++i)
    {
        deepcopy_VkMemoryType(pool, from->memoryTypes + i, (VkMemoryType*)(to->memoryTypes + i));
    }
    for (uint32_t i = 0; i < (uint32_t)VK_MAX_MEMORY_HEAPS; ++i)
    {
        deepcopy_VkMemoryHeap(pool, from->memoryHeaps + i, (VkMemoryHeap*)(to->memoryHeaps + i));
    }
}

void deepcopy_VkDeviceQueueCreateInfo(
    Pool* pool,
    const VkDeviceQueueCreateInfo* from,
    VkDeviceQueueCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pQueuePriorities = nullptr;
    if (from->pQueuePriorities)
    {
        to->pQueuePriorities = (float*)pool->dupArray(from->pQueuePriorities, from->queueCount * sizeof(const float));
    }
}

void deepcopy_VkDeviceCreateInfo(
    Pool* pool,
    const VkDeviceCreateInfo* from,
    VkDeviceCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pQueueCreateInfos = nullptr;
    if (from->pQueueCreateInfos)
    {
        to->pQueueCreateInfos = (VkDeviceQueueCreateInfo*)pool->alloc(from->queueCreateInfoCount * sizeof(const VkDeviceQueueCreateInfo));
        to->queueCreateInfoCount = from->queueCreateInfoCount;
        for (uint32_t i = 0; i < (uint32_t)from->queueCreateInfoCount; ++i)
        {
            deepcopy_VkDeviceQueueCreateInfo(pool, from->pQueueCreateInfos + i, (VkDeviceQueueCreateInfo*)(to->pQueueCreateInfos + i));
        }
    }
    to->ppEnabledLayerNames = nullptr;
    if (from->ppEnabledLayerNames && from->enabledLayerCount)
    {
        to->ppEnabledLayerNames = pool->strDupArray(from->ppEnabledLayerNames, from->enabledLayerCount);
    }
    to->ppEnabledExtensionNames = nullptr;
    if (from->ppEnabledExtensionNames && from->enabledExtensionCount)
    {
        to->ppEnabledExtensionNames = pool->strDupArray(from->ppEnabledExtensionNames, from->enabledExtensionCount);
    }
    to->pEnabledFeatures = nullptr;
    if (from->pEnabledFeatures)
    {
        to->pEnabledFeatures = (VkPhysicalDeviceFeatures*)pool->alloc(sizeof(const VkPhysicalDeviceFeatures));
        deepcopy_VkPhysicalDeviceFeatures(pool, from->pEnabledFeatures, (VkPhysicalDeviceFeatures*)(to->pEnabledFeatures));
    }
}

void deepcopy_VkExtensionProperties(
    Pool* pool,
    const VkExtensionProperties* from,
    VkExtensionProperties* to)
{
    (void)pool;
    *to = *from;
    memcpy(to->extensionName, from->extensionName, VK_MAX_EXTENSION_NAME_SIZE * sizeof(char));
}

void deepcopy_VkLayerProperties(
    Pool* pool,
    const VkLayerProperties* from,
    VkLayerProperties* to)
{
    (void)pool;
    *to = *from;
    memcpy(to->layerName, from->layerName, VK_MAX_EXTENSION_NAME_SIZE * sizeof(char));
    memcpy(to->description, from->description, VK_MAX_DESCRIPTION_SIZE * sizeof(char));
}

void deepcopy_VkSubmitInfo(
    Pool* pool,
    const VkSubmitInfo* from,
    VkSubmitInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pWaitSemaphores = nullptr;
    if (from->pWaitSemaphores)
    {
        to->pWaitSemaphores = (VkSemaphore*)pool->dupArray(from->pWaitSemaphores, from->waitSemaphoreCount * sizeof(const VkSemaphore));
    }
    to->pWaitDstStageMask = nullptr;
    if (from->pWaitDstStageMask)
    {
        to->pWaitDstStageMask = (VkPipelineStageFlags*)pool->dupArray(from->pWaitDstStageMask, from->waitSemaphoreCount * sizeof(const VkPipelineStageFlags));
    }
    to->pCommandBuffers = nullptr;
    if (from->pCommandBuffers)
    {
        to->pCommandBuffers = (VkCommandBuffer*)pool->dupArray(from->pCommandBuffers, from->commandBufferCount * sizeof(const VkCommandBuffer));
    }
    to->pSignalSemaphores = nullptr;
    if (from->pSignalSemaphores)
    {
        to->pSignalSemaphores = (VkSemaphore*)pool->dupArray(from->pSignalSemaphores, from->signalSemaphoreCount * sizeof(const VkSemaphore));
    }
}

void deepcopy_VkMemoryAllocateInfo(
    Pool* pool,
    const VkMemoryAllocateInfo* from,
    VkMemoryAllocateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkMappedMemoryRange(
    Pool* pool,
    const VkMappedMemoryRange* from,
    VkMappedMemoryRange* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkMemoryRequirements(
    Pool* pool,
    const VkMemoryRequirements* from,
    VkMemoryRequirements* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkSparseImageFormatProperties(
    Pool* pool,
    const VkSparseImageFormatProperties* from,
    VkSparseImageFormatProperties* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkExtent3D(pool, &from->imageGranularity, (VkExtent3D*)(&to->imageGranularity));
}

void deepcopy_VkSparseImageMemoryRequirements(
    Pool* pool,
    const VkSparseImageMemoryRequirements* from,
    VkSparseImageMemoryRequirements* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkSparseImageFormatProperties(pool, &from->formatProperties, (VkSparseImageFormatProperties*)(&to->formatProperties));
}

void deepcopy_VkSparseMemoryBind(
    Pool* pool,
    const VkSparseMemoryBind* from,
    VkSparseMemoryBind* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkSparseBufferMemoryBindInfo(
    Pool* pool,
    const VkSparseBufferMemoryBindInfo* from,
    VkSparseBufferMemoryBindInfo* to)
{
    (void)pool;
    *to = *from;
    to->pBinds = nullptr;
    if (from->pBinds)
    {
        to->pBinds = (VkSparseMemoryBind*)pool->alloc(from->bindCount * sizeof(const VkSparseMemoryBind));
        to->bindCount = from->bindCount;
        for (uint32_t i = 0; i < (uint32_t)from->bindCount; ++i)
        {
            deepcopy_VkSparseMemoryBind(pool, from->pBinds + i, (VkSparseMemoryBind*)(to->pBinds + i));
        }
    }
}

void deepcopy_VkSparseImageOpaqueMemoryBindInfo(
    Pool* pool,
    const VkSparseImageOpaqueMemoryBindInfo* from,
    VkSparseImageOpaqueMemoryBindInfo* to)
{
    (void)pool;
    *to = *from;
    to->pBinds = nullptr;
    if (from->pBinds)
    {
        to->pBinds = (VkSparseMemoryBind*)pool->alloc(from->bindCount * sizeof(const VkSparseMemoryBind));
        to->bindCount = from->bindCount;
        for (uint32_t i = 0; i < (uint32_t)from->bindCount; ++i)
        {
            deepcopy_VkSparseMemoryBind(pool, from->pBinds + i, (VkSparseMemoryBind*)(to->pBinds + i));
        }
    }
}

void deepcopy_VkImageSubresource(
    Pool* pool,
    const VkImageSubresource* from,
    VkImageSubresource* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkOffset3D(
    Pool* pool,
    const VkOffset3D* from,
    VkOffset3D* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkSparseImageMemoryBind(
    Pool* pool,
    const VkSparseImageMemoryBind* from,
    VkSparseImageMemoryBind* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkImageSubresource(pool, &from->subresource, (VkImageSubresource*)(&to->subresource));
    deepcopy_VkOffset3D(pool, &from->offset, (VkOffset3D*)(&to->offset));
    deepcopy_VkExtent3D(pool, &from->extent, (VkExtent3D*)(&to->extent));
}

void deepcopy_VkSparseImageMemoryBindInfo(
    Pool* pool,
    const VkSparseImageMemoryBindInfo* from,
    VkSparseImageMemoryBindInfo* to)
{
    (void)pool;
    *to = *from;
    to->pBinds = nullptr;
    if (from->pBinds)
    {
        to->pBinds = (VkSparseImageMemoryBind*)pool->alloc(from->bindCount * sizeof(const VkSparseImageMemoryBind));
        to->bindCount = from->bindCount;
        for (uint32_t i = 0; i < (uint32_t)from->bindCount; ++i)
        {
            deepcopy_VkSparseImageMemoryBind(pool, from->pBinds + i, (VkSparseImageMemoryBind*)(to->pBinds + i));
        }
    }
}

void deepcopy_VkBindSparseInfo(
    Pool* pool,
    const VkBindSparseInfo* from,
    VkBindSparseInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pWaitSemaphores = nullptr;
    if (from->pWaitSemaphores)
    {
        to->pWaitSemaphores = (VkSemaphore*)pool->dupArray(from->pWaitSemaphores, from->waitSemaphoreCount * sizeof(const VkSemaphore));
    }
    to->pBufferBinds = nullptr;
    if (from->pBufferBinds)
    {
        to->pBufferBinds = (VkSparseBufferMemoryBindInfo*)pool->alloc(from->bufferBindCount * sizeof(const VkSparseBufferMemoryBindInfo));
        to->bufferBindCount = from->bufferBindCount;
        for (uint32_t i = 0; i < (uint32_t)from->bufferBindCount; ++i)
        {
            deepcopy_VkSparseBufferMemoryBindInfo(pool, from->pBufferBinds + i, (VkSparseBufferMemoryBindInfo*)(to->pBufferBinds + i));
        }
    }
    to->pImageOpaqueBinds = nullptr;
    if (from->pImageOpaqueBinds)
    {
        to->pImageOpaqueBinds = (VkSparseImageOpaqueMemoryBindInfo*)pool->alloc(from->imageOpaqueBindCount * sizeof(const VkSparseImageOpaqueMemoryBindInfo));
        to->imageOpaqueBindCount = from->imageOpaqueBindCount;
        for (uint32_t i = 0; i < (uint32_t)from->imageOpaqueBindCount; ++i)
        {
            deepcopy_VkSparseImageOpaqueMemoryBindInfo(pool, from->pImageOpaqueBinds + i, (VkSparseImageOpaqueMemoryBindInfo*)(to->pImageOpaqueBinds + i));
        }
    }
    to->pImageBinds = nullptr;
    if (from->pImageBinds)
    {
        to->pImageBinds = (VkSparseImageMemoryBindInfo*)pool->alloc(from->imageBindCount * sizeof(const VkSparseImageMemoryBindInfo));
        to->imageBindCount = from->imageBindCount;
        for (uint32_t i = 0; i < (uint32_t)from->imageBindCount; ++i)
        {
            deepcopy_VkSparseImageMemoryBindInfo(pool, from->pImageBinds + i, (VkSparseImageMemoryBindInfo*)(to->pImageBinds + i));
        }
    }
    to->pSignalSemaphores = nullptr;
    if (from->pSignalSemaphores)
    {
        to->pSignalSemaphores = (VkSemaphore*)pool->dupArray(from->pSignalSemaphores, from->signalSemaphoreCount * sizeof(const VkSemaphore));
    }
}

void deepcopy_VkFenceCreateInfo(
    Pool* pool,
    const VkFenceCreateInfo* from,
    VkFenceCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkSemaphoreCreateInfo(
    Pool* pool,
    const VkSemaphoreCreateInfo* from,
    VkSemaphoreCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkEventCreateInfo(
    Pool* pool,
    const VkEventCreateInfo* from,
    VkEventCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkQueryPoolCreateInfo(
    Pool* pool,
    const VkQueryPoolCreateInfo* from,
    VkQueryPoolCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkBufferCreateInfo(
    Pool* pool,
    const VkBufferCreateInfo* from,
    VkBufferCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pQueueFamilyIndices = nullptr;
    if (from->pQueueFamilyIndices)
    {
        to->pQueueFamilyIndices = (uint32_t*)pool->dupArray(from->pQueueFamilyIndices, from->queueFamilyIndexCount * sizeof(const uint32_t));
    }
}

void deepcopy_VkBufferViewCreateInfo(
    Pool* pool,
    const VkBufferViewCreateInfo* from,
    VkBufferViewCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkImageCreateInfo(
    Pool* pool,
    const VkImageCreateInfo* from,
    VkImageCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkExtent3D(pool, &from->extent, (VkExtent3D*)(&to->extent));
    to->pQueueFamilyIndices = nullptr;
    if (from->pQueueFamilyIndices)
    {
        to->pQueueFamilyIndices = (uint32_t*)pool->dupArray(from->pQueueFamilyIndices, from->queueFamilyIndexCount * sizeof(const uint32_t));
    }
}

void deepcopy_VkSubresourceLayout(
    Pool* pool,
    const VkSubresourceLayout* from,
    VkSubresourceLayout* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkComponentMapping(
    Pool* pool,
    const VkComponentMapping* from,
    VkComponentMapping* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkImageSubresourceRange(
    Pool* pool,
    const VkImageSubresourceRange* from,
    VkImageSubresourceRange* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkImageViewCreateInfo(
    Pool* pool,
    const VkImageViewCreateInfo* from,
    VkImageViewCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkComponentMapping(pool, &from->components, (VkComponentMapping*)(&to->components));
    deepcopy_VkImageSubresourceRange(pool, &from->subresourceRange, (VkImageSubresourceRange*)(&to->subresourceRange));
}

void deepcopy_VkShaderModuleCreateInfo(
    Pool* pool,
    const VkShaderModuleCreateInfo* from,
    VkShaderModuleCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pCode = nullptr;
    if (from->pCode)
    {
        to->pCode = (uint32_t*)pool->dupArray(from->pCode, (from->codeSize / 4) * sizeof(const uint32_t));
    }
}

void deepcopy_VkPipelineCacheCreateInfo(
    Pool* pool,
    const VkPipelineCacheCreateInfo* from,
    VkPipelineCacheCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pInitialData = nullptr;
    if (from->pInitialData)
    {
        to->pInitialData = (void*)pool->dupArray(from->pInitialData, from->initialDataSize * sizeof(const uint8_t));
    }
}

void deepcopy_VkSpecializationMapEntry(
    Pool* pool,
    const VkSpecializationMapEntry* from,
    VkSpecializationMapEntry* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkSpecializationInfo(
    Pool* pool,
    const VkSpecializationInfo* from,
    VkSpecializationInfo* to)
{
    (void)pool;
    *to = *from;
    to->pMapEntries = nullptr;
    if (from->pMapEntries)
    {
        to->pMapEntries = (VkSpecializationMapEntry*)pool->alloc(from->mapEntryCount * sizeof(const VkSpecializationMapEntry));
        to->mapEntryCount = from->mapEntryCount;
        for (uint32_t i = 0; i < (uint32_t)from->mapEntryCount; ++i)
        {
            deepcopy_VkSpecializationMapEntry(pool, from->pMapEntries + i, (VkSpecializationMapEntry*)(to->pMapEntries + i));
        }
    }
    to->pData = nullptr;
    if (from->pData)
    {
        to->pData = (void*)pool->dupArray(from->pData, from->dataSize * sizeof(const uint8_t));
    }
}

void deepcopy_VkPipelineShaderStageCreateInfo(
    Pool* pool,
    const VkPipelineShaderStageCreateInfo* from,
    VkPipelineShaderStageCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pName = nullptr;
    if (from->pName)
    {
        to->pName = pool->strDup(from->pName);
    }
    to->pSpecializationInfo = nullptr;
    if (from->pSpecializationInfo)
    {
        to->pSpecializationInfo = (VkSpecializationInfo*)pool->alloc(sizeof(const VkSpecializationInfo));
        deepcopy_VkSpecializationInfo(pool, from->pSpecializationInfo, (VkSpecializationInfo*)(to->pSpecializationInfo));
    }
}

void deepcopy_VkVertexInputBindingDescription(
    Pool* pool,
    const VkVertexInputBindingDescription* from,
    VkVertexInputBindingDescription* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkVertexInputAttributeDescription(
    Pool* pool,
    const VkVertexInputAttributeDescription* from,
    VkVertexInputAttributeDescription* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkPipelineVertexInputStateCreateInfo(
    Pool* pool,
    const VkPipelineVertexInputStateCreateInfo* from,
    VkPipelineVertexInputStateCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pVertexBindingDescriptions = nullptr;
    if (from->pVertexBindingDescriptions)
    {
        to->pVertexBindingDescriptions = (VkVertexInputBindingDescription*)pool->alloc(from->vertexBindingDescriptionCount * sizeof(const VkVertexInputBindingDescription));
        to->vertexBindingDescriptionCount = from->vertexBindingDescriptionCount;
        for (uint32_t i = 0; i < (uint32_t)from->vertexBindingDescriptionCount; ++i)
        {
            deepcopy_VkVertexInputBindingDescription(pool, from->pVertexBindingDescriptions + i, (VkVertexInputBindingDescription*)(to->pVertexBindingDescriptions + i));
        }
    }
    to->pVertexAttributeDescriptions = nullptr;
    if (from->pVertexAttributeDescriptions)
    {
        to->pVertexAttributeDescriptions = (VkVertexInputAttributeDescription*)pool->alloc(from->vertexAttributeDescriptionCount * sizeof(const VkVertexInputAttributeDescription));
        to->vertexAttributeDescriptionCount = from->vertexAttributeDescriptionCount;
        for (uint32_t i = 0; i < (uint32_t)from->vertexAttributeDescriptionCount; ++i)
        {
            deepcopy_VkVertexInputAttributeDescription(pool, from->pVertexAttributeDescriptions + i, (VkVertexInputAttributeDescription*)(to->pVertexAttributeDescriptions + i));
        }
    }
}

void deepcopy_VkPipelineInputAssemblyStateCreateInfo(
    Pool* pool,
    const VkPipelineInputAssemblyStateCreateInfo* from,
    VkPipelineInputAssemblyStateCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPipelineTessellationStateCreateInfo(
    Pool* pool,
    const VkPipelineTessellationStateCreateInfo* from,
    VkPipelineTessellationStateCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkViewport(
    Pool* pool,
    const VkViewport* from,
    VkViewport* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkOffset2D(
    Pool* pool,
    const VkOffset2D* from,
    VkOffset2D* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkExtent2D(
    Pool* pool,
    const VkExtent2D* from,
    VkExtent2D* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkRect2D(
    Pool* pool,
    const VkRect2D* from,
    VkRect2D* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkOffset2D(pool, &from->offset, (VkOffset2D*)(&to->offset));
    deepcopy_VkExtent2D(pool, &from->extent, (VkExtent2D*)(&to->extent));
}

void deepcopy_VkPipelineViewportStateCreateInfo(
    Pool* pool,
    const VkPipelineViewportStateCreateInfo* from,
    VkPipelineViewportStateCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pViewports = nullptr;
    if (from->pViewports)
    {
        to->pViewports = (VkViewport*)pool->alloc(from->viewportCount * sizeof(const VkViewport));
        to->viewportCount = from->viewportCount;
        for (uint32_t i = 0; i < (uint32_t)from->viewportCount; ++i)
        {
            deepcopy_VkViewport(pool, from->pViewports + i, (VkViewport*)(to->pViewports + i));
        }
    }
    to->pScissors = nullptr;
    if (from->pScissors)
    {
        to->pScissors = (VkRect2D*)pool->alloc(from->scissorCount * sizeof(const VkRect2D));
        to->scissorCount = from->scissorCount;
        for (uint32_t i = 0; i < (uint32_t)from->scissorCount; ++i)
        {
            deepcopy_VkRect2D(pool, from->pScissors + i, (VkRect2D*)(to->pScissors + i));
        }
    }
}

void deepcopy_VkPipelineRasterizationStateCreateInfo(
    Pool* pool,
    const VkPipelineRasterizationStateCreateInfo* from,
    VkPipelineRasterizationStateCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPipelineMultisampleStateCreateInfo(
    Pool* pool,
    const VkPipelineMultisampleStateCreateInfo* from,
    VkPipelineMultisampleStateCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pSampleMask = nullptr;
    if (from->pSampleMask)
    {
        to->pSampleMask = (VkSampleMask*)pool->dupArray(from->pSampleMask, (((from->rasterizationSamples) + 31) / 32) * sizeof(const VkSampleMask));
    }
}

void deepcopy_VkStencilOpState(
    Pool* pool,
    const VkStencilOpState* from,
    VkStencilOpState* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkPipelineDepthStencilStateCreateInfo(
    Pool* pool,
    const VkPipelineDepthStencilStateCreateInfo* from,
    VkPipelineDepthStencilStateCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkStencilOpState(pool, &from->front, (VkStencilOpState*)(&to->front));
    deepcopy_VkStencilOpState(pool, &from->back, (VkStencilOpState*)(&to->back));
}

void deepcopy_VkPipelineColorBlendAttachmentState(
    Pool* pool,
    const VkPipelineColorBlendAttachmentState* from,
    VkPipelineColorBlendAttachmentState* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkPipelineColorBlendStateCreateInfo(
    Pool* pool,
    const VkPipelineColorBlendStateCreateInfo* from,
    VkPipelineColorBlendStateCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pAttachments = nullptr;
    if (from->pAttachments)
    {
        to->pAttachments = (VkPipelineColorBlendAttachmentState*)pool->alloc(from->attachmentCount * sizeof(const VkPipelineColorBlendAttachmentState));
        to->attachmentCount = from->attachmentCount;
        for (uint32_t i = 0; i < (uint32_t)from->attachmentCount; ++i)
        {
            deepcopy_VkPipelineColorBlendAttachmentState(pool, from->pAttachments + i, (VkPipelineColorBlendAttachmentState*)(to->pAttachments + i));
        }
    }
    memcpy(to->blendConstants, from->blendConstants, 4 * sizeof(float));
}

void deepcopy_VkPipelineDynamicStateCreateInfo(
    Pool* pool,
    const VkPipelineDynamicStateCreateInfo* from,
    VkPipelineDynamicStateCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pDynamicStates = nullptr;
    if (from->pDynamicStates)
    {
        to->pDynamicStates = (VkDynamicState*)pool->dupArray(from->pDynamicStates, from->dynamicStateCount * sizeof(const VkDynamicState));
    }
}

void deepcopy_VkGraphicsPipelineCreateInfo(
    Pool* pool,
    const VkGraphicsPipelineCreateInfo* from,
    VkGraphicsPipelineCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pStages = nullptr;
    if (from->pStages)
    {
        to->pStages = (VkPipelineShaderStageCreateInfo*)pool->alloc(from->stageCount * sizeof(const VkPipelineShaderStageCreateInfo));
        to->stageCount = from->stageCount;
        for (uint32_t i = 0; i < (uint32_t)from->stageCount; ++i)
        {
            deepcopy_VkPipelineShaderStageCreateInfo(pool, from->pStages + i, (VkPipelineShaderStageCreateInfo*)(to->pStages + i));
        }
    }
    to->pVertexInputState = nullptr;
    if (from->pVertexInputState)
    {
        to->pVertexInputState = (VkPipelineVertexInputStateCreateInfo*)pool->alloc(sizeof(const VkPipelineVertexInputStateCreateInfo));
        deepcopy_VkPipelineVertexInputStateCreateInfo(pool, from->pVertexInputState, (VkPipelineVertexInputStateCreateInfo*)(to->pVertexInputState));
    }
    to->pInputAssemblyState = nullptr;
    if (from->pInputAssemblyState)
    {
        to->pInputAssemblyState = (VkPipelineInputAssemblyStateCreateInfo*)pool->alloc(sizeof(const VkPipelineInputAssemblyStateCreateInfo));
        deepcopy_VkPipelineInputAssemblyStateCreateInfo(pool, from->pInputAssemblyState, (VkPipelineInputAssemblyStateCreateInfo*)(to->pInputAssemblyState));
    }
    to->pTessellationState = nullptr;
    if (from->pTessellationState)
    {
        to->pTessellationState = (VkPipelineTessellationStateCreateInfo*)pool->alloc(sizeof(const VkPipelineTessellationStateCreateInfo));
        deepcopy_VkPipelineTessellationStateCreateInfo(pool, from->pTessellationState, (VkPipelineTessellationStateCreateInfo*)(to->pTessellationState));
    }
    to->pViewportState = nullptr;
    if (from->pViewportState)
    {
        to->pViewportState = (VkPipelineViewportStateCreateInfo*)pool->alloc(sizeof(const VkPipelineViewportStateCreateInfo));
        deepcopy_VkPipelineViewportStateCreateInfo(pool, from->pViewportState, (VkPipelineViewportStateCreateInfo*)(to->pViewportState));
    }
    to->pRasterizationState = nullptr;
    if (from->pRasterizationState)
    {
        to->pRasterizationState = (VkPipelineRasterizationStateCreateInfo*)pool->alloc(sizeof(const VkPipelineRasterizationStateCreateInfo));
        deepcopy_VkPipelineRasterizationStateCreateInfo(pool, from->pRasterizationState, (VkPipelineRasterizationStateCreateInfo*)(to->pRasterizationState));
    }
    to->pMultisampleState = nullptr;
    if (from->pMultisampleState)
    {
        to->pMultisampleState = (VkPipelineMultisampleStateCreateInfo*)pool->alloc(sizeof(const VkPipelineMultisampleStateCreateInfo));
        deepcopy_VkPipelineMultisampleStateCreateInfo(pool, from->pMultisampleState, (VkPipelineMultisampleStateCreateInfo*)(to->pMultisampleState));
    }
    to->pDepthStencilState = nullptr;
    if (from->pDepthStencilState)
    {
        to->pDepthStencilState = (VkPipelineDepthStencilStateCreateInfo*)pool->alloc(sizeof(const VkPipelineDepthStencilStateCreateInfo));
        deepcopy_VkPipelineDepthStencilStateCreateInfo(pool, from->pDepthStencilState, (VkPipelineDepthStencilStateCreateInfo*)(to->pDepthStencilState));
    }
    to->pColorBlendState = nullptr;
    if (from->pColorBlendState)
    {
        to->pColorBlendState = (VkPipelineColorBlendStateCreateInfo*)pool->alloc(sizeof(const VkPipelineColorBlendStateCreateInfo));
        deepcopy_VkPipelineColorBlendStateCreateInfo(pool, from->pColorBlendState, (VkPipelineColorBlendStateCreateInfo*)(to->pColorBlendState));
    }
    to->pDynamicState = nullptr;
    if (from->pDynamicState)
    {
        to->pDynamicState = (VkPipelineDynamicStateCreateInfo*)pool->alloc(sizeof(const VkPipelineDynamicStateCreateInfo));
        deepcopy_VkPipelineDynamicStateCreateInfo(pool, from->pDynamicState, (VkPipelineDynamicStateCreateInfo*)(to->pDynamicState));
    }
}

void deepcopy_VkComputePipelineCreateInfo(
    Pool* pool,
    const VkComputePipelineCreateInfo* from,
    VkComputePipelineCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkPipelineShaderStageCreateInfo(pool, &from->stage, (VkPipelineShaderStageCreateInfo*)(&to->stage));
}

void deepcopy_VkPushConstantRange(
    Pool* pool,
    const VkPushConstantRange* from,
    VkPushConstantRange* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkPipelineLayoutCreateInfo(
    Pool* pool,
    const VkPipelineLayoutCreateInfo* from,
    VkPipelineLayoutCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pSetLayouts = nullptr;
    if (from->pSetLayouts)
    {
        to->pSetLayouts = (VkDescriptorSetLayout*)pool->dupArray(from->pSetLayouts, from->setLayoutCount * sizeof(const VkDescriptorSetLayout));
    }
    to->pPushConstantRanges = nullptr;
    if (from->pPushConstantRanges)
    {
        to->pPushConstantRanges = (VkPushConstantRange*)pool->alloc(from->pushConstantRangeCount * sizeof(const VkPushConstantRange));
        to->pushConstantRangeCount = from->pushConstantRangeCount;
        for (uint32_t i = 0; i < (uint32_t)from->pushConstantRangeCount; ++i)
        {
            deepcopy_VkPushConstantRange(pool, from->pPushConstantRanges + i, (VkPushConstantRange*)(to->pPushConstantRanges + i));
        }
    }
}

void deepcopy_VkSamplerCreateInfo(
    Pool* pool,
    const VkSamplerCreateInfo* from,
    VkSamplerCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkDescriptorSetLayoutBinding(
    Pool* pool,
    const VkDescriptorSetLayoutBinding* from,
    VkDescriptorSetLayoutBinding* to)
{
    (void)pool;
    *to = *from;
    to->pImmutableSamplers = nullptr;
    if (from->pImmutableSamplers)
    {
        to->pImmutableSamplers = (VkSampler*)pool->dupArray(from->pImmutableSamplers, from->descriptorCount * sizeof(const VkSampler));
    }
}

void deepcopy_VkDescriptorSetLayoutCreateInfo(
    Pool* pool,
    const VkDescriptorSetLayoutCreateInfo* from,
    VkDescriptorSetLayoutCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pBindings = nullptr;
    if (from->pBindings)
    {
        to->pBindings = (VkDescriptorSetLayoutBinding*)pool->alloc(from->bindingCount * sizeof(const VkDescriptorSetLayoutBinding));
        to->bindingCount = from->bindingCount;
        for (uint32_t i = 0; i < (uint32_t)from->bindingCount; ++i)
        {
            deepcopy_VkDescriptorSetLayoutBinding(pool, from->pBindings + i, (VkDescriptorSetLayoutBinding*)(to->pBindings + i));
        }
    }
}

void deepcopy_VkDescriptorPoolSize(
    Pool* pool,
    const VkDescriptorPoolSize* from,
    VkDescriptorPoolSize* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkDescriptorPoolCreateInfo(
    Pool* pool,
    const VkDescriptorPoolCreateInfo* from,
    VkDescriptorPoolCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pPoolSizes = nullptr;
    if (from->pPoolSizes)
    {
        to->pPoolSizes = (VkDescriptorPoolSize*)pool->alloc(from->poolSizeCount * sizeof(const VkDescriptorPoolSize));
        to->poolSizeCount = from->poolSizeCount;
        for (uint32_t i = 0; i < (uint32_t)from->poolSizeCount; ++i)
        {
            deepcopy_VkDescriptorPoolSize(pool, from->pPoolSizes + i, (VkDescriptorPoolSize*)(to->pPoolSizes + i));
        }
    }
}

void deepcopy_VkDescriptorSetAllocateInfo(
    Pool* pool,
    const VkDescriptorSetAllocateInfo* from,
    VkDescriptorSetAllocateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pSetLayouts = nullptr;
    if (from->pSetLayouts)
    {
        to->pSetLayouts = (VkDescriptorSetLayout*)pool->dupArray(from->pSetLayouts, from->descriptorSetCount * sizeof(const VkDescriptorSetLayout));
    }
}

void deepcopy_VkDescriptorImageInfo(
    Pool* pool,
    const VkDescriptorImageInfo* from,
    VkDescriptorImageInfo* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkDescriptorBufferInfo(
    Pool* pool,
    const VkDescriptorBufferInfo* from,
    VkDescriptorBufferInfo* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkWriteDescriptorSet(
    Pool* pool,
    const VkWriteDescriptorSet* from,
    VkWriteDescriptorSet* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pImageInfo = nullptr;
    if (from->pImageInfo)
    {
        to->pImageInfo = (VkDescriptorImageInfo*)pool->alloc(from->descriptorCount * sizeof(const VkDescriptorImageInfo));
        to->descriptorCount = from->descriptorCount;
        for (uint32_t i = 0; i < (uint32_t)from->descriptorCount; ++i)
        {
            deepcopy_VkDescriptorImageInfo(pool, from->pImageInfo + i, (VkDescriptorImageInfo*)(to->pImageInfo + i));
        }
    }
    to->pBufferInfo = nullptr;
    if (from->pBufferInfo)
    {
        to->pBufferInfo = (VkDescriptorBufferInfo*)pool->alloc(from->descriptorCount * sizeof(const VkDescriptorBufferInfo));
        to->descriptorCount = from->descriptorCount;
        for (uint32_t i = 0; i < (uint32_t)from->descriptorCount; ++i)
        {
            deepcopy_VkDescriptorBufferInfo(pool, from->pBufferInfo + i, (VkDescriptorBufferInfo*)(to->pBufferInfo + i));
        }
    }
    to->pTexelBufferView = nullptr;
    if (from->pTexelBufferView)
    {
        to->pTexelBufferView = (VkBufferView*)pool->dupArray(from->pTexelBufferView, from->descriptorCount * sizeof(const VkBufferView));
    }
}

void deepcopy_VkCopyDescriptorSet(
    Pool* pool,
    const VkCopyDescriptorSet* from,
    VkCopyDescriptorSet* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkFramebufferCreateInfo(
    Pool* pool,
    const VkFramebufferCreateInfo* from,
    VkFramebufferCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pAttachments = nullptr;
    if (from->pAttachments)
    {
        to->pAttachments = (VkImageView*)pool->dupArray(from->pAttachments, from->attachmentCount * sizeof(const VkImageView));
    }
}

void deepcopy_VkAttachmentDescription(
    Pool* pool,
    const VkAttachmentDescription* from,
    VkAttachmentDescription* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkAttachmentReference(
    Pool* pool,
    const VkAttachmentReference* from,
    VkAttachmentReference* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkSubpassDescription(
    Pool* pool,
    const VkSubpassDescription* from,
    VkSubpassDescription* to)
{
    (void)pool;
    *to = *from;
    to->pInputAttachments = nullptr;
    if (from->pInputAttachments)
    {
        to->pInputAttachments = (VkAttachmentReference*)pool->alloc(from->inputAttachmentCount * sizeof(const VkAttachmentReference));
        to->inputAttachmentCount = from->inputAttachmentCount;
        for (uint32_t i = 0; i < (uint32_t)from->inputAttachmentCount; ++i)
        {
            deepcopy_VkAttachmentReference(pool, from->pInputAttachments + i, (VkAttachmentReference*)(to->pInputAttachments + i));
        }
    }
    to->pColorAttachments = nullptr;
    if (from->pColorAttachments)
    {
        to->pColorAttachments = (VkAttachmentReference*)pool->alloc(from->colorAttachmentCount * sizeof(const VkAttachmentReference));
        to->colorAttachmentCount = from->colorAttachmentCount;
        for (uint32_t i = 0; i < (uint32_t)from->colorAttachmentCount; ++i)
        {
            deepcopy_VkAttachmentReference(pool, from->pColorAttachments + i, (VkAttachmentReference*)(to->pColorAttachments + i));
        }
    }
    to->pResolveAttachments = nullptr;
    if (from->pResolveAttachments)
    {
        to->pResolveAttachments = (VkAttachmentReference*)pool->alloc(from->colorAttachmentCount * sizeof(const VkAttachmentReference));
        to->colorAttachmentCount = from->colorAttachmentCount;
        for (uint32_t i = 0; i < (uint32_t)from->colorAttachmentCount; ++i)
        {
            deepcopy_VkAttachmentReference(pool, from->pResolveAttachments + i, (VkAttachmentReference*)(to->pResolveAttachments + i));
        }
    }
    to->pDepthStencilAttachment = nullptr;
    if (from->pDepthStencilAttachment)
    {
        to->pDepthStencilAttachment = (VkAttachmentReference*)pool->alloc(sizeof(const VkAttachmentReference));
        deepcopy_VkAttachmentReference(pool, from->pDepthStencilAttachment, (VkAttachmentReference*)(to->pDepthStencilAttachment));
    }
    to->pPreserveAttachments = nullptr;
    if (from->pPreserveAttachments)
    {
        to->pPreserveAttachments = (uint32_t*)pool->dupArray(from->pPreserveAttachments, from->preserveAttachmentCount * sizeof(const uint32_t));
    }
}

void deepcopy_VkSubpassDependency(
    Pool* pool,
    const VkSubpassDependency* from,
    VkSubpassDependency* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkRenderPassCreateInfo(
    Pool* pool,
    const VkRenderPassCreateInfo* from,
    VkRenderPassCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pAttachments = nullptr;
    if (from->pAttachments)
    {
        to->pAttachments = (VkAttachmentDescription*)pool->alloc(from->attachmentCount * sizeof(const VkAttachmentDescription));
        to->attachmentCount = from->attachmentCount;
        for (uint32_t i = 0; i < (uint32_t)from->attachmentCount; ++i)
        {
            deepcopy_VkAttachmentDescription(pool, from->pAttachments + i, (VkAttachmentDescription*)(to->pAttachments + i));
        }
    }
    to->pSubpasses = nullptr;
    if (from->pSubpasses)
    {
        to->pSubpasses = (VkSubpassDescription*)pool->alloc(from->subpassCount * sizeof(const VkSubpassDescription));
        to->subpassCount = from->subpassCount;
        for (uint32_t i = 0; i < (uint32_t)from->subpassCount; ++i)
        {
            deepcopy_VkSubpassDescription(pool, from->pSubpasses + i, (VkSubpassDescription*)(to->pSubpasses + i));
        }
    }
    to->pDependencies = nullptr;
    if (from->pDependencies)
    {
        to->pDependencies = (VkSubpassDependency*)pool->alloc(from->dependencyCount * sizeof(const VkSubpassDependency));
        to->dependencyCount = from->dependencyCount;
        for (uint32_t i = 0; i < (uint32_t)from->dependencyCount; ++i)
        {
            deepcopy_VkSubpassDependency(pool, from->pDependencies + i, (VkSubpassDependency*)(to->pDependencies + i));
        }
    }
}

void deepcopy_VkCommandPoolCreateInfo(
    Pool* pool,
    const VkCommandPoolCreateInfo* from,
    VkCommandPoolCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkCommandBufferAllocateInfo(
    Pool* pool,
    const VkCommandBufferAllocateInfo* from,
    VkCommandBufferAllocateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkCommandBufferInheritanceInfo(
    Pool* pool,
    const VkCommandBufferInheritanceInfo* from,
    VkCommandBufferInheritanceInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkCommandBufferBeginInfo(
    Pool* pool,
    const VkCommandBufferBeginInfo* from,
    VkCommandBufferBeginInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pInheritanceInfo = nullptr;
    if (from->pInheritanceInfo)
    {
        to->pInheritanceInfo = (VkCommandBufferInheritanceInfo*)pool->alloc(sizeof(const VkCommandBufferInheritanceInfo));
        deepcopy_VkCommandBufferInheritanceInfo(pool, from->pInheritanceInfo, (VkCommandBufferInheritanceInfo*)(to->pInheritanceInfo));
    }
}

void deepcopy_VkBufferCopy(
    Pool* pool,
    const VkBufferCopy* from,
    VkBufferCopy* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkImageSubresourceLayers(
    Pool* pool,
    const VkImageSubresourceLayers* from,
    VkImageSubresourceLayers* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkImageCopy(
    Pool* pool,
    const VkImageCopy* from,
    VkImageCopy* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkImageSubresourceLayers(pool, &from->srcSubresource, (VkImageSubresourceLayers*)(&to->srcSubresource));
    deepcopy_VkOffset3D(pool, &from->srcOffset, (VkOffset3D*)(&to->srcOffset));
    deepcopy_VkImageSubresourceLayers(pool, &from->dstSubresource, (VkImageSubresourceLayers*)(&to->dstSubresource));
    deepcopy_VkOffset3D(pool, &from->dstOffset, (VkOffset3D*)(&to->dstOffset));
    deepcopy_VkExtent3D(pool, &from->extent, (VkExtent3D*)(&to->extent));
}

void deepcopy_VkImageBlit(
    Pool* pool,
    const VkImageBlit* from,
    VkImageBlit* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkImageSubresourceLayers(pool, &from->srcSubresource, (VkImageSubresourceLayers*)(&to->srcSubresource));
    for (uint32_t i = 0; i < (uint32_t)2; ++i)
    {
        deepcopy_VkOffset3D(pool, from->srcOffsets + i, (VkOffset3D*)(to->srcOffsets + i));
    }
    deepcopy_VkImageSubresourceLayers(pool, &from->dstSubresource, (VkImageSubresourceLayers*)(&to->dstSubresource));
    for (uint32_t i = 0; i < (uint32_t)2; ++i)
    {
        deepcopy_VkOffset3D(pool, from->dstOffsets + i, (VkOffset3D*)(to->dstOffsets + i));
    }
}

void deepcopy_VkBufferImageCopy(
    Pool* pool,
    const VkBufferImageCopy* from,
    VkBufferImageCopy* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkImageSubresourceLayers(pool, &from->imageSubresource, (VkImageSubresourceLayers*)(&to->imageSubresource));
    deepcopy_VkOffset3D(pool, &from->imageOffset, (VkOffset3D*)(&to->imageOffset));
    deepcopy_VkExtent3D(pool, &from->imageExtent, (VkExtent3D*)(&to->imageExtent));
}

void deepcopy_VkClearColorValue(
    Pool* pool,
    const VkClearColorValue* from,
    VkClearColorValue* to)
{
    (void)pool;
    *to = *from;
    memcpy(to->float32, from->float32, 4 * sizeof(float));
    memcpy(to->int32, from->int32, 4 * sizeof(int32_t));
    memcpy(to->uint32, from->uint32, 4 * sizeof(uint32_t));
}

void deepcopy_VkClearDepthStencilValue(
    Pool* pool,
    const VkClearDepthStencilValue* from,
    VkClearDepthStencilValue* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkClearValue(
    Pool* pool,
    const VkClearValue* from,
    VkClearValue* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkClearColorValue(pool, &from->color, (VkClearColorValue*)(&to->color));
    deepcopy_VkClearDepthStencilValue(pool, &from->depthStencil, (VkClearDepthStencilValue*)(&to->depthStencil));
}

void deepcopy_VkClearAttachment(
    Pool* pool,
    const VkClearAttachment* from,
    VkClearAttachment* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkClearValue(pool, &from->clearValue, (VkClearValue*)(&to->clearValue));
}

void deepcopy_VkClearRect(
    Pool* pool,
    const VkClearRect* from,
    VkClearRect* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkRect2D(pool, &from->rect, (VkRect2D*)(&to->rect));
}

void deepcopy_VkImageResolve(
    Pool* pool,
    const VkImageResolve* from,
    VkImageResolve* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkImageSubresourceLayers(pool, &from->srcSubresource, (VkImageSubresourceLayers*)(&to->srcSubresource));
    deepcopy_VkOffset3D(pool, &from->srcOffset, (VkOffset3D*)(&to->srcOffset));
    deepcopy_VkImageSubresourceLayers(pool, &from->dstSubresource, (VkImageSubresourceLayers*)(&to->dstSubresource));
    deepcopy_VkOffset3D(pool, &from->dstOffset, (VkOffset3D*)(&to->dstOffset));
    deepcopy_VkExtent3D(pool, &from->extent, (VkExtent3D*)(&to->extent));
}

void deepcopy_VkMemoryBarrier(
    Pool* pool,
    const VkMemoryBarrier* from,
    VkMemoryBarrier* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkBufferMemoryBarrier(
    Pool* pool,
    const VkBufferMemoryBarrier* from,
    VkBufferMemoryBarrier* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkImageMemoryBarrier(
    Pool* pool,
    const VkImageMemoryBarrier* from,
    VkImageMemoryBarrier* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkImageSubresourceRange(pool, &from->subresourceRange, (VkImageSubresourceRange*)(&to->subresourceRange));
}

void deepcopy_VkRenderPassBeginInfo(
    Pool* pool,
    const VkRenderPassBeginInfo* from,
    VkRenderPassBeginInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkRect2D(pool, &from->renderArea, (VkRect2D*)(&to->renderArea));
    to->pClearValues = nullptr;
    if (from->pClearValues)
    {
        to->pClearValues = (VkClearValue*)pool->alloc(from->clearValueCount * sizeof(const VkClearValue));
        to->clearValueCount = from->clearValueCount;
        for (uint32_t i = 0; i < (uint32_t)from->clearValueCount; ++i)
        {
            deepcopy_VkClearValue(pool, from->pClearValues + i, (VkClearValue*)(to->pClearValues + i));
        }
    }
}

void deepcopy_VkDispatchIndirectCommand(
    Pool* pool,
    const VkDispatchIndirectCommand* from,
    VkDispatchIndirectCommand* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkDrawIndexedIndirectCommand(
    Pool* pool,
    const VkDrawIndexedIndirectCommand* from,
    VkDrawIndexedIndirectCommand* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkDrawIndirectCommand(
    Pool* pool,
    const VkDrawIndirectCommand* from,
    VkDrawIndirectCommand* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkBaseOutStructure(
    Pool* pool,
    const VkBaseOutStructure* from,
    VkBaseOutStructure* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (VkBaseOutStructure*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkBaseInStructure(
    Pool* pool,
    const VkBaseInStructure* from,
    VkBaseInStructure* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const VkBaseInStructure*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_VERSION_1_1
void deepcopy_VkPhysicalDeviceSubgroupProperties(
    Pool* pool,
    const VkPhysicalDeviceSubgroupProperties* from,
    VkPhysicalDeviceSubgroupProperties* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkBindBufferMemoryInfo(
    Pool* pool,
    const VkBindBufferMemoryInfo* from,
    VkBindBufferMemoryInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkBindImageMemoryInfo(
    Pool* pool,
    const VkBindImageMemoryInfo* from,
    VkBindImageMemoryInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPhysicalDevice16BitStorageFeatures(
    Pool* pool,
    const VkPhysicalDevice16BitStorageFeatures* from,
    VkPhysicalDevice16BitStorageFeatures* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkMemoryDedicatedRequirements(
    Pool* pool,
    const VkMemoryDedicatedRequirements* from,
    VkMemoryDedicatedRequirements* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkMemoryDedicatedAllocateInfo(
    Pool* pool,
    const VkMemoryDedicatedAllocateInfo* from,
    VkMemoryDedicatedAllocateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkMemoryAllocateFlagsInfo(
    Pool* pool,
    const VkMemoryAllocateFlagsInfo* from,
    VkMemoryAllocateFlagsInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkDeviceGroupRenderPassBeginInfo(
    Pool* pool,
    const VkDeviceGroupRenderPassBeginInfo* from,
    VkDeviceGroupRenderPassBeginInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pDeviceRenderAreas = nullptr;
    if (from->pDeviceRenderAreas)
    {
        to->pDeviceRenderAreas = (VkRect2D*)pool->alloc(from->deviceRenderAreaCount * sizeof(const VkRect2D));
        to->deviceRenderAreaCount = from->deviceRenderAreaCount;
        for (uint32_t i = 0; i < (uint32_t)from->deviceRenderAreaCount; ++i)
        {
            deepcopy_VkRect2D(pool, from->pDeviceRenderAreas + i, (VkRect2D*)(to->pDeviceRenderAreas + i));
        }
    }
}

void deepcopy_VkDeviceGroupCommandBufferBeginInfo(
    Pool* pool,
    const VkDeviceGroupCommandBufferBeginInfo* from,
    VkDeviceGroupCommandBufferBeginInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkDeviceGroupSubmitInfo(
    Pool* pool,
    const VkDeviceGroupSubmitInfo* from,
    VkDeviceGroupSubmitInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pWaitSemaphoreDeviceIndices = nullptr;
    if (from->pWaitSemaphoreDeviceIndices)
    {
        to->pWaitSemaphoreDeviceIndices = (uint32_t*)pool->dupArray(from->pWaitSemaphoreDeviceIndices, from->waitSemaphoreCount * sizeof(const uint32_t));
    }
    to->pCommandBufferDeviceMasks = nullptr;
    if (from->pCommandBufferDeviceMasks)
    {
        to->pCommandBufferDeviceMasks = (uint32_t*)pool->dupArray(from->pCommandBufferDeviceMasks, from->commandBufferCount * sizeof(const uint32_t));
    }
    to->pSignalSemaphoreDeviceIndices = nullptr;
    if (from->pSignalSemaphoreDeviceIndices)
    {
        to->pSignalSemaphoreDeviceIndices = (uint32_t*)pool->dupArray(from->pSignalSemaphoreDeviceIndices, from->signalSemaphoreCount * sizeof(const uint32_t));
    }
}

void deepcopy_VkDeviceGroupBindSparseInfo(
    Pool* pool,
    const VkDeviceGroupBindSparseInfo* from,
    VkDeviceGroupBindSparseInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkBindBufferMemoryDeviceGroupInfo(
    Pool* pool,
    const VkBindBufferMemoryDeviceGroupInfo* from,
    VkBindBufferMemoryDeviceGroupInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pDeviceIndices = nullptr;
    if (from->pDeviceIndices)
    {
        to->pDeviceIndices = (uint32_t*)pool->dupArray(from->pDeviceIndices, from->deviceIndexCount * sizeof(const uint32_t));
    }
}

void deepcopy_VkBindImageMemoryDeviceGroupInfo(
    Pool* pool,
    const VkBindImageMemoryDeviceGroupInfo* from,
    VkBindImageMemoryDeviceGroupInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pDeviceIndices = nullptr;
    if (from->pDeviceIndices)
    {
        to->pDeviceIndices = (uint32_t*)pool->dupArray(from->pDeviceIndices, from->deviceIndexCount * sizeof(const uint32_t));
    }
    to->pSplitInstanceBindRegions = nullptr;
    if (from->pSplitInstanceBindRegions)
    {
        to->pSplitInstanceBindRegions = (VkRect2D*)pool->alloc(from->splitInstanceBindRegionCount * sizeof(const VkRect2D));
        to->splitInstanceBindRegionCount = from->splitInstanceBindRegionCount;
        for (uint32_t i = 0; i < (uint32_t)from->splitInstanceBindRegionCount; ++i)
        {
            deepcopy_VkRect2D(pool, from->pSplitInstanceBindRegions + i, (VkRect2D*)(to->pSplitInstanceBindRegions + i));
        }
    }
}

void deepcopy_VkPhysicalDeviceGroupProperties(
    Pool* pool,
    const VkPhysicalDeviceGroupProperties* from,
    VkPhysicalDeviceGroupProperties* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    memcpy(to->physicalDevices, from->physicalDevices, VK_MAX_DEVICE_GROUP_SIZE * sizeof(VkPhysicalDevice));
}

void deepcopy_VkDeviceGroupDeviceCreateInfo(
    Pool* pool,
    const VkDeviceGroupDeviceCreateInfo* from,
    VkDeviceGroupDeviceCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pPhysicalDevices = nullptr;
    if (from->pPhysicalDevices)
    {
        to->pPhysicalDevices = (VkPhysicalDevice*)pool->dupArray(from->pPhysicalDevices, from->physicalDeviceCount * sizeof(const VkPhysicalDevice));
    }
}

void deepcopy_VkBufferMemoryRequirementsInfo2(
    Pool* pool,
    const VkBufferMemoryRequirementsInfo2* from,
    VkBufferMemoryRequirementsInfo2* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkImageMemoryRequirementsInfo2(
    Pool* pool,
    const VkImageMemoryRequirementsInfo2* from,
    VkImageMemoryRequirementsInfo2* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkImageSparseMemoryRequirementsInfo2(
    Pool* pool,
    const VkImageSparseMemoryRequirementsInfo2* from,
    VkImageSparseMemoryRequirementsInfo2* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkMemoryRequirements2(
    Pool* pool,
    const VkMemoryRequirements2* from,
    VkMemoryRequirements2* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkMemoryRequirements(pool, &from->memoryRequirements, (VkMemoryRequirements*)(&to->memoryRequirements));
}

void deepcopy_VkSparseImageMemoryRequirements2(
    Pool* pool,
    const VkSparseImageMemoryRequirements2* from,
    VkSparseImageMemoryRequirements2* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkSparseImageMemoryRequirements(pool, &from->memoryRequirements, (VkSparseImageMemoryRequirements*)(&to->memoryRequirements));
}

void deepcopy_VkPhysicalDeviceFeatures2(
    Pool* pool,
    const VkPhysicalDeviceFeatures2* from,
    VkPhysicalDeviceFeatures2* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkPhysicalDeviceFeatures(pool, &from->features, (VkPhysicalDeviceFeatures*)(&to->features));
}

void deepcopy_VkPhysicalDeviceProperties2(
    Pool* pool,
    const VkPhysicalDeviceProperties2* from,
    VkPhysicalDeviceProperties2* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkPhysicalDeviceProperties(pool, &from->properties, (VkPhysicalDeviceProperties*)(&to->properties));
}

void deepcopy_VkFormatProperties2(
    Pool* pool,
    const VkFormatProperties2* from,
    VkFormatProperties2* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkFormatProperties(pool, &from->formatProperties, (VkFormatProperties*)(&to->formatProperties));
}

void deepcopy_VkImageFormatProperties2(
    Pool* pool,
    const VkImageFormatProperties2* from,
    VkImageFormatProperties2* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkImageFormatProperties(pool, &from->imageFormatProperties, (VkImageFormatProperties*)(&to->imageFormatProperties));
}

void deepcopy_VkPhysicalDeviceImageFormatInfo2(
    Pool* pool,
    const VkPhysicalDeviceImageFormatInfo2* from,
    VkPhysicalDeviceImageFormatInfo2* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkQueueFamilyProperties2(
    Pool* pool,
    const VkQueueFamilyProperties2* from,
    VkQueueFamilyProperties2* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkQueueFamilyProperties(pool, &from->queueFamilyProperties, (VkQueueFamilyProperties*)(&to->queueFamilyProperties));
}

void deepcopy_VkPhysicalDeviceMemoryProperties2(
    Pool* pool,
    const VkPhysicalDeviceMemoryProperties2* from,
    VkPhysicalDeviceMemoryProperties2* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkPhysicalDeviceMemoryProperties(pool, &from->memoryProperties, (VkPhysicalDeviceMemoryProperties*)(&to->memoryProperties));
}

void deepcopy_VkSparseImageFormatProperties2(
    Pool* pool,
    const VkSparseImageFormatProperties2* from,
    VkSparseImageFormatProperties2* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkSparseImageFormatProperties(pool, &from->properties, (VkSparseImageFormatProperties*)(&to->properties));
}

void deepcopy_VkPhysicalDeviceSparseImageFormatInfo2(
    Pool* pool,
    const VkPhysicalDeviceSparseImageFormatInfo2* from,
    VkPhysicalDeviceSparseImageFormatInfo2* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPhysicalDevicePointClippingProperties(
    Pool* pool,
    const VkPhysicalDevicePointClippingProperties* from,
    VkPhysicalDevicePointClippingProperties* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkInputAttachmentAspectReference(
    Pool* pool,
    const VkInputAttachmentAspectReference* from,
    VkInputAttachmentAspectReference* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkRenderPassInputAttachmentAspectCreateInfo(
    Pool* pool,
    const VkRenderPassInputAttachmentAspectCreateInfo* from,
    VkRenderPassInputAttachmentAspectCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pAspectReferences = nullptr;
    if (from->pAspectReferences)
    {
        to->pAspectReferences = (VkInputAttachmentAspectReference*)pool->alloc(from->aspectReferenceCount * sizeof(const VkInputAttachmentAspectReference));
        to->aspectReferenceCount = from->aspectReferenceCount;
        for (uint32_t i = 0; i < (uint32_t)from->aspectReferenceCount; ++i)
        {
            deepcopy_VkInputAttachmentAspectReference(pool, from->pAspectReferences + i, (VkInputAttachmentAspectReference*)(to->pAspectReferences + i));
        }
    }
}

void deepcopy_VkImageViewUsageCreateInfo(
    Pool* pool,
    const VkImageViewUsageCreateInfo* from,
    VkImageViewUsageCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPipelineTessellationDomainOriginStateCreateInfo(
    Pool* pool,
    const VkPipelineTessellationDomainOriginStateCreateInfo* from,
    VkPipelineTessellationDomainOriginStateCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkRenderPassMultiviewCreateInfo(
    Pool* pool,
    const VkRenderPassMultiviewCreateInfo* from,
    VkRenderPassMultiviewCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pViewMasks = nullptr;
    if (from->pViewMasks)
    {
        to->pViewMasks = (uint32_t*)pool->dupArray(from->pViewMasks, from->subpassCount * sizeof(const uint32_t));
    }
    to->pViewOffsets = nullptr;
    if (from->pViewOffsets)
    {
        to->pViewOffsets = (int32_t*)pool->dupArray(from->pViewOffsets, from->dependencyCount * sizeof(const int32_t));
    }
    to->pCorrelationMasks = nullptr;
    if (from->pCorrelationMasks)
    {
        to->pCorrelationMasks = (uint32_t*)pool->dupArray(from->pCorrelationMasks, from->correlationMaskCount * sizeof(const uint32_t));
    }
}

void deepcopy_VkPhysicalDeviceMultiviewFeatures(
    Pool* pool,
    const VkPhysicalDeviceMultiviewFeatures* from,
    VkPhysicalDeviceMultiviewFeatures* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPhysicalDeviceMultiviewProperties(
    Pool* pool,
    const VkPhysicalDeviceMultiviewProperties* from,
    VkPhysicalDeviceMultiviewProperties* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPhysicalDeviceVariablePointerFeatures(
    Pool* pool,
    const VkPhysicalDeviceVariablePointerFeatures* from,
    VkPhysicalDeviceVariablePointerFeatures* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPhysicalDeviceProtectedMemoryFeatures(
    Pool* pool,
    const VkPhysicalDeviceProtectedMemoryFeatures* from,
    VkPhysicalDeviceProtectedMemoryFeatures* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPhysicalDeviceProtectedMemoryProperties(
    Pool* pool,
    const VkPhysicalDeviceProtectedMemoryProperties* from,
    VkPhysicalDeviceProtectedMemoryProperties* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkDeviceQueueInfo2(
    Pool* pool,
    const VkDeviceQueueInfo2* from,
    VkDeviceQueueInfo2* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkProtectedSubmitInfo(
    Pool* pool,
    const VkProtectedSubmitInfo* from,
    VkProtectedSubmitInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkSamplerYcbcrConversionCreateInfo(
    Pool* pool,
    const VkSamplerYcbcrConversionCreateInfo* from,
    VkSamplerYcbcrConversionCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkComponentMapping(pool, &from->components, (VkComponentMapping*)(&to->components));
}

void deepcopy_VkSamplerYcbcrConversionInfo(
    Pool* pool,
    const VkSamplerYcbcrConversionInfo* from,
    VkSamplerYcbcrConversionInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkBindImagePlaneMemoryInfo(
    Pool* pool,
    const VkBindImagePlaneMemoryInfo* from,
    VkBindImagePlaneMemoryInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkImagePlaneMemoryRequirementsInfo(
    Pool* pool,
    const VkImagePlaneMemoryRequirementsInfo* from,
    VkImagePlaneMemoryRequirementsInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPhysicalDeviceSamplerYcbcrConversionFeatures(
    Pool* pool,
    const VkPhysicalDeviceSamplerYcbcrConversionFeatures* from,
    VkPhysicalDeviceSamplerYcbcrConversionFeatures* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkSamplerYcbcrConversionImageFormatProperties(
    Pool* pool,
    const VkSamplerYcbcrConversionImageFormatProperties* from,
    VkSamplerYcbcrConversionImageFormatProperties* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkDescriptorUpdateTemplateEntry(
    Pool* pool,
    const VkDescriptorUpdateTemplateEntry* from,
    VkDescriptorUpdateTemplateEntry* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkDescriptorUpdateTemplateCreateInfo(
    Pool* pool,
    const VkDescriptorUpdateTemplateCreateInfo* from,
    VkDescriptorUpdateTemplateCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pDescriptorUpdateEntries = nullptr;
    if (from->pDescriptorUpdateEntries)
    {
        to->pDescriptorUpdateEntries = (VkDescriptorUpdateTemplateEntry*)pool->alloc(from->descriptorUpdateEntryCount * sizeof(const VkDescriptorUpdateTemplateEntry));
        to->descriptorUpdateEntryCount = from->descriptorUpdateEntryCount;
        for (uint32_t i = 0; i < (uint32_t)from->descriptorUpdateEntryCount; ++i)
        {
            deepcopy_VkDescriptorUpdateTemplateEntry(pool, from->pDescriptorUpdateEntries + i, (VkDescriptorUpdateTemplateEntry*)(to->pDescriptorUpdateEntries + i));
        }
    }
}

void deepcopy_VkExternalMemoryProperties(
    Pool* pool,
    const VkExternalMemoryProperties* from,
    VkExternalMemoryProperties* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkPhysicalDeviceExternalImageFormatInfo(
    Pool* pool,
    const VkPhysicalDeviceExternalImageFormatInfo* from,
    VkPhysicalDeviceExternalImageFormatInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkExternalImageFormatProperties(
    Pool* pool,
    const VkExternalImageFormatProperties* from,
    VkExternalImageFormatProperties* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkExternalMemoryProperties(pool, &from->externalMemoryProperties, (VkExternalMemoryProperties*)(&to->externalMemoryProperties));
}

void deepcopy_VkPhysicalDeviceExternalBufferInfo(
    Pool* pool,
    const VkPhysicalDeviceExternalBufferInfo* from,
    VkPhysicalDeviceExternalBufferInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkExternalBufferProperties(
    Pool* pool,
    const VkExternalBufferProperties* from,
    VkExternalBufferProperties* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkExternalMemoryProperties(pool, &from->externalMemoryProperties, (VkExternalMemoryProperties*)(&to->externalMemoryProperties));
}

void deepcopy_VkPhysicalDeviceIDProperties(
    Pool* pool,
    const VkPhysicalDeviceIDProperties* from,
    VkPhysicalDeviceIDProperties* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    memcpy(to->deviceUUID, from->deviceUUID, VK_UUID_SIZE * sizeof(uint8_t));
    memcpy(to->driverUUID, from->driverUUID, VK_UUID_SIZE * sizeof(uint8_t));
    memcpy(to->deviceLUID, from->deviceLUID, VK_LUID_SIZE * sizeof(uint8_t));
}

void deepcopy_VkExternalMemoryImageCreateInfo(
    Pool* pool,
    const VkExternalMemoryImageCreateInfo* from,
    VkExternalMemoryImageCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkExternalMemoryBufferCreateInfo(
    Pool* pool,
    const VkExternalMemoryBufferCreateInfo* from,
    VkExternalMemoryBufferCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkExportMemoryAllocateInfo(
    Pool* pool,
    const VkExportMemoryAllocateInfo* from,
    VkExportMemoryAllocateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPhysicalDeviceExternalFenceInfo(
    Pool* pool,
    const VkPhysicalDeviceExternalFenceInfo* from,
    VkPhysicalDeviceExternalFenceInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkExternalFenceProperties(
    Pool* pool,
    const VkExternalFenceProperties* from,
    VkExternalFenceProperties* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkExportFenceCreateInfo(
    Pool* pool,
    const VkExportFenceCreateInfo* from,
    VkExportFenceCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkExportSemaphoreCreateInfo(
    Pool* pool,
    const VkExportSemaphoreCreateInfo* from,
    VkExportSemaphoreCreateInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPhysicalDeviceExternalSemaphoreInfo(
    Pool* pool,
    const VkPhysicalDeviceExternalSemaphoreInfo* from,
    VkPhysicalDeviceExternalSemaphoreInfo* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkExternalSemaphoreProperties(
    Pool* pool,
    const VkExternalSemaphoreProperties* from,
    VkExternalSemaphoreProperties* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPhysicalDeviceMaintenance3Properties(
    Pool* pool,
    const VkPhysicalDeviceMaintenance3Properties* from,
    VkPhysicalDeviceMaintenance3Properties* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkDescriptorSetLayoutSupport(
    Pool* pool,
    const VkDescriptorSetLayoutSupport* from,
    VkDescriptorSetLayoutSupport* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPhysicalDeviceShaderDrawParameterFeatures(
    Pool* pool,
    const VkPhysicalDeviceShaderDrawParameterFeatures* from,
    VkPhysicalDeviceShaderDrawParameterFeatures* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_KHR_surface
void deepcopy_VkSurfaceCapabilitiesKHR(
    Pool* pool,
    const VkSurfaceCapabilitiesKHR* from,
    VkSurfaceCapabilitiesKHR* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkExtent2D(pool, &from->currentExtent, (VkExtent2D*)(&to->currentExtent));
    deepcopy_VkExtent2D(pool, &from->minImageExtent, (VkExtent2D*)(&to->minImageExtent));
    deepcopy_VkExtent2D(pool, &from->maxImageExtent, (VkExtent2D*)(&to->maxImageExtent));
}

void deepcopy_VkSurfaceFormatKHR(
    Pool* pool,
    const VkSurfaceFormatKHR* from,
    VkSurfaceFormatKHR* to)
{
    (void)pool;
    *to = *from;
}

#endif
#ifdef VK_KHR_swapchain
void deepcopy_VkSwapchainCreateInfoKHR(
    Pool* pool,
    const VkSwapchainCreateInfoKHR* from,
    VkSwapchainCreateInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkExtent2D(pool, &from->imageExtent, (VkExtent2D*)(&to->imageExtent));
    to->pQueueFamilyIndices = nullptr;
    if (from->pQueueFamilyIndices)
    {
        to->pQueueFamilyIndices = (uint32_t*)pool->dupArray(from->pQueueFamilyIndices, from->queueFamilyIndexCount * sizeof(const uint32_t));
    }
}

void deepcopy_VkPresentInfoKHR(
    Pool* pool,
    const VkPresentInfoKHR* from,
    VkPresentInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pWaitSemaphores = nullptr;
    if (from->pWaitSemaphores)
    {
        to->pWaitSemaphores = (VkSemaphore*)pool->dupArray(from->pWaitSemaphores, from->waitSemaphoreCount * sizeof(const VkSemaphore));
    }
    to->pSwapchains = nullptr;
    if (from->pSwapchains)
    {
        to->pSwapchains = (VkSwapchainKHR*)pool->dupArray(from->pSwapchains, from->swapchainCount * sizeof(const VkSwapchainKHR));
    }
    to->pImageIndices = nullptr;
    if (from->pImageIndices)
    {
        to->pImageIndices = (uint32_t*)pool->dupArray(from->pImageIndices, from->swapchainCount * sizeof(const uint32_t));
    }
    to->pResults = nullptr;
    if (from->pResults)
    {
        to->pResults = (VkResult*)pool->dupArray(from->pResults, from->swapchainCount * sizeof(VkResult));
    }
}

void deepcopy_VkImageSwapchainCreateInfoKHR(
    Pool* pool,
    const VkImageSwapchainCreateInfoKHR* from,
    VkImageSwapchainCreateInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkBindImageMemorySwapchainInfoKHR(
    Pool* pool,
    const VkBindImageMemorySwapchainInfoKHR* from,
    VkBindImageMemorySwapchainInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkAcquireNextImageInfoKHR(
    Pool* pool,
    const VkAcquireNextImageInfoKHR* from,
    VkAcquireNextImageInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkDeviceGroupPresentCapabilitiesKHR(
    Pool* pool,
    const VkDeviceGroupPresentCapabilitiesKHR* from,
    VkDeviceGroupPresentCapabilitiesKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    memcpy(to->presentMask, from->presentMask, VK_MAX_DEVICE_GROUP_SIZE * sizeof(uint32_t));
}

void deepcopy_VkDeviceGroupPresentInfoKHR(
    Pool* pool,
    const VkDeviceGroupPresentInfoKHR* from,
    VkDeviceGroupPresentInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pDeviceMasks = nullptr;
    if (from->pDeviceMasks)
    {
        to->pDeviceMasks = (uint32_t*)pool->dupArray(from->pDeviceMasks, from->swapchainCount * sizeof(const uint32_t));
    }
}

void deepcopy_VkDeviceGroupSwapchainCreateInfoKHR(
    Pool* pool,
    const VkDeviceGroupSwapchainCreateInfoKHR* from,
    VkDeviceGroupSwapchainCreateInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_KHR_display
void deepcopy_VkDisplayPropertiesKHR(
    Pool* pool,
    const VkDisplayPropertiesKHR* from,
    VkDisplayPropertiesKHR* to)
{
    (void)pool;
    *to = *from;
    to->displayName = nullptr;
    if (from->displayName)
    {
        to->displayName = pool->strDup(from->displayName);
    }
    deepcopy_VkExtent2D(pool, &from->physicalDimensions, (VkExtent2D*)(&to->physicalDimensions));
    deepcopy_VkExtent2D(pool, &from->physicalResolution, (VkExtent2D*)(&to->physicalResolution));
}

void deepcopy_VkDisplayModeParametersKHR(
    Pool* pool,
    const VkDisplayModeParametersKHR* from,
    VkDisplayModeParametersKHR* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkExtent2D(pool, &from->visibleRegion, (VkExtent2D*)(&to->visibleRegion));
}

void deepcopy_VkDisplayModePropertiesKHR(
    Pool* pool,
    const VkDisplayModePropertiesKHR* from,
    VkDisplayModePropertiesKHR* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkDisplayModeParametersKHR(pool, &from->parameters, (VkDisplayModeParametersKHR*)(&to->parameters));
}

void deepcopy_VkDisplayModeCreateInfoKHR(
    Pool* pool,
    const VkDisplayModeCreateInfoKHR* from,
    VkDisplayModeCreateInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkDisplayModeParametersKHR(pool, &from->parameters, (VkDisplayModeParametersKHR*)(&to->parameters));
}

void deepcopy_VkDisplayPlaneCapabilitiesKHR(
    Pool* pool,
    const VkDisplayPlaneCapabilitiesKHR* from,
    VkDisplayPlaneCapabilitiesKHR* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkOffset2D(pool, &from->minSrcPosition, (VkOffset2D*)(&to->minSrcPosition));
    deepcopy_VkOffset2D(pool, &from->maxSrcPosition, (VkOffset2D*)(&to->maxSrcPosition));
    deepcopy_VkExtent2D(pool, &from->minSrcExtent, (VkExtent2D*)(&to->minSrcExtent));
    deepcopy_VkExtent2D(pool, &from->maxSrcExtent, (VkExtent2D*)(&to->maxSrcExtent));
    deepcopy_VkOffset2D(pool, &from->minDstPosition, (VkOffset2D*)(&to->minDstPosition));
    deepcopy_VkOffset2D(pool, &from->maxDstPosition, (VkOffset2D*)(&to->maxDstPosition));
    deepcopy_VkExtent2D(pool, &from->minDstExtent, (VkExtent2D*)(&to->minDstExtent));
    deepcopy_VkExtent2D(pool, &from->maxDstExtent, (VkExtent2D*)(&to->maxDstExtent));
}

void deepcopy_VkDisplayPlanePropertiesKHR(
    Pool* pool,
    const VkDisplayPlanePropertiesKHR* from,
    VkDisplayPlanePropertiesKHR* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkDisplaySurfaceCreateInfoKHR(
    Pool* pool,
    const VkDisplaySurfaceCreateInfoKHR* from,
    VkDisplaySurfaceCreateInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkExtent2D(pool, &from->imageExtent, (VkExtent2D*)(&to->imageExtent));
}

#endif
#ifdef VK_KHR_display_swapchain
void deepcopy_VkDisplayPresentInfoKHR(
    Pool* pool,
    const VkDisplayPresentInfoKHR* from,
    VkDisplayPresentInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkRect2D(pool, &from->srcRect, (VkRect2D*)(&to->srcRect));
    deepcopy_VkRect2D(pool, &from->dstRect, (VkRect2D*)(&to->dstRect));
}

#endif
#ifdef VK_KHR_xlib_surface
void deepcopy_VkXlibSurfaceCreateInfoKHR(
    Pool* pool,
    const VkXlibSurfaceCreateInfoKHR* from,
    VkXlibSurfaceCreateInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->dpy = nullptr;
    if (from->dpy)
    {
        to->dpy = (Display*)pool->dupArray(from->dpy, sizeof(Display));
    }
}

#endif
#ifdef VK_KHR_xcb_surface
void deepcopy_VkXcbSurfaceCreateInfoKHR(
    Pool* pool,
    const VkXcbSurfaceCreateInfoKHR* from,
    VkXcbSurfaceCreateInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->connection = nullptr;
    if (from->connection)
    {
        to->connection = (xcb_connection_t*)pool->dupArray(from->connection, sizeof(xcb_connection_t));
    }
}

#endif
#ifdef VK_KHR_wayland_surface
void deepcopy_VkWaylandSurfaceCreateInfoKHR(
    Pool* pool,
    const VkWaylandSurfaceCreateInfoKHR* from,
    VkWaylandSurfaceCreateInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->display = nullptr;
    if (from->display)
    {
        to->display = (wl_display*)pool->dupArray(from->display, sizeof(wl_display));
    }
    to->surface = nullptr;
    if (from->surface)
    {
        to->surface = (wl_surface*)pool->dupArray(from->surface, sizeof(wl_surface));
    }
}

#endif
#ifdef VK_KHR_mir_surface
void deepcopy_VkMirSurfaceCreateInfoKHR(
    Pool* pool,
    const VkMirSurfaceCreateInfoKHR* from,
    VkMirSurfaceCreateInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->connection = nullptr;
    if (from->connection)
    {
        to->connection = (MirConnection*)pool->dupArray(from->connection, sizeof(MirConnection));
    }
    to->mirSurface = nullptr;
    if (from->mirSurface)
    {
        to->mirSurface = (MirSurface*)pool->dupArray(from->mirSurface, sizeof(MirSurface));
    }
}

#endif
#ifdef VK_KHR_android_surface
void deepcopy_VkAndroidSurfaceCreateInfoKHR(
    Pool* pool,
    const VkAndroidSurfaceCreateInfoKHR* from,
    VkAndroidSurfaceCreateInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->window = nullptr;
    if (from->window)
    {
        to->window = (ANativeWindow*)pool->dupArray(from->window, sizeof(ANativeWindow));
    }
}

#endif
#ifdef VK_KHR_win32_surface
void deepcopy_VkWin32SurfaceCreateInfoKHR(
    Pool* pool,
    const VkWin32SurfaceCreateInfoKHR* from,
    VkWin32SurfaceCreateInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_KHR_sampler_mirror_clamp_to_edge
#endif
#ifdef VK_KHR_multiview
#endif
#ifdef VK_KHR_get_physical_device_properties2
#endif
#ifdef VK_KHR_device_group
#endif
#ifdef VK_KHR_shader_draw_parameters
#endif
#ifdef VK_KHR_maintenance1
#endif
#ifdef VK_KHR_device_group_creation
#endif
#ifdef VK_KHR_external_memory_capabilities
#endif
#ifdef VK_KHR_external_memory
#endif
#ifdef VK_KHR_external_memory_win32
void deepcopy_VkImportMemoryWin32HandleInfoKHR(
    Pool* pool,
    const VkImportMemoryWin32HandleInfoKHR* from,
    VkImportMemoryWin32HandleInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkExportMemoryWin32HandleInfoKHR(
    Pool* pool,
    const VkExportMemoryWin32HandleInfoKHR* from,
    VkExportMemoryWin32HandleInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pAttributes = nullptr;
    if (from->pAttributes)
    {
        to->pAttributes = (SECURITY_ATTRIBUTES*)pool->dupArray(from->pAttributes, sizeof(const SECURITY_ATTRIBUTES));
    }
}

void deepcopy_VkMemoryWin32HandlePropertiesKHR(
    Pool* pool,
    const VkMemoryWin32HandlePropertiesKHR* from,
    VkMemoryWin32HandlePropertiesKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkMemoryGetWin32HandleInfoKHR(
    Pool* pool,
    const VkMemoryGetWin32HandleInfoKHR* from,
    VkMemoryGetWin32HandleInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_KHR_external_memory_fd
void deepcopy_VkImportMemoryFdInfoKHR(
    Pool* pool,
    const VkImportMemoryFdInfoKHR* from,
    VkImportMemoryFdInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkMemoryFdPropertiesKHR(
    Pool* pool,
    const VkMemoryFdPropertiesKHR* from,
    VkMemoryFdPropertiesKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkMemoryGetFdInfoKHR(
    Pool* pool,
    const VkMemoryGetFdInfoKHR* from,
    VkMemoryGetFdInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_KHR_win32_keyed_mutex
void deepcopy_VkWin32KeyedMutexAcquireReleaseInfoKHR(
    Pool* pool,
    const VkWin32KeyedMutexAcquireReleaseInfoKHR* from,
    VkWin32KeyedMutexAcquireReleaseInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pAcquireSyncs = nullptr;
    if (from->pAcquireSyncs)
    {
        to->pAcquireSyncs = (VkDeviceMemory*)pool->dupArray(from->pAcquireSyncs, from->acquireCount * sizeof(const VkDeviceMemory));
    }
    to->pAcquireKeys = nullptr;
    if (from->pAcquireKeys)
    {
        to->pAcquireKeys = (uint64_t*)pool->dupArray(from->pAcquireKeys, from->acquireCount * sizeof(const uint64_t));
    }
    to->pAcquireTimeouts = nullptr;
    if (from->pAcquireTimeouts)
    {
        to->pAcquireTimeouts = (uint32_t*)pool->dupArray(from->pAcquireTimeouts, from->acquireCount * sizeof(const uint32_t));
    }
    to->pReleaseSyncs = nullptr;
    if (from->pReleaseSyncs)
    {
        to->pReleaseSyncs = (VkDeviceMemory*)pool->dupArray(from->pReleaseSyncs, from->releaseCount * sizeof(const VkDeviceMemory));
    }
    to->pReleaseKeys = nullptr;
    if (from->pReleaseKeys)
    {
        to->pReleaseKeys = (uint64_t*)pool->dupArray(from->pReleaseKeys, from->releaseCount * sizeof(const uint64_t));
    }
}

#endif
#ifdef VK_KHR_external_semaphore_capabilities
#endif
#ifdef VK_KHR_external_semaphore
#endif
#ifdef VK_KHR_external_semaphore_win32
void deepcopy_VkImportSemaphoreWin32HandleInfoKHR(
    Pool* pool,
    const VkImportSemaphoreWin32HandleInfoKHR* from,
    VkImportSemaphoreWin32HandleInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkExportSemaphoreWin32HandleInfoKHR(
    Pool* pool,
    const VkExportSemaphoreWin32HandleInfoKHR* from,
    VkExportSemaphoreWin32HandleInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pAttributes = nullptr;
    if (from->pAttributes)
    {
        to->pAttributes = (SECURITY_ATTRIBUTES*)pool->dupArray(from->pAttributes, sizeof(const SECURITY_ATTRIBUTES));
    }
}

void deepcopy_VkD3D12FenceSubmitInfoKHR(
    Pool* pool,
    const VkD3D12FenceSubmitInfoKHR* from,
    VkD3D12FenceSubmitInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pWaitSemaphoreValues = nullptr;
    if (from->pWaitSemaphoreValues)
    {
        to->pWaitSemaphoreValues = (uint64_t*)pool->dupArray(from->pWaitSemaphoreValues, from->waitSemaphoreValuesCount * sizeof(const uint64_t));
    }
    to->pSignalSemaphoreValues = nullptr;
    if (from->pSignalSemaphoreValues)
    {
        to->pSignalSemaphoreValues = (uint64_t*)pool->dupArray(from->pSignalSemaphoreValues, from->signalSemaphoreValuesCount * sizeof(const uint64_t));
    }
}

void deepcopy_VkSemaphoreGetWin32HandleInfoKHR(
    Pool* pool,
    const VkSemaphoreGetWin32HandleInfoKHR* from,
    VkSemaphoreGetWin32HandleInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_KHR_external_semaphore_fd
void deepcopy_VkImportSemaphoreFdInfoKHR(
    Pool* pool,
    const VkImportSemaphoreFdInfoKHR* from,
    VkImportSemaphoreFdInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkSemaphoreGetFdInfoKHR(
    Pool* pool,
    const VkSemaphoreGetFdInfoKHR* from,
    VkSemaphoreGetFdInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_KHR_push_descriptor
void deepcopy_VkPhysicalDevicePushDescriptorPropertiesKHR(
    Pool* pool,
    const VkPhysicalDevicePushDescriptorPropertiesKHR* from,
    VkPhysicalDevicePushDescriptorPropertiesKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_KHR_16bit_storage
#endif
#ifdef VK_KHR_incremental_present
void deepcopy_VkRectLayerKHR(
    Pool* pool,
    const VkRectLayerKHR* from,
    VkRectLayerKHR* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkOffset2D(pool, &from->offset, (VkOffset2D*)(&to->offset));
    deepcopy_VkExtent2D(pool, &from->extent, (VkExtent2D*)(&to->extent));
}

void deepcopy_VkPresentRegionKHR(
    Pool* pool,
    const VkPresentRegionKHR* from,
    VkPresentRegionKHR* to)
{
    (void)pool;
    *to = *from;
    to->pRectangles = nullptr;
    if (from->pRectangles)
    {
        to->pRectangles = (VkRectLayerKHR*)pool->alloc(from->rectangleCount * sizeof(const VkRectLayerKHR));
        to->rectangleCount = from->rectangleCount;
        for (uint32_t i = 0; i < (uint32_t)from->rectangleCount; ++i)
        {
            deepcopy_VkRectLayerKHR(pool, from->pRectangles + i, (VkRectLayerKHR*)(to->pRectangles + i));
        }
    }
}

void deepcopy_VkPresentRegionsKHR(
    Pool* pool,
    const VkPresentRegionsKHR* from,
    VkPresentRegionsKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pRegions = nullptr;
    if (from->pRegions)
    {
        to->pRegions = (VkPresentRegionKHR*)pool->alloc(from->swapchainCount * sizeof(const VkPresentRegionKHR));
        to->swapchainCount = from->swapchainCount;
        for (uint32_t i = 0; i < (uint32_t)from->swapchainCount; ++i)
        {
            deepcopy_VkPresentRegionKHR(pool, from->pRegions + i, (VkPresentRegionKHR*)(to->pRegions + i));
        }
    }
}

#endif
#ifdef VK_KHR_descriptor_update_template
#endif
#ifdef VK_KHR_create_renderpass2
void deepcopy_VkAttachmentDescription2KHR(
    Pool* pool,
    const VkAttachmentDescription2KHR* from,
    VkAttachmentDescription2KHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkAttachmentReference2KHR(
    Pool* pool,
    const VkAttachmentReference2KHR* from,
    VkAttachmentReference2KHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkSubpassDescription2KHR(
    Pool* pool,
    const VkSubpassDescription2KHR* from,
    VkSubpassDescription2KHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pInputAttachments = nullptr;
    if (from->pInputAttachments)
    {
        to->pInputAttachments = (VkAttachmentReference2KHR*)pool->alloc(from->inputAttachmentCount * sizeof(const VkAttachmentReference2KHR));
        to->inputAttachmentCount = from->inputAttachmentCount;
        for (uint32_t i = 0; i < (uint32_t)from->inputAttachmentCount; ++i)
        {
            deepcopy_VkAttachmentReference2KHR(pool, from->pInputAttachments + i, (VkAttachmentReference2KHR*)(to->pInputAttachments + i));
        }
    }
    to->pColorAttachments = nullptr;
    if (from->pColorAttachments)
    {
        to->pColorAttachments = (VkAttachmentReference2KHR*)pool->alloc(from->colorAttachmentCount * sizeof(const VkAttachmentReference2KHR));
        to->colorAttachmentCount = from->colorAttachmentCount;
        for (uint32_t i = 0; i < (uint32_t)from->colorAttachmentCount; ++i)
        {
            deepcopy_VkAttachmentReference2KHR(pool, from->pColorAttachments + i, (VkAttachmentReference2KHR*)(to->pColorAttachments + i));
        }
    }
    to->pResolveAttachments = nullptr;
    if (from->pResolveAttachments)
    {
        to->pResolveAttachments = (VkAttachmentReference2KHR*)pool->alloc(from->colorAttachmentCount * sizeof(const VkAttachmentReference2KHR));
        to->colorAttachmentCount = from->colorAttachmentCount;
        for (uint32_t i = 0; i < (uint32_t)from->colorAttachmentCount; ++i)
        {
            deepcopy_VkAttachmentReference2KHR(pool, from->pResolveAttachments + i, (VkAttachmentReference2KHR*)(to->pResolveAttachments + i));
        }
    }
    to->pDepthStencilAttachment = nullptr;
    if (from->pDepthStencilAttachment)
    {
        to->pDepthStencilAttachment = (VkAttachmentReference2KHR*)pool->alloc(sizeof(const VkAttachmentReference2KHR));
        deepcopy_VkAttachmentReference2KHR(pool, from->pDepthStencilAttachment, (VkAttachmentReference2KHR*)(to->pDepthStencilAttachment));
    }
    to->pPreserveAttachments = nullptr;
    if (from->pPreserveAttachments)
    {
        to->pPreserveAttachments = (uint32_t*)pool->dupArray(from->pPreserveAttachments, from->preserveAttachmentCount * sizeof(const uint32_t));
    }
}

void deepcopy_VkSubpassDependency2KHR(
    Pool* pool,
    const VkSubpassDependency2KHR* from,
    VkSubpassDependency2KHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkRenderPassCreateInfo2KHR(
    Pool* pool,
    const VkRenderPassCreateInfo2KHR* from,
    VkRenderPassCreateInfo2KHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pAttachments = nullptr;
    if (from->pAttachments)
    {
        to->pAttachments = (VkAttachmentDescription2KHR*)pool->alloc(from->attachmentCount * sizeof(const VkAttachmentDescription2KHR));
        to->attachmentCount = from->attachmentCount;
        for (uint32_t i = 0; i < (uint32_t)from->attachmentCount; ++i)
        {
            deepcopy_VkAttachmentDescription2KHR(pool, from->pAttachments + i, (VkAttachmentDescription2KHR*)(to->pAttachments + i));
        }
    }
    to->pSubpasses = nullptr;
    if (from->pSubpasses)
    {
        to->pSubpasses = (VkSubpassDescription2KHR*)pool->alloc(from->subpassCount * sizeof(const VkSubpassDescription2KHR));
        to->subpassCount = from->subpassCount;
        for (uint32_t i = 0; i < (uint32_t)from->subpassCount; ++i)
        {
            deepcopy_VkSubpassDescription2KHR(pool, from->pSubpasses + i, (VkSubpassDescription2KHR*)(to->pSubpasses + i));
        }
    }
    to->pDependencies = nullptr;
    if (from->pDependencies)
    {
        to->pDependencies = (VkSubpassDependency2KHR*)pool->alloc(from->dependencyCount * sizeof(const VkSubpassDependency2KHR));
        to->dependencyCount = from->dependencyCount;
        for (uint32_t i = 0; i < (uint32_t)from->dependencyCount; ++i)
        {
            deepcopy_VkSubpassDependency2KHR(pool, from->pDependencies + i, (VkSubpassDependency2KHR*)(to->pDependencies + i));
        }
    }
    to->pCorrelatedViewMasks = nullptr;
    if (from->pCorrelatedViewMasks)
    {
        to->pCorrelatedViewMasks = (uint32_t*)pool->dupArray(from->pCorrelatedViewMasks, from->correlatedViewMaskCount * sizeof(const uint32_t));
    }
}

void deepcopy_VkSubpassBeginInfoKHR(
    Pool* pool,
    const VkSubpassBeginInfoKHR* from,
    VkSubpassBeginInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkSubpassEndInfoKHR(
    Pool* pool,
    const VkSubpassEndInfoKHR* from,
    VkSubpassEndInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_KHR_shared_presentable_image
void deepcopy_VkSharedPresentSurfaceCapabilitiesKHR(
    Pool* pool,
    const VkSharedPresentSurfaceCapabilitiesKHR* from,
    VkSharedPresentSurfaceCapabilitiesKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_KHR_external_fence_capabilities
#endif
#ifdef VK_KHR_external_fence
#endif
#ifdef VK_KHR_external_fence_win32
void deepcopy_VkImportFenceWin32HandleInfoKHR(
    Pool* pool,
    const VkImportFenceWin32HandleInfoKHR* from,
    VkImportFenceWin32HandleInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkExportFenceWin32HandleInfoKHR(
    Pool* pool,
    const VkExportFenceWin32HandleInfoKHR* from,
    VkExportFenceWin32HandleInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pAttributes = nullptr;
    if (from->pAttributes)
    {
        to->pAttributes = (SECURITY_ATTRIBUTES*)pool->dupArray(from->pAttributes, sizeof(const SECURITY_ATTRIBUTES));
    }
}

void deepcopy_VkFenceGetWin32HandleInfoKHR(
    Pool* pool,
    const VkFenceGetWin32HandleInfoKHR* from,
    VkFenceGetWin32HandleInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_KHR_external_fence_fd
void deepcopy_VkImportFenceFdInfoKHR(
    Pool* pool,
    const VkImportFenceFdInfoKHR* from,
    VkImportFenceFdInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkFenceGetFdInfoKHR(
    Pool* pool,
    const VkFenceGetFdInfoKHR* from,
    VkFenceGetFdInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_KHR_maintenance2
#endif
#ifdef VK_KHR_get_surface_capabilities2
void deepcopy_VkPhysicalDeviceSurfaceInfo2KHR(
    Pool* pool,
    const VkPhysicalDeviceSurfaceInfo2KHR* from,
    VkPhysicalDeviceSurfaceInfo2KHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkSurfaceCapabilities2KHR(
    Pool* pool,
    const VkSurfaceCapabilities2KHR* from,
    VkSurfaceCapabilities2KHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkSurfaceCapabilitiesKHR(pool, &from->surfaceCapabilities, (VkSurfaceCapabilitiesKHR*)(&to->surfaceCapabilities));
}

void deepcopy_VkSurfaceFormat2KHR(
    Pool* pool,
    const VkSurfaceFormat2KHR* from,
    VkSurfaceFormat2KHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkSurfaceFormatKHR(pool, &from->surfaceFormat, (VkSurfaceFormatKHR*)(&to->surfaceFormat));
}

#endif
#ifdef VK_KHR_variable_pointers
#endif
#ifdef VK_KHR_get_display_properties2
void deepcopy_VkDisplayProperties2KHR(
    Pool* pool,
    const VkDisplayProperties2KHR* from,
    VkDisplayProperties2KHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkDisplayPropertiesKHR(pool, &from->displayProperties, (VkDisplayPropertiesKHR*)(&to->displayProperties));
}

void deepcopy_VkDisplayPlaneProperties2KHR(
    Pool* pool,
    const VkDisplayPlaneProperties2KHR* from,
    VkDisplayPlaneProperties2KHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkDisplayPlanePropertiesKHR(pool, &from->displayPlaneProperties, (VkDisplayPlanePropertiesKHR*)(&to->displayPlaneProperties));
}

void deepcopy_VkDisplayModeProperties2KHR(
    Pool* pool,
    const VkDisplayModeProperties2KHR* from,
    VkDisplayModeProperties2KHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkDisplayModePropertiesKHR(pool, &from->displayModeProperties, (VkDisplayModePropertiesKHR*)(&to->displayModeProperties));
}

void deepcopy_VkDisplayPlaneInfo2KHR(
    Pool* pool,
    const VkDisplayPlaneInfo2KHR* from,
    VkDisplayPlaneInfo2KHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkDisplayPlaneCapabilities2KHR(
    Pool* pool,
    const VkDisplayPlaneCapabilities2KHR* from,
    VkDisplayPlaneCapabilities2KHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkDisplayPlaneCapabilitiesKHR(pool, &from->capabilities, (VkDisplayPlaneCapabilitiesKHR*)(&to->capabilities));
}

#endif
#ifdef VK_KHR_dedicated_allocation
#endif
#ifdef VK_KHR_storage_buffer_storage_class
#endif
#ifdef VK_KHR_relaxed_block_layout
#endif
#ifdef VK_KHR_get_memory_requirements2
#endif
#ifdef VK_KHR_image_format_list
void deepcopy_VkImageFormatListCreateInfoKHR(
    Pool* pool,
    const VkImageFormatListCreateInfoKHR* from,
    VkImageFormatListCreateInfoKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pViewFormats = nullptr;
    if (from->pViewFormats)
    {
        to->pViewFormats = (VkFormat*)pool->dupArray(from->pViewFormats, from->viewFormatCount * sizeof(const VkFormat));
    }
}

#endif
#ifdef VK_KHR_sampler_ycbcr_conversion
#endif
#ifdef VK_KHR_bind_memory2
#endif
#ifdef VK_KHR_maintenance3
#endif
#ifdef VK_KHR_draw_indirect_count
#endif
#ifdef VK_KHR_8bit_storage
void deepcopy_VkPhysicalDevice8BitStorageFeaturesKHR(
    Pool* pool,
    const VkPhysicalDevice8BitStorageFeaturesKHR* from,
    VkPhysicalDevice8BitStorageFeaturesKHR* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_ANDROID_native_buffer
void deepcopy_VkNativeBufferANDROID(
    Pool* pool,
    const VkNativeBufferANDROID* from,
    VkNativeBufferANDROID* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->handle = nullptr;
    if (from->handle)
    {
        to->handle = (uint32_t*)pool->dupArray(from->handle, sizeof(const uint32_t));
    }
}

#endif
#ifdef VK_EXT_debug_report
void deepcopy_VkDebugReportCallbackCreateInfoEXT(
    Pool* pool,
    const VkDebugReportCallbackCreateInfoEXT* from,
    VkDebugReportCallbackCreateInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pUserData = nullptr;
    if (from->pUserData)
    {
        to->pUserData = (void*)pool->dupArray(from->pUserData, sizeof(uint8_t));
    }
}

#endif
#ifdef VK_NV_glsl_shader
#endif
#ifdef VK_EXT_depth_range_unrestricted
#endif
#ifdef VK_IMG_filter_cubic
#endif
#ifdef VK_AMD_rasterization_order
void deepcopy_VkPipelineRasterizationStateRasterizationOrderAMD(
    Pool* pool,
    const VkPipelineRasterizationStateRasterizationOrderAMD* from,
    VkPipelineRasterizationStateRasterizationOrderAMD* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_AMD_shader_trinary_minmax
#endif
#ifdef VK_AMD_shader_explicit_vertex_parameter
#endif
#ifdef VK_EXT_debug_marker
void deepcopy_VkDebugMarkerObjectNameInfoEXT(
    Pool* pool,
    const VkDebugMarkerObjectNameInfoEXT* from,
    VkDebugMarkerObjectNameInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pObjectName = nullptr;
    if (from->pObjectName)
    {
        to->pObjectName = pool->strDup(from->pObjectName);
    }
}

void deepcopy_VkDebugMarkerObjectTagInfoEXT(
    Pool* pool,
    const VkDebugMarkerObjectTagInfoEXT* from,
    VkDebugMarkerObjectTagInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pTag = nullptr;
    if (from->pTag)
    {
        to->pTag = (void*)pool->dupArray(from->pTag, from->tagSize * sizeof(const uint8_t));
    }
}

void deepcopy_VkDebugMarkerMarkerInfoEXT(
    Pool* pool,
    const VkDebugMarkerMarkerInfoEXT* from,
    VkDebugMarkerMarkerInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pMarkerName = nullptr;
    if (from->pMarkerName)
    {
        to->pMarkerName = pool->strDup(from->pMarkerName);
    }
    memcpy(to->color, from->color, 4 * sizeof(float));
}

#endif
#ifdef VK_AMD_gcn_shader
#endif
#ifdef VK_NV_dedicated_allocation
void deepcopy_VkDedicatedAllocationImageCreateInfoNV(
    Pool* pool,
    const VkDedicatedAllocationImageCreateInfoNV* from,
    VkDedicatedAllocationImageCreateInfoNV* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkDedicatedAllocationBufferCreateInfoNV(
    Pool* pool,
    const VkDedicatedAllocationBufferCreateInfoNV* from,
    VkDedicatedAllocationBufferCreateInfoNV* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkDedicatedAllocationMemoryAllocateInfoNV(
    Pool* pool,
    const VkDedicatedAllocationMemoryAllocateInfoNV* from,
    VkDedicatedAllocationMemoryAllocateInfoNV* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_AMD_draw_indirect_count
#endif
#ifdef VK_AMD_negative_viewport_height
#endif
#ifdef VK_AMD_gpu_shader_half_float
#endif
#ifdef VK_AMD_shader_ballot
#endif
#ifdef VK_AMD_texture_gather_bias_lod
void deepcopy_VkTextureLODGatherFormatPropertiesAMD(
    Pool* pool,
    const VkTextureLODGatherFormatPropertiesAMD* from,
    VkTextureLODGatherFormatPropertiesAMD* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_AMD_shader_info
void deepcopy_VkShaderResourceUsageAMD(
    Pool* pool,
    const VkShaderResourceUsageAMD* from,
    VkShaderResourceUsageAMD* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkShaderStatisticsInfoAMD(
    Pool* pool,
    const VkShaderStatisticsInfoAMD* from,
    VkShaderStatisticsInfoAMD* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkShaderResourceUsageAMD(pool, &from->resourceUsage, (VkShaderResourceUsageAMD*)(&to->resourceUsage));
    memcpy(to->computeWorkGroupSize, from->computeWorkGroupSize, 3 * sizeof(uint32_t));
}

#endif
#ifdef VK_AMD_shader_image_load_store_lod
#endif
#ifdef VK_IMG_format_pvrtc
#endif
#ifdef VK_NV_external_memory_capabilities
void deepcopy_VkExternalImageFormatPropertiesNV(
    Pool* pool,
    const VkExternalImageFormatPropertiesNV* from,
    VkExternalImageFormatPropertiesNV* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkImageFormatProperties(pool, &from->imageFormatProperties, (VkImageFormatProperties*)(&to->imageFormatProperties));
}

#endif
#ifdef VK_NV_external_memory
void deepcopy_VkExternalMemoryImageCreateInfoNV(
    Pool* pool,
    const VkExternalMemoryImageCreateInfoNV* from,
    VkExternalMemoryImageCreateInfoNV* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkExportMemoryAllocateInfoNV(
    Pool* pool,
    const VkExportMemoryAllocateInfoNV* from,
    VkExportMemoryAllocateInfoNV* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_NV_external_memory_win32
void deepcopy_VkImportMemoryWin32HandleInfoNV(
    Pool* pool,
    const VkImportMemoryWin32HandleInfoNV* from,
    VkImportMemoryWin32HandleInfoNV* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkExportMemoryWin32HandleInfoNV(
    Pool* pool,
    const VkExportMemoryWin32HandleInfoNV* from,
    VkExportMemoryWin32HandleInfoNV* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pAttributes = nullptr;
    if (from->pAttributes)
    {
        to->pAttributes = (SECURITY_ATTRIBUTES*)pool->dupArray(from->pAttributes, sizeof(const SECURITY_ATTRIBUTES));
    }
}

#endif
#ifdef VK_NV_win32_keyed_mutex
void deepcopy_VkWin32KeyedMutexAcquireReleaseInfoNV(
    Pool* pool,
    const VkWin32KeyedMutexAcquireReleaseInfoNV* from,
    VkWin32KeyedMutexAcquireReleaseInfoNV* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pAcquireSyncs = nullptr;
    if (from->pAcquireSyncs)
    {
        to->pAcquireSyncs = (VkDeviceMemory*)pool->dupArray(from->pAcquireSyncs, from->acquireCount * sizeof(const VkDeviceMemory));
    }
    to->pAcquireKeys = nullptr;
    if (from->pAcquireKeys)
    {
        to->pAcquireKeys = (uint64_t*)pool->dupArray(from->pAcquireKeys, from->acquireCount * sizeof(const uint64_t));
    }
    to->pAcquireTimeoutMilliseconds = nullptr;
    if (from->pAcquireTimeoutMilliseconds)
    {
        to->pAcquireTimeoutMilliseconds = (uint32_t*)pool->dupArray(from->pAcquireTimeoutMilliseconds, from->acquireCount * sizeof(const uint32_t));
    }
    to->pReleaseSyncs = nullptr;
    if (from->pReleaseSyncs)
    {
        to->pReleaseSyncs = (VkDeviceMemory*)pool->dupArray(from->pReleaseSyncs, from->releaseCount * sizeof(const VkDeviceMemory));
    }
    to->pReleaseKeys = nullptr;
    if (from->pReleaseKeys)
    {
        to->pReleaseKeys = (uint64_t*)pool->dupArray(from->pReleaseKeys, from->releaseCount * sizeof(const uint64_t));
    }
}

#endif
#ifdef VK_EXT_validation_flags
void deepcopy_VkValidationFlagsEXT(
    Pool* pool,
    const VkValidationFlagsEXT* from,
    VkValidationFlagsEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pDisabledValidationChecks = nullptr;
    if (from->pDisabledValidationChecks)
    {
        to->pDisabledValidationChecks = (VkValidationCheckEXT*)pool->dupArray(from->pDisabledValidationChecks, from->disabledValidationCheckCount * sizeof(const VkValidationCheckEXT));
    }
}

#endif
#ifdef VK_NN_vi_surface
void deepcopy_VkViSurfaceCreateInfoNN(
    Pool* pool,
    const VkViSurfaceCreateInfoNN* from,
    VkViSurfaceCreateInfoNN* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->window = nullptr;
    if (from->window)
    {
        to->window = (void*)pool->dupArray(from->window, sizeof(uint8_t));
    }
}

#endif
#ifdef VK_EXT_shader_subgroup_ballot
#endif
#ifdef VK_EXT_shader_subgroup_vote
#endif
#ifdef VK_EXT_conditional_rendering
void deepcopy_VkConditionalRenderingBeginInfoEXT(
    Pool* pool,
    const VkConditionalRenderingBeginInfoEXT* from,
    VkConditionalRenderingBeginInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPhysicalDeviceConditionalRenderingFeaturesEXT(
    Pool* pool,
    const VkPhysicalDeviceConditionalRenderingFeaturesEXT* from,
    VkPhysicalDeviceConditionalRenderingFeaturesEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkCommandBufferInheritanceConditionalRenderingInfoEXT(
    Pool* pool,
    const VkCommandBufferInheritanceConditionalRenderingInfoEXT* from,
    VkCommandBufferInheritanceConditionalRenderingInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_NVX_device_generated_commands
void deepcopy_VkDeviceGeneratedCommandsFeaturesNVX(
    Pool* pool,
    const VkDeviceGeneratedCommandsFeaturesNVX* from,
    VkDeviceGeneratedCommandsFeaturesNVX* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkDeviceGeneratedCommandsLimitsNVX(
    Pool* pool,
    const VkDeviceGeneratedCommandsLimitsNVX* from,
    VkDeviceGeneratedCommandsLimitsNVX* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkIndirectCommandsTokenNVX(
    Pool* pool,
    const VkIndirectCommandsTokenNVX* from,
    VkIndirectCommandsTokenNVX* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkIndirectCommandsLayoutTokenNVX(
    Pool* pool,
    const VkIndirectCommandsLayoutTokenNVX* from,
    VkIndirectCommandsLayoutTokenNVX* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkIndirectCommandsLayoutCreateInfoNVX(
    Pool* pool,
    const VkIndirectCommandsLayoutCreateInfoNVX* from,
    VkIndirectCommandsLayoutCreateInfoNVX* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pTokens = nullptr;
    if (from->pTokens)
    {
        to->pTokens = (VkIndirectCommandsLayoutTokenNVX*)pool->alloc(from->tokenCount * sizeof(const VkIndirectCommandsLayoutTokenNVX));
        to->tokenCount = from->tokenCount;
        for (uint32_t i = 0; i < (uint32_t)from->tokenCount; ++i)
        {
            deepcopy_VkIndirectCommandsLayoutTokenNVX(pool, from->pTokens + i, (VkIndirectCommandsLayoutTokenNVX*)(to->pTokens + i));
        }
    }
}

void deepcopy_VkCmdProcessCommandsInfoNVX(
    Pool* pool,
    const VkCmdProcessCommandsInfoNVX* from,
    VkCmdProcessCommandsInfoNVX* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pIndirectCommandsTokens = nullptr;
    if (from->pIndirectCommandsTokens)
    {
        to->pIndirectCommandsTokens = (VkIndirectCommandsTokenNVX*)pool->alloc(from->indirectCommandsTokenCount * sizeof(const VkIndirectCommandsTokenNVX));
        to->indirectCommandsTokenCount = from->indirectCommandsTokenCount;
        for (uint32_t i = 0; i < (uint32_t)from->indirectCommandsTokenCount; ++i)
        {
            deepcopy_VkIndirectCommandsTokenNVX(pool, from->pIndirectCommandsTokens + i, (VkIndirectCommandsTokenNVX*)(to->pIndirectCommandsTokens + i));
        }
    }
}

void deepcopy_VkCmdReserveSpaceForCommandsInfoNVX(
    Pool* pool,
    const VkCmdReserveSpaceForCommandsInfoNVX* from,
    VkCmdReserveSpaceForCommandsInfoNVX* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkObjectTableCreateInfoNVX(
    Pool* pool,
    const VkObjectTableCreateInfoNVX* from,
    VkObjectTableCreateInfoNVX* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pObjectEntryTypes = nullptr;
    if (from->pObjectEntryTypes)
    {
        to->pObjectEntryTypes = (VkObjectEntryTypeNVX*)pool->dupArray(from->pObjectEntryTypes, from->objectCount * sizeof(const VkObjectEntryTypeNVX));
    }
    to->pObjectEntryCounts = nullptr;
    if (from->pObjectEntryCounts)
    {
        to->pObjectEntryCounts = (uint32_t*)pool->dupArray(from->pObjectEntryCounts, from->objectCount * sizeof(const uint32_t));
    }
    to->pObjectEntryUsageFlags = nullptr;
    if (from->pObjectEntryUsageFlags)
    {
        to->pObjectEntryUsageFlags = (VkObjectEntryUsageFlagsNVX*)pool->dupArray(from->pObjectEntryUsageFlags, from->objectCount * sizeof(const VkObjectEntryUsageFlagsNVX));
    }
}

void deepcopy_VkObjectTableEntryNVX(
    Pool* pool,
    const VkObjectTableEntryNVX* from,
    VkObjectTableEntryNVX* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkObjectTablePipelineEntryNVX(
    Pool* pool,
    const VkObjectTablePipelineEntryNVX* from,
    VkObjectTablePipelineEntryNVX* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkObjectTableDescriptorSetEntryNVX(
    Pool* pool,
    const VkObjectTableDescriptorSetEntryNVX* from,
    VkObjectTableDescriptorSetEntryNVX* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkObjectTableVertexBufferEntryNVX(
    Pool* pool,
    const VkObjectTableVertexBufferEntryNVX* from,
    VkObjectTableVertexBufferEntryNVX* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkObjectTableIndexBufferEntryNVX(
    Pool* pool,
    const VkObjectTableIndexBufferEntryNVX* from,
    VkObjectTableIndexBufferEntryNVX* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkObjectTablePushConstantEntryNVX(
    Pool* pool,
    const VkObjectTablePushConstantEntryNVX* from,
    VkObjectTablePushConstantEntryNVX* to)
{
    (void)pool;
    *to = *from;
}

#endif
#ifdef VK_NV_clip_space_w_scaling
void deepcopy_VkViewportWScalingNV(
    Pool* pool,
    const VkViewportWScalingNV* from,
    VkViewportWScalingNV* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkPipelineViewportWScalingStateCreateInfoNV(
    Pool* pool,
    const VkPipelineViewportWScalingStateCreateInfoNV* from,
    VkPipelineViewportWScalingStateCreateInfoNV* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pViewportWScalings = nullptr;
    if (from->pViewportWScalings)
    {
        to->pViewportWScalings = (VkViewportWScalingNV*)pool->alloc(from->viewportCount * sizeof(const VkViewportWScalingNV));
        to->viewportCount = from->viewportCount;
        for (uint32_t i = 0; i < (uint32_t)from->viewportCount; ++i)
        {
            deepcopy_VkViewportWScalingNV(pool, from->pViewportWScalings + i, (VkViewportWScalingNV*)(to->pViewportWScalings + i));
        }
    }
}

#endif
#ifdef VK_EXT_direct_mode_display
#endif
#ifdef VK_EXT_acquire_xlib_display
#endif
#ifdef VK_EXT_display_surface_counter
void deepcopy_VkSurfaceCapabilities2EXT(
    Pool* pool,
    const VkSurfaceCapabilities2EXT* from,
    VkSurfaceCapabilities2EXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkExtent2D(pool, &from->currentExtent, (VkExtent2D*)(&to->currentExtent));
    deepcopy_VkExtent2D(pool, &from->minImageExtent, (VkExtent2D*)(&to->minImageExtent));
    deepcopy_VkExtent2D(pool, &from->maxImageExtent, (VkExtent2D*)(&to->maxImageExtent));
}

#endif
#ifdef VK_EXT_display_control
void deepcopy_VkDisplayPowerInfoEXT(
    Pool* pool,
    const VkDisplayPowerInfoEXT* from,
    VkDisplayPowerInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkDeviceEventInfoEXT(
    Pool* pool,
    const VkDeviceEventInfoEXT* from,
    VkDeviceEventInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkDisplayEventInfoEXT(
    Pool* pool,
    const VkDisplayEventInfoEXT* from,
    VkDisplayEventInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkSwapchainCounterCreateInfoEXT(
    Pool* pool,
    const VkSwapchainCounterCreateInfoEXT* from,
    VkSwapchainCounterCreateInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_GOOGLE_display_timing
void deepcopy_VkRefreshCycleDurationGOOGLE(
    Pool* pool,
    const VkRefreshCycleDurationGOOGLE* from,
    VkRefreshCycleDurationGOOGLE* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkPastPresentationTimingGOOGLE(
    Pool* pool,
    const VkPastPresentationTimingGOOGLE* from,
    VkPastPresentationTimingGOOGLE* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkPresentTimeGOOGLE(
    Pool* pool,
    const VkPresentTimeGOOGLE* from,
    VkPresentTimeGOOGLE* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkPresentTimesInfoGOOGLE(
    Pool* pool,
    const VkPresentTimesInfoGOOGLE* from,
    VkPresentTimesInfoGOOGLE* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pTimes = nullptr;
    if (from->pTimes)
    {
        to->pTimes = (VkPresentTimeGOOGLE*)pool->alloc(from->swapchainCount * sizeof(const VkPresentTimeGOOGLE));
        to->swapchainCount = from->swapchainCount;
        for (uint32_t i = 0; i < (uint32_t)from->swapchainCount; ++i)
        {
            deepcopy_VkPresentTimeGOOGLE(pool, from->pTimes + i, (VkPresentTimeGOOGLE*)(to->pTimes + i));
        }
    }
}

#endif
#ifdef VK_NV_sample_mask_override_coverage
#endif
#ifdef VK_NV_geometry_shader_passthrough
#endif
#ifdef VK_NV_viewport_array2
#endif
#ifdef VK_NVX_multiview_per_view_attributes
void deepcopy_VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX(
    Pool* pool,
    const VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX* from,
    VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_NV_viewport_swizzle
void deepcopy_VkViewportSwizzleNV(
    Pool* pool,
    const VkViewportSwizzleNV* from,
    VkViewportSwizzleNV* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkPipelineViewportSwizzleStateCreateInfoNV(
    Pool* pool,
    const VkPipelineViewportSwizzleStateCreateInfoNV* from,
    VkPipelineViewportSwizzleStateCreateInfoNV* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pViewportSwizzles = nullptr;
    if (from->pViewportSwizzles)
    {
        to->pViewportSwizzles = (VkViewportSwizzleNV*)pool->alloc(from->viewportCount * sizeof(const VkViewportSwizzleNV));
        to->viewportCount = from->viewportCount;
        for (uint32_t i = 0; i < (uint32_t)from->viewportCount; ++i)
        {
            deepcopy_VkViewportSwizzleNV(pool, from->pViewportSwizzles + i, (VkViewportSwizzleNV*)(to->pViewportSwizzles + i));
        }
    }
}

#endif
#ifdef VK_EXT_discard_rectangles
void deepcopy_VkPhysicalDeviceDiscardRectanglePropertiesEXT(
    Pool* pool,
    const VkPhysicalDeviceDiscardRectanglePropertiesEXT* from,
    VkPhysicalDeviceDiscardRectanglePropertiesEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPipelineDiscardRectangleStateCreateInfoEXT(
    Pool* pool,
    const VkPipelineDiscardRectangleStateCreateInfoEXT* from,
    VkPipelineDiscardRectangleStateCreateInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pDiscardRectangles = nullptr;
    if (from->pDiscardRectangles)
    {
        to->pDiscardRectangles = (VkRect2D*)pool->alloc(from->discardRectangleCount * sizeof(const VkRect2D));
        to->discardRectangleCount = from->discardRectangleCount;
        for (uint32_t i = 0; i < (uint32_t)from->discardRectangleCount; ++i)
        {
            deepcopy_VkRect2D(pool, from->pDiscardRectangles + i, (VkRect2D*)(to->pDiscardRectangles + i));
        }
    }
}

#endif
#ifdef VK_EXT_conservative_rasterization
void deepcopy_VkPhysicalDeviceConservativeRasterizationPropertiesEXT(
    Pool* pool,
    const VkPhysicalDeviceConservativeRasterizationPropertiesEXT* from,
    VkPhysicalDeviceConservativeRasterizationPropertiesEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPipelineRasterizationConservativeStateCreateInfoEXT(
    Pool* pool,
    const VkPipelineRasterizationConservativeStateCreateInfoEXT* from,
    VkPipelineRasterizationConservativeStateCreateInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_EXT_swapchain_colorspace
#endif
#ifdef VK_EXT_hdr_metadata
void deepcopy_VkXYColorEXT(
    Pool* pool,
    const VkXYColorEXT* from,
    VkXYColorEXT* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkHdrMetadataEXT(
    Pool* pool,
    const VkHdrMetadataEXT* from,
    VkHdrMetadataEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkXYColorEXT(pool, &from->displayPrimaryRed, (VkXYColorEXT*)(&to->displayPrimaryRed));
    deepcopy_VkXYColorEXT(pool, &from->displayPrimaryGreen, (VkXYColorEXT*)(&to->displayPrimaryGreen));
    deepcopy_VkXYColorEXT(pool, &from->displayPrimaryBlue, (VkXYColorEXT*)(&to->displayPrimaryBlue));
    deepcopy_VkXYColorEXT(pool, &from->whitePoint, (VkXYColorEXT*)(&to->whitePoint));
}

#endif
#ifdef VK_MVK_ios_surface
void deepcopy_VkIOSSurfaceCreateInfoMVK(
    Pool* pool,
    const VkIOSSurfaceCreateInfoMVK* from,
    VkIOSSurfaceCreateInfoMVK* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pView = nullptr;
    if (from->pView)
    {
        to->pView = (void*)pool->dupArray(from->pView, sizeof(const uint8_t));
    }
}

#endif
#ifdef VK_MVK_macos_surface
void deepcopy_VkMacOSSurfaceCreateInfoMVK(
    Pool* pool,
    const VkMacOSSurfaceCreateInfoMVK* from,
    VkMacOSSurfaceCreateInfoMVK* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pView = nullptr;
    if (from->pView)
    {
        to->pView = (void*)pool->dupArray(from->pView, sizeof(const uint8_t));
    }
}

#endif
#ifdef VK_EXT_external_memory_dma_buf
#endif
#ifdef VK_EXT_queue_family_foreign
#endif
#ifdef VK_EXT_debug_utils
void deepcopy_VkDebugUtilsObjectNameInfoEXT(
    Pool* pool,
    const VkDebugUtilsObjectNameInfoEXT* from,
    VkDebugUtilsObjectNameInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pObjectName = nullptr;
    if (from->pObjectName)
    {
        to->pObjectName = pool->strDup(from->pObjectName);
    }
}

void deepcopy_VkDebugUtilsObjectTagInfoEXT(
    Pool* pool,
    const VkDebugUtilsObjectTagInfoEXT* from,
    VkDebugUtilsObjectTagInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pTag = nullptr;
    if (from->pTag)
    {
        to->pTag = (void*)pool->dupArray(from->pTag, from->tagSize * sizeof(const uint8_t));
    }
}

void deepcopy_VkDebugUtilsLabelEXT(
    Pool* pool,
    const VkDebugUtilsLabelEXT* from,
    VkDebugUtilsLabelEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pLabelName = nullptr;
    if (from->pLabelName)
    {
        to->pLabelName = pool->strDup(from->pLabelName);
    }
    memcpy(to->color, from->color, 4 * sizeof(float));
}

void deepcopy_VkDebugUtilsMessengerCallbackDataEXT(
    Pool* pool,
    const VkDebugUtilsMessengerCallbackDataEXT* from,
    VkDebugUtilsMessengerCallbackDataEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pMessageIdName = nullptr;
    if (from->pMessageIdName)
    {
        to->pMessageIdName = pool->strDup(from->pMessageIdName);
    }
    to->pMessage = nullptr;
    if (from->pMessage)
    {
        to->pMessage = pool->strDup(from->pMessage);
    }
    to->pQueueLabels = nullptr;
    if (from->pQueueLabels)
    {
        to->pQueueLabels = (VkDebugUtilsLabelEXT*)pool->alloc(from->queueLabelCount * sizeof(VkDebugUtilsLabelEXT));
        to->queueLabelCount = from->queueLabelCount;
        for (uint32_t i = 0; i < (uint32_t)from->queueLabelCount; ++i)
        {
            deepcopy_VkDebugUtilsLabelEXT(pool, from->pQueueLabels + i, (VkDebugUtilsLabelEXT*)(to->pQueueLabels + i));
        }
    }
    to->pCmdBufLabels = nullptr;
    if (from->pCmdBufLabels)
    {
        to->pCmdBufLabels = (VkDebugUtilsLabelEXT*)pool->alloc(from->cmdBufLabelCount * sizeof(VkDebugUtilsLabelEXT));
        to->cmdBufLabelCount = from->cmdBufLabelCount;
        for (uint32_t i = 0; i < (uint32_t)from->cmdBufLabelCount; ++i)
        {
            deepcopy_VkDebugUtilsLabelEXT(pool, from->pCmdBufLabels + i, (VkDebugUtilsLabelEXT*)(to->pCmdBufLabels + i));
        }
    }
    to->pObjects = nullptr;
    if (from->pObjects)
    {
        to->pObjects = (VkDebugUtilsObjectNameInfoEXT*)pool->alloc(from->objectCount * sizeof(VkDebugUtilsObjectNameInfoEXT));
        to->objectCount = from->objectCount;
        for (uint32_t i = 0; i < (uint32_t)from->objectCount; ++i)
        {
            deepcopy_VkDebugUtilsObjectNameInfoEXT(pool, from->pObjects + i, (VkDebugUtilsObjectNameInfoEXT*)(to->pObjects + i));
        }
    }
}

void deepcopy_VkDebugUtilsMessengerCreateInfoEXT(
    Pool* pool,
    const VkDebugUtilsMessengerCreateInfoEXT* from,
    VkDebugUtilsMessengerCreateInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pUserData = nullptr;
    if (from->pUserData)
    {
        to->pUserData = (void*)pool->dupArray(from->pUserData, sizeof(uint8_t));
    }
}

#endif
#ifdef VK_ANDROID_external_memory_android_hardware_buffer
void deepcopy_VkAndroidHardwareBufferUsageANDROID(
    Pool* pool,
    const VkAndroidHardwareBufferUsageANDROID* from,
    VkAndroidHardwareBufferUsageANDROID* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkAndroidHardwareBufferPropertiesANDROID(
    Pool* pool,
    const VkAndroidHardwareBufferPropertiesANDROID* from,
    VkAndroidHardwareBufferPropertiesANDROID* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkAndroidHardwareBufferFormatPropertiesANDROID(
    Pool* pool,
    const VkAndroidHardwareBufferFormatPropertiesANDROID* from,
    VkAndroidHardwareBufferFormatPropertiesANDROID* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkComponentMapping(pool, &from->samplerYcbcrConversionComponents, (VkComponentMapping*)(&to->samplerYcbcrConversionComponents));
}

void deepcopy_VkImportAndroidHardwareBufferInfoANDROID(
    Pool* pool,
    const VkImportAndroidHardwareBufferInfoANDROID* from,
    VkImportAndroidHardwareBufferInfoANDROID* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->buffer = nullptr;
    if (from->buffer)
    {
        to->buffer = (AHardwareBuffer*)pool->dupArray(from->buffer, sizeof(AHardwareBuffer));
    }
}

void deepcopy_VkMemoryGetAndroidHardwareBufferInfoANDROID(
    Pool* pool,
    const VkMemoryGetAndroidHardwareBufferInfoANDROID* from,
    VkMemoryGetAndroidHardwareBufferInfoANDROID* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkExternalFormatANDROID(
    Pool* pool,
    const VkExternalFormatANDROID* from,
    VkExternalFormatANDROID* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_EXT_sampler_filter_minmax
void deepcopy_VkSamplerReductionModeCreateInfoEXT(
    Pool* pool,
    const VkSamplerReductionModeCreateInfoEXT* from,
    VkSamplerReductionModeCreateInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT(
    Pool* pool,
    const VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT* from,
    VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_AMD_gpu_shader_int16
#endif
#ifdef VK_AMD_mixed_attachment_samples
#endif
#ifdef VK_AMD_shader_fragment_mask
#endif
#ifdef VK_EXT_shader_stencil_export
#endif
#ifdef VK_EXT_sample_locations
void deepcopy_VkSampleLocationEXT(
    Pool* pool,
    const VkSampleLocationEXT* from,
    VkSampleLocationEXT* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkSampleLocationsInfoEXT(
    Pool* pool,
    const VkSampleLocationsInfoEXT* from,
    VkSampleLocationsInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkExtent2D(pool, &from->sampleLocationGridSize, (VkExtent2D*)(&to->sampleLocationGridSize));
    to->pSampleLocations = nullptr;
    if (from->pSampleLocations)
    {
        to->pSampleLocations = (VkSampleLocationEXT*)pool->alloc(from->sampleLocationsCount * sizeof(const VkSampleLocationEXT));
        to->sampleLocationsCount = from->sampleLocationsCount;
        for (uint32_t i = 0; i < (uint32_t)from->sampleLocationsCount; ++i)
        {
            deepcopy_VkSampleLocationEXT(pool, from->pSampleLocations + i, (VkSampleLocationEXT*)(to->pSampleLocations + i));
        }
    }
}

void deepcopy_VkAttachmentSampleLocationsEXT(
    Pool* pool,
    const VkAttachmentSampleLocationsEXT* from,
    VkAttachmentSampleLocationsEXT* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkSampleLocationsInfoEXT(pool, &from->sampleLocationsInfo, (VkSampleLocationsInfoEXT*)(&to->sampleLocationsInfo));
}

void deepcopy_VkSubpassSampleLocationsEXT(
    Pool* pool,
    const VkSubpassSampleLocationsEXT* from,
    VkSubpassSampleLocationsEXT* to)
{
    (void)pool;
    *to = *from;
    deepcopy_VkSampleLocationsInfoEXT(pool, &from->sampleLocationsInfo, (VkSampleLocationsInfoEXT*)(&to->sampleLocationsInfo));
}

void deepcopy_VkRenderPassSampleLocationsBeginInfoEXT(
    Pool* pool,
    const VkRenderPassSampleLocationsBeginInfoEXT* from,
    VkRenderPassSampleLocationsBeginInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pAttachmentInitialSampleLocations = nullptr;
    if (from->pAttachmentInitialSampleLocations)
    {
        to->pAttachmentInitialSampleLocations = (VkAttachmentSampleLocationsEXT*)pool->alloc(from->attachmentInitialSampleLocationsCount * sizeof(const VkAttachmentSampleLocationsEXT));
        to->attachmentInitialSampleLocationsCount = from->attachmentInitialSampleLocationsCount;
        for (uint32_t i = 0; i < (uint32_t)from->attachmentInitialSampleLocationsCount; ++i)
        {
            deepcopy_VkAttachmentSampleLocationsEXT(pool, from->pAttachmentInitialSampleLocations + i, (VkAttachmentSampleLocationsEXT*)(to->pAttachmentInitialSampleLocations + i));
        }
    }
    to->pPostSubpassSampleLocations = nullptr;
    if (from->pPostSubpassSampleLocations)
    {
        to->pPostSubpassSampleLocations = (VkSubpassSampleLocationsEXT*)pool->alloc(from->postSubpassSampleLocationsCount * sizeof(const VkSubpassSampleLocationsEXT));
        to->postSubpassSampleLocationsCount = from->postSubpassSampleLocationsCount;
        for (uint32_t i = 0; i < (uint32_t)from->postSubpassSampleLocationsCount; ++i)
        {
            deepcopy_VkSubpassSampleLocationsEXT(pool, from->pPostSubpassSampleLocations + i, (VkSubpassSampleLocationsEXT*)(to->pPostSubpassSampleLocations + i));
        }
    }
}

void deepcopy_VkPipelineSampleLocationsStateCreateInfoEXT(
    Pool* pool,
    const VkPipelineSampleLocationsStateCreateInfoEXT* from,
    VkPipelineSampleLocationsStateCreateInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkSampleLocationsInfoEXT(pool, &from->sampleLocationsInfo, (VkSampleLocationsInfoEXT*)(&to->sampleLocationsInfo));
}

void deepcopy_VkPhysicalDeviceSampleLocationsPropertiesEXT(
    Pool* pool,
    const VkPhysicalDeviceSampleLocationsPropertiesEXT* from,
    VkPhysicalDeviceSampleLocationsPropertiesEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkExtent2D(pool, &from->maxSampleLocationGridSize, (VkExtent2D*)(&to->maxSampleLocationGridSize));
    memcpy(to->sampleLocationCoordinateRange, from->sampleLocationCoordinateRange, 2 * sizeof(float));
}

void deepcopy_VkMultisamplePropertiesEXT(
    Pool* pool,
    const VkMultisamplePropertiesEXT* from,
    VkMultisamplePropertiesEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    deepcopy_VkExtent2D(pool, &from->maxSampleLocationGridSize, (VkExtent2D*)(&to->maxSampleLocationGridSize));
}

#endif
#ifdef VK_EXT_blend_operation_advanced
void deepcopy_VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT(
    Pool* pool,
    const VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT* from,
    VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT(
    Pool* pool,
    const VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT* from,
    VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPipelineColorBlendAdvancedStateCreateInfoEXT(
    Pool* pool,
    const VkPipelineColorBlendAdvancedStateCreateInfoEXT* from,
    VkPipelineColorBlendAdvancedStateCreateInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_NV_fragment_coverage_to_color
void deepcopy_VkPipelineCoverageToColorStateCreateInfoNV(
    Pool* pool,
    const VkPipelineCoverageToColorStateCreateInfoNV* from,
    VkPipelineCoverageToColorStateCreateInfoNV* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_NV_framebuffer_mixed_samples
void deepcopy_VkPipelineCoverageModulationStateCreateInfoNV(
    Pool* pool,
    const VkPipelineCoverageModulationStateCreateInfoNV* from,
    VkPipelineCoverageModulationStateCreateInfoNV* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pCoverageModulationTable = nullptr;
    if (from->pCoverageModulationTable)
    {
        to->pCoverageModulationTable = (float*)pool->dupArray(from->pCoverageModulationTable, from->coverageModulationTableCount * sizeof(const float));
    }
}

#endif
#ifdef VK_NV_fill_rectangle
#endif
#ifdef VK_EXT_post_depth_coverage
#endif
#ifdef VK_EXT_validation_cache
void deepcopy_VkValidationCacheCreateInfoEXT(
    Pool* pool,
    const VkValidationCacheCreateInfoEXT* from,
    VkValidationCacheCreateInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pInitialData = nullptr;
    if (from->pInitialData)
    {
        to->pInitialData = (void*)pool->dupArray(from->pInitialData, from->initialDataSize * sizeof(const uint8_t));
    }
}

void deepcopy_VkShaderModuleValidationCacheCreateInfoEXT(
    Pool* pool,
    const VkShaderModuleValidationCacheCreateInfoEXT* from,
    VkShaderModuleValidationCacheCreateInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_EXT_descriptor_indexing
void deepcopy_VkDescriptorSetLayoutBindingFlagsCreateInfoEXT(
    Pool* pool,
    const VkDescriptorSetLayoutBindingFlagsCreateInfoEXT* from,
    VkDescriptorSetLayoutBindingFlagsCreateInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pBindingFlags = nullptr;
    if (from->pBindingFlags)
    {
        to->pBindingFlags = (VkDescriptorBindingFlagsEXT*)pool->dupArray(from->pBindingFlags, from->bindingCount * sizeof(const VkDescriptorBindingFlagsEXT));
    }
}

void deepcopy_VkPhysicalDeviceDescriptorIndexingFeaturesEXT(
    Pool* pool,
    const VkPhysicalDeviceDescriptorIndexingFeaturesEXT* from,
    VkPhysicalDeviceDescriptorIndexingFeaturesEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPhysicalDeviceDescriptorIndexingPropertiesEXT(
    Pool* pool,
    const VkPhysicalDeviceDescriptorIndexingPropertiesEXT* from,
    VkPhysicalDeviceDescriptorIndexingPropertiesEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkDescriptorSetVariableDescriptorCountAllocateInfoEXT(
    Pool* pool,
    const VkDescriptorSetVariableDescriptorCountAllocateInfoEXT* from,
    VkDescriptorSetVariableDescriptorCountAllocateInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pDescriptorCounts = nullptr;
    if (from->pDescriptorCounts)
    {
        to->pDescriptorCounts = (uint32_t*)pool->dupArray(from->pDescriptorCounts, from->descriptorSetCount * sizeof(const uint32_t));
    }
}

void deepcopy_VkDescriptorSetVariableDescriptorCountLayoutSupportEXT(
    Pool* pool,
    const VkDescriptorSetVariableDescriptorCountLayoutSupportEXT* from,
    VkDescriptorSetVariableDescriptorCountLayoutSupportEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_EXT_shader_viewport_index_layer
#endif
#ifdef VK_EXT_global_priority
void deepcopy_VkDeviceQueueGlobalPriorityCreateInfoEXT(
    Pool* pool,
    const VkDeviceQueueGlobalPriorityCreateInfoEXT* from,
    VkDeviceQueueGlobalPriorityCreateInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_EXT_external_memory_host
void deepcopy_VkImportMemoryHostPointerInfoEXT(
    Pool* pool,
    const VkImportMemoryHostPointerInfoEXT* from,
    VkImportMemoryHostPointerInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pHostPointer = nullptr;
    if (from->pHostPointer)
    {
        to->pHostPointer = (void*)pool->dupArray(from->pHostPointer, sizeof(uint8_t));
    }
}

void deepcopy_VkMemoryHostPointerPropertiesEXT(
    Pool* pool,
    const VkMemoryHostPointerPropertiesEXT* from,
    VkMemoryHostPointerPropertiesEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkPhysicalDeviceExternalMemoryHostPropertiesEXT(
    Pool* pool,
    const VkPhysicalDeviceExternalMemoryHostPropertiesEXT* from,
    VkPhysicalDeviceExternalMemoryHostPropertiesEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_AMD_buffer_marker
#endif
#ifdef VK_AMD_shader_core_properties
void deepcopy_VkPhysicalDeviceShaderCorePropertiesAMD(
    Pool* pool,
    const VkPhysicalDeviceShaderCorePropertiesAMD* from,
    VkPhysicalDeviceShaderCorePropertiesAMD* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_EXT_vertex_attribute_divisor
void deepcopy_VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT(
    Pool* pool,
    const VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT* from,
    VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkVertexInputBindingDivisorDescriptionEXT(
    Pool* pool,
    const VkVertexInputBindingDivisorDescriptionEXT* from,
    VkVertexInputBindingDivisorDescriptionEXT* to)
{
    (void)pool;
    *to = *from;
}

void deepcopy_VkPipelineVertexInputDivisorStateCreateInfoEXT(
    Pool* pool,
    const VkPipelineVertexInputDivisorStateCreateInfoEXT* from,
    VkPipelineVertexInputDivisorStateCreateInfoEXT* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (const void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pVertexBindingDivisors = nullptr;
    if (from->pVertexBindingDivisors)
    {
        to->pVertexBindingDivisors = (VkVertexInputBindingDivisorDescriptionEXT*)pool->alloc(from->vertexBindingDivisorCount * sizeof(const VkVertexInputBindingDivisorDescriptionEXT));
        to->vertexBindingDivisorCount = from->vertexBindingDivisorCount;
        for (uint32_t i = 0; i < (uint32_t)from->vertexBindingDivisorCount; ++i)
        {
            deepcopy_VkVertexInputBindingDivisorDescriptionEXT(pool, from->pVertexBindingDivisors + i, (VkVertexInputBindingDivisorDescriptionEXT*)(to->pVertexBindingDivisors + i));
        }
    }
}

#endif
#ifdef VK_NV_shader_subgroup_partitioned
#endif
#ifdef VK_NV_device_diagnostic_checkpoints
void deepcopy_VkQueueFamilyCheckpointPropertiesNV(
    Pool* pool,
    const VkQueueFamilyCheckpointPropertiesNV* from,
    VkQueueFamilyCheckpointPropertiesNV* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkCheckpointDataNV(
    Pool* pool,
    const VkCheckpointDataNV* from,
    VkCheckpointDataNV* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
    to->pCheckpointMarker = nullptr;
    if (from->pCheckpointMarker)
    {
        to->pCheckpointMarker = (void*)pool->dupArray(from->pCheckpointMarker, sizeof(uint8_t));
    }
}

#endif
#ifdef VK_GOOGLE_address_space
#endif
#ifdef VK_GOOGLE_color_buffer
void deepcopy_VkImportColorBufferGOOGLE(
    Pool* pool,
    const VkImportColorBufferGOOGLE* from,
    VkImportColorBufferGOOGLE* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

void deepcopy_VkImportPhysicalAddressGOOGLE(
    Pool* pool,
    const VkImportPhysicalAddressGOOGLE* from,
    VkImportPhysicalAddressGOOGLE* to)
{
    (void)pool;
    *to = *from;
    size_t pNext_size = goldfish_vk_extension_struct_size(from->pNext);
    to->pNext = nullptr;
    if (pNext_size)
    {
        to->pNext = (void*)pool->alloc(pNext_size);
        deepcopy_extension_struct(pool, from->pNext, (void*)(to->pNext));
    }
}

#endif
#ifdef VK_GOOGLE_sized_descriptor_update_template
#endif
#ifdef VK_GOOGLE_async_command_buffers
#endif
#ifdef VK_GOOGLE_create_resources_with_requirements
#endif
#ifdef VK_GOOGLE_address_space_info
#endif
#ifdef VK_GOOGLE_free_memory_sync
#endif
void deepcopy_extension_struct(
    Pool* pool,
    const void* structExtension,
    void* structExtension_out)
{
    if (!structExtension)
    {
        return;
    }
    uint32_t structType = (uint32_t)goldfish_vk_struct_type(structExtension);
    switch(structType)
    {
#ifdef VK_VERSION_1_1
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SUBGROUP_PROPERTIES:
        {
            deepcopy_VkPhysicalDeviceSubgroupProperties(pool, reinterpret_cast<const VkPhysicalDeviceSubgroupProperties*>(structExtension), reinterpret_cast<VkPhysicalDeviceSubgroupProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_16BIT_STORAGE_FEATURES:
        {
            deepcopy_VkPhysicalDevice16BitStorageFeatures(pool, reinterpret_cast<const VkPhysicalDevice16BitStorageFeatures*>(structExtension), reinterpret_cast<VkPhysicalDevice16BitStorageFeatures*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_MEMORY_DEDICATED_REQUIREMENTS:
        {
            deepcopy_VkMemoryDedicatedRequirements(pool, reinterpret_cast<const VkMemoryDedicatedRequirements*>(structExtension), reinterpret_cast<VkMemoryDedicatedRequirements*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_MEMORY_DEDICATED_ALLOCATE_INFO:
        {
            deepcopy_VkMemoryDedicatedAllocateInfo(pool, reinterpret_cast<const VkMemoryDedicatedAllocateInfo*>(structExtension), reinterpret_cast<VkMemoryDedicatedAllocateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_MEMORY_ALLOCATE_FLAGS_INFO:
        {
            deepcopy_VkMemoryAllocateFlagsInfo(pool, reinterpret_cast<const VkMemoryAllocateFlagsInfo*>(structExtension), reinterpret_cast<VkMemoryAllocateFlagsInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_RENDER_PASS_BEGIN_INFO:
        {
            deepcopy_VkDeviceGroupRenderPassBeginInfo(pool, reinterpret_cast<const VkDeviceGroupRenderPassBeginInfo*>(structExtension), reinterpret_cast<VkDeviceGroupRenderPassBeginInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_COMMAND_BUFFER_BEGIN_INFO:
        {
            deepcopy_VkDeviceGroupCommandBufferBeginInfo(pool, reinterpret_cast<const VkDeviceGroupCommandBufferBeginInfo*>(structExtension), reinterpret_cast<VkDeviceGroupCommandBufferBeginInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_SUBMIT_INFO:
        {
            deepcopy_VkDeviceGroupSubmitInfo(pool, reinterpret_cast<const VkDeviceGroupSubmitInfo*>(structExtension), reinterpret_cast<VkDeviceGroupSubmitInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_BIND_SPARSE_INFO:
        {
            deepcopy_VkDeviceGroupBindSparseInfo(pool, reinterpret_cast<const VkDeviceGroupBindSparseInfo*>(structExtension), reinterpret_cast<VkDeviceGroupBindSparseInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_BIND_BUFFER_MEMORY_DEVICE_GROUP_INFO:
        {
            deepcopy_VkBindBufferMemoryDeviceGroupInfo(pool, reinterpret_cast<const VkBindBufferMemoryDeviceGroupInfo*>(structExtension), reinterpret_cast<VkBindBufferMemoryDeviceGroupInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_BIND_IMAGE_MEMORY_DEVICE_GROUP_INFO:
        {
            deepcopy_VkBindImageMemoryDeviceGroupInfo(pool, reinterpret_cast<const VkBindImageMemoryDeviceGroupInfo*>(structExtension), reinterpret_cast<VkBindImageMemoryDeviceGroupInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_DEVICE_CREATE_INFO:
        {
            deepcopy_VkDeviceGroupDeviceCreateInfo(pool, reinterpret_cast<const VkDeviceGroupDeviceCreateInfo*>(structExtension), reinterpret_cast<VkDeviceGroupDeviceCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_FEATURES_2:
        {
            deepcopy_VkPhysicalDeviceFeatures2(pool, reinterpret_cast<const VkPhysicalDeviceFeatures2*>(structExtension), reinterpret_cast<VkPhysicalDeviceFeatures2*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_POINT_CLIPPING_PROPERTIES:
        {
            deepcopy_VkPhysicalDevicePointClippingProperties(pool, reinterpret_cast<const VkPhysicalDevicePointClippingProperties*>(structExtension), reinterpret_cast<VkPhysicalDevicePointClippingProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_RENDER_PASS_INPUT_ATTACHMENT_ASPECT_CREATE_INFO:
        {
            deepcopy_VkRenderPassInputAttachmentAspectCreateInfo(pool, reinterpret_cast<const VkRenderPassInputAttachmentAspectCreateInfo*>(structExtension), reinterpret_cast<VkRenderPassInputAttachmentAspectCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_IMAGE_VIEW_USAGE_CREATE_INFO:
        {
            deepcopy_VkImageViewUsageCreateInfo(pool, reinterpret_cast<const VkImageViewUsageCreateInfo*>(structExtension), reinterpret_cast<VkImageViewUsageCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_TESSELLATION_DOMAIN_ORIGIN_STATE_CREATE_INFO:
        {
            deepcopy_VkPipelineTessellationDomainOriginStateCreateInfo(pool, reinterpret_cast<const VkPipelineTessellationDomainOriginStateCreateInfo*>(structExtension), reinterpret_cast<VkPipelineTessellationDomainOriginStateCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_RENDER_PASS_MULTIVIEW_CREATE_INFO:
        {
            deepcopy_VkRenderPassMultiviewCreateInfo(pool, reinterpret_cast<const VkRenderPassMultiviewCreateInfo*>(structExtension), reinterpret_cast<VkRenderPassMultiviewCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MULTIVIEW_FEATURES:
        {
            deepcopy_VkPhysicalDeviceMultiviewFeatures(pool, reinterpret_cast<const VkPhysicalDeviceMultiviewFeatures*>(structExtension), reinterpret_cast<VkPhysicalDeviceMultiviewFeatures*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MULTIVIEW_PROPERTIES:
        {
            deepcopy_VkPhysicalDeviceMultiviewProperties(pool, reinterpret_cast<const VkPhysicalDeviceMultiviewProperties*>(structExtension), reinterpret_cast<VkPhysicalDeviceMultiviewProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_VARIABLE_POINTER_FEATURES:
        {
            deepcopy_VkPhysicalDeviceVariablePointerFeatures(pool, reinterpret_cast<const VkPhysicalDeviceVariablePointerFeatures*>(structExtension), reinterpret_cast<VkPhysicalDeviceVariablePointerFeatures*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PROTECTED_MEMORY_FEATURES:
        {
            deepcopy_VkPhysicalDeviceProtectedMemoryFeatures(pool, reinterpret_cast<const VkPhysicalDeviceProtectedMemoryFeatures*>(structExtension), reinterpret_cast<VkPhysicalDeviceProtectedMemoryFeatures*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PROTECTED_MEMORY_PROPERTIES:
        {
            deepcopy_VkPhysicalDeviceProtectedMemoryProperties(pool, reinterpret_cast<const VkPhysicalDeviceProtectedMemoryProperties*>(structExtension), reinterpret_cast<VkPhysicalDeviceProtectedMemoryProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PROTECTED_SUBMIT_INFO:
        {
            deepcopy_VkProtectedSubmitInfo(pool, reinterpret_cast<const VkProtectedSubmitInfo*>(structExtension), reinterpret_cast<VkProtectedSubmitInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_SAMPLER_YCBCR_CONVERSION_INFO:
        {
            deepcopy_VkSamplerYcbcrConversionInfo(pool, reinterpret_cast<const VkSamplerYcbcrConversionInfo*>(structExtension), reinterpret_cast<VkSamplerYcbcrConversionInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_BIND_IMAGE_PLANE_MEMORY_INFO:
        {
            deepcopy_VkBindImagePlaneMemoryInfo(pool, reinterpret_cast<const VkBindImagePlaneMemoryInfo*>(structExtension), reinterpret_cast<VkBindImagePlaneMemoryInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_IMAGE_PLANE_MEMORY_REQUIREMENTS_INFO:
        {
            deepcopy_VkImagePlaneMemoryRequirementsInfo(pool, reinterpret_cast<const VkImagePlaneMemoryRequirementsInfo*>(structExtension), reinterpret_cast<VkImagePlaneMemoryRequirementsInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SAMPLER_YCBCR_CONVERSION_FEATURES:
        {
            deepcopy_VkPhysicalDeviceSamplerYcbcrConversionFeatures(pool, reinterpret_cast<const VkPhysicalDeviceSamplerYcbcrConversionFeatures*>(structExtension), reinterpret_cast<VkPhysicalDeviceSamplerYcbcrConversionFeatures*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_SAMPLER_YCBCR_CONVERSION_IMAGE_FORMAT_PROPERTIES:
        {
            deepcopy_VkSamplerYcbcrConversionImageFormatProperties(pool, reinterpret_cast<const VkSamplerYcbcrConversionImageFormatProperties*>(structExtension), reinterpret_cast<VkSamplerYcbcrConversionImageFormatProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_EXTERNAL_IMAGE_FORMAT_INFO:
        {
            deepcopy_VkPhysicalDeviceExternalImageFormatInfo(pool, reinterpret_cast<const VkPhysicalDeviceExternalImageFormatInfo*>(structExtension), reinterpret_cast<VkPhysicalDeviceExternalImageFormatInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXTERNAL_IMAGE_FORMAT_PROPERTIES:
        {
            deepcopy_VkExternalImageFormatProperties(pool, reinterpret_cast<const VkExternalImageFormatProperties*>(structExtension), reinterpret_cast<VkExternalImageFormatProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_ID_PROPERTIES:
        {
            deepcopy_VkPhysicalDeviceIDProperties(pool, reinterpret_cast<const VkPhysicalDeviceIDProperties*>(structExtension), reinterpret_cast<VkPhysicalDeviceIDProperties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXTERNAL_MEMORY_IMAGE_CREATE_INFO:
        {
            deepcopy_VkExternalMemoryImageCreateInfo(pool, reinterpret_cast<const VkExternalMemoryImageCreateInfo*>(structExtension), reinterpret_cast<VkExternalMemoryImageCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXTERNAL_MEMORY_BUFFER_CREATE_INFO:
        {
            deepcopy_VkExternalMemoryBufferCreateInfo(pool, reinterpret_cast<const VkExternalMemoryBufferCreateInfo*>(structExtension), reinterpret_cast<VkExternalMemoryBufferCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_MEMORY_ALLOCATE_INFO:
        {
            deepcopy_VkExportMemoryAllocateInfo(pool, reinterpret_cast<const VkExportMemoryAllocateInfo*>(structExtension), reinterpret_cast<VkExportMemoryAllocateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_FENCE_CREATE_INFO:
        {
            deepcopy_VkExportFenceCreateInfo(pool, reinterpret_cast<const VkExportFenceCreateInfo*>(structExtension), reinterpret_cast<VkExportFenceCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_SEMAPHORE_CREATE_INFO:
        {
            deepcopy_VkExportSemaphoreCreateInfo(pool, reinterpret_cast<const VkExportSemaphoreCreateInfo*>(structExtension), reinterpret_cast<VkExportSemaphoreCreateInfo*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MAINTENANCE_3_PROPERTIES:
        {
            deepcopy_VkPhysicalDeviceMaintenance3Properties(pool, reinterpret_cast<const VkPhysicalDeviceMaintenance3Properties*>(structExtension), reinterpret_cast<VkPhysicalDeviceMaintenance3Properties*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SHADER_DRAW_PARAMETER_FEATURES:
        {
            deepcopy_VkPhysicalDeviceShaderDrawParameterFeatures(pool, reinterpret_cast<const VkPhysicalDeviceShaderDrawParameterFeatures*>(structExtension), reinterpret_cast<VkPhysicalDeviceShaderDrawParameterFeatures*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_swapchain
        case VK_STRUCTURE_TYPE_IMAGE_SWAPCHAIN_CREATE_INFO_KHR:
        {
            deepcopy_VkImageSwapchainCreateInfoKHR(pool, reinterpret_cast<const VkImageSwapchainCreateInfoKHR*>(structExtension), reinterpret_cast<VkImageSwapchainCreateInfoKHR*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_BIND_IMAGE_MEMORY_SWAPCHAIN_INFO_KHR:
        {
            deepcopy_VkBindImageMemorySwapchainInfoKHR(pool, reinterpret_cast<const VkBindImageMemorySwapchainInfoKHR*>(structExtension), reinterpret_cast<VkBindImageMemorySwapchainInfoKHR*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_PRESENT_INFO_KHR:
        {
            deepcopy_VkDeviceGroupPresentInfoKHR(pool, reinterpret_cast<const VkDeviceGroupPresentInfoKHR*>(structExtension), reinterpret_cast<VkDeviceGroupPresentInfoKHR*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEVICE_GROUP_SWAPCHAIN_CREATE_INFO_KHR:
        {
            deepcopy_VkDeviceGroupSwapchainCreateInfoKHR(pool, reinterpret_cast<const VkDeviceGroupSwapchainCreateInfoKHR*>(structExtension), reinterpret_cast<VkDeviceGroupSwapchainCreateInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_display_swapchain
        case VK_STRUCTURE_TYPE_DISPLAY_PRESENT_INFO_KHR:
        {
            deepcopy_VkDisplayPresentInfoKHR(pool, reinterpret_cast<const VkDisplayPresentInfoKHR*>(structExtension), reinterpret_cast<VkDisplayPresentInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_external_memory_win32
        case VK_STRUCTURE_TYPE_IMPORT_MEMORY_WIN32_HANDLE_INFO_KHR:
        {
            deepcopy_VkImportMemoryWin32HandleInfoKHR(pool, reinterpret_cast<const VkImportMemoryWin32HandleInfoKHR*>(structExtension), reinterpret_cast<VkImportMemoryWin32HandleInfoKHR*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_MEMORY_WIN32_HANDLE_INFO_KHR:
        {
            deepcopy_VkExportMemoryWin32HandleInfoKHR(pool, reinterpret_cast<const VkExportMemoryWin32HandleInfoKHR*>(structExtension), reinterpret_cast<VkExportMemoryWin32HandleInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_external_memory_fd
        case VK_STRUCTURE_TYPE_IMPORT_MEMORY_FD_INFO_KHR:
        {
            deepcopy_VkImportMemoryFdInfoKHR(pool, reinterpret_cast<const VkImportMemoryFdInfoKHR*>(structExtension), reinterpret_cast<VkImportMemoryFdInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_win32_keyed_mutex
        case VK_STRUCTURE_TYPE_WIN32_KEYED_MUTEX_ACQUIRE_RELEASE_INFO_KHR:
        {
            deepcopy_VkWin32KeyedMutexAcquireReleaseInfoKHR(pool, reinterpret_cast<const VkWin32KeyedMutexAcquireReleaseInfoKHR*>(structExtension), reinterpret_cast<VkWin32KeyedMutexAcquireReleaseInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_external_semaphore_win32
        case VK_STRUCTURE_TYPE_EXPORT_SEMAPHORE_WIN32_HANDLE_INFO_KHR:
        {
            deepcopy_VkExportSemaphoreWin32HandleInfoKHR(pool, reinterpret_cast<const VkExportSemaphoreWin32HandleInfoKHR*>(structExtension), reinterpret_cast<VkExportSemaphoreWin32HandleInfoKHR*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_D3D12_FENCE_SUBMIT_INFO_KHR:
        {
            deepcopy_VkD3D12FenceSubmitInfoKHR(pool, reinterpret_cast<const VkD3D12FenceSubmitInfoKHR*>(structExtension), reinterpret_cast<VkD3D12FenceSubmitInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_push_descriptor
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PUSH_DESCRIPTOR_PROPERTIES_KHR:
        {
            deepcopy_VkPhysicalDevicePushDescriptorPropertiesKHR(pool, reinterpret_cast<const VkPhysicalDevicePushDescriptorPropertiesKHR*>(structExtension), reinterpret_cast<VkPhysicalDevicePushDescriptorPropertiesKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_incremental_present
        case VK_STRUCTURE_TYPE_PRESENT_REGIONS_KHR:
        {
            deepcopy_VkPresentRegionsKHR(pool, reinterpret_cast<const VkPresentRegionsKHR*>(structExtension), reinterpret_cast<VkPresentRegionsKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_shared_presentable_image
        case VK_STRUCTURE_TYPE_SHARED_PRESENT_SURFACE_CAPABILITIES_KHR:
        {
            deepcopy_VkSharedPresentSurfaceCapabilitiesKHR(pool, reinterpret_cast<const VkSharedPresentSurfaceCapabilitiesKHR*>(structExtension), reinterpret_cast<VkSharedPresentSurfaceCapabilitiesKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_external_fence_win32
        case VK_STRUCTURE_TYPE_EXPORT_FENCE_WIN32_HANDLE_INFO_KHR:
        {
            deepcopy_VkExportFenceWin32HandleInfoKHR(pool, reinterpret_cast<const VkExportFenceWin32HandleInfoKHR*>(structExtension), reinterpret_cast<VkExportFenceWin32HandleInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_image_format_list
        case VK_STRUCTURE_TYPE_IMAGE_FORMAT_LIST_CREATE_INFO_KHR:
        {
            deepcopy_VkImageFormatListCreateInfoKHR(pool, reinterpret_cast<const VkImageFormatListCreateInfoKHR*>(structExtension), reinterpret_cast<VkImageFormatListCreateInfoKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_KHR_8bit_storage
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_8BIT_STORAGE_FEATURES_KHR:
        {
            deepcopy_VkPhysicalDevice8BitStorageFeaturesKHR(pool, reinterpret_cast<const VkPhysicalDevice8BitStorageFeaturesKHR*>(structExtension), reinterpret_cast<VkPhysicalDevice8BitStorageFeaturesKHR*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_ANDROID_native_buffer
        case VK_STRUCTURE_TYPE_NATIVE_BUFFER_ANDROID:
        {
            deepcopy_VkNativeBufferANDROID(pool, reinterpret_cast<const VkNativeBufferANDROID*>(structExtension), reinterpret_cast<VkNativeBufferANDROID*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_debug_report
        case VK_STRUCTURE_TYPE_DEBUG_REPORT_CALLBACK_CREATE_INFO_EXT:
        {
            deepcopy_VkDebugReportCallbackCreateInfoEXT(pool, reinterpret_cast<const VkDebugReportCallbackCreateInfoEXT*>(structExtension), reinterpret_cast<VkDebugReportCallbackCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_AMD_rasterization_order
        case VK_STRUCTURE_TYPE_PIPELINE_RASTERIZATION_STATE_RASTERIZATION_ORDER_AMD:
        {
            deepcopy_VkPipelineRasterizationStateRasterizationOrderAMD(pool, reinterpret_cast<const VkPipelineRasterizationStateRasterizationOrderAMD*>(structExtension), reinterpret_cast<VkPipelineRasterizationStateRasterizationOrderAMD*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_dedicated_allocation
        case VK_STRUCTURE_TYPE_DEDICATED_ALLOCATION_IMAGE_CREATE_INFO_NV:
        {
            deepcopy_VkDedicatedAllocationImageCreateInfoNV(pool, reinterpret_cast<const VkDedicatedAllocationImageCreateInfoNV*>(structExtension), reinterpret_cast<VkDedicatedAllocationImageCreateInfoNV*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEDICATED_ALLOCATION_BUFFER_CREATE_INFO_NV:
        {
            deepcopy_VkDedicatedAllocationBufferCreateInfoNV(pool, reinterpret_cast<const VkDedicatedAllocationBufferCreateInfoNV*>(structExtension), reinterpret_cast<VkDedicatedAllocationBufferCreateInfoNV*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DEDICATED_ALLOCATION_MEMORY_ALLOCATE_INFO_NV:
        {
            deepcopy_VkDedicatedAllocationMemoryAllocateInfoNV(pool, reinterpret_cast<const VkDedicatedAllocationMemoryAllocateInfoNV*>(structExtension), reinterpret_cast<VkDedicatedAllocationMemoryAllocateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_AMD_texture_gather_bias_lod
        case VK_STRUCTURE_TYPE_TEXTURE_LOD_GATHER_FORMAT_PROPERTIES_AMD:
        {
            deepcopy_VkTextureLODGatherFormatPropertiesAMD(pool, reinterpret_cast<const VkTextureLODGatherFormatPropertiesAMD*>(structExtension), reinterpret_cast<VkTextureLODGatherFormatPropertiesAMD*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_external_memory
        case VK_STRUCTURE_TYPE_EXTERNAL_MEMORY_IMAGE_CREATE_INFO_NV:
        {
            deepcopy_VkExternalMemoryImageCreateInfoNV(pool, reinterpret_cast<const VkExternalMemoryImageCreateInfoNV*>(structExtension), reinterpret_cast<VkExternalMemoryImageCreateInfoNV*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_MEMORY_ALLOCATE_INFO_NV:
        {
            deepcopy_VkExportMemoryAllocateInfoNV(pool, reinterpret_cast<const VkExportMemoryAllocateInfoNV*>(structExtension), reinterpret_cast<VkExportMemoryAllocateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_external_memory_win32
        case VK_STRUCTURE_TYPE_IMPORT_MEMORY_WIN32_HANDLE_INFO_NV:
        {
            deepcopy_VkImportMemoryWin32HandleInfoNV(pool, reinterpret_cast<const VkImportMemoryWin32HandleInfoNV*>(structExtension), reinterpret_cast<VkImportMemoryWin32HandleInfoNV*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXPORT_MEMORY_WIN32_HANDLE_INFO_NV:
        {
            deepcopy_VkExportMemoryWin32HandleInfoNV(pool, reinterpret_cast<const VkExportMemoryWin32HandleInfoNV*>(structExtension), reinterpret_cast<VkExportMemoryWin32HandleInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_win32_keyed_mutex
        case VK_STRUCTURE_TYPE_WIN32_KEYED_MUTEX_ACQUIRE_RELEASE_INFO_NV:
        {
            deepcopy_VkWin32KeyedMutexAcquireReleaseInfoNV(pool, reinterpret_cast<const VkWin32KeyedMutexAcquireReleaseInfoNV*>(structExtension), reinterpret_cast<VkWin32KeyedMutexAcquireReleaseInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_validation_flags
        case VK_STRUCTURE_TYPE_VALIDATION_FLAGS_EXT:
        {
            deepcopy_VkValidationFlagsEXT(pool, reinterpret_cast<const VkValidationFlagsEXT*>(structExtension), reinterpret_cast<VkValidationFlagsEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_conditional_rendering
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_CONDITIONAL_RENDERING_FEATURES_EXT:
        {
            deepcopy_VkPhysicalDeviceConditionalRenderingFeaturesEXT(pool, reinterpret_cast<const VkPhysicalDeviceConditionalRenderingFeaturesEXT*>(structExtension), reinterpret_cast<VkPhysicalDeviceConditionalRenderingFeaturesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_COMMAND_BUFFER_INHERITANCE_CONDITIONAL_RENDERING_INFO_EXT:
        {
            deepcopy_VkCommandBufferInheritanceConditionalRenderingInfoEXT(pool, reinterpret_cast<const VkCommandBufferInheritanceConditionalRenderingInfoEXT*>(structExtension), reinterpret_cast<VkCommandBufferInheritanceConditionalRenderingInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_clip_space_w_scaling
        case VK_STRUCTURE_TYPE_PIPELINE_VIEWPORT_W_SCALING_STATE_CREATE_INFO_NV:
        {
            deepcopy_VkPipelineViewportWScalingStateCreateInfoNV(pool, reinterpret_cast<const VkPipelineViewportWScalingStateCreateInfoNV*>(structExtension), reinterpret_cast<VkPipelineViewportWScalingStateCreateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_display_control
        case VK_STRUCTURE_TYPE_SWAPCHAIN_COUNTER_CREATE_INFO_EXT:
        {
            deepcopy_VkSwapchainCounterCreateInfoEXT(pool, reinterpret_cast<const VkSwapchainCounterCreateInfoEXT*>(structExtension), reinterpret_cast<VkSwapchainCounterCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_GOOGLE_display_timing
        case VK_STRUCTURE_TYPE_PRESENT_TIMES_INFO_GOOGLE:
        {
            deepcopy_VkPresentTimesInfoGOOGLE(pool, reinterpret_cast<const VkPresentTimesInfoGOOGLE*>(structExtension), reinterpret_cast<VkPresentTimesInfoGOOGLE*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NVX_multiview_per_view_attributes
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MULTIVIEW_PER_VIEW_ATTRIBUTES_PROPERTIES_NVX:
        {
            deepcopy_VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX(pool, reinterpret_cast<const VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX*>(structExtension), reinterpret_cast<VkPhysicalDeviceMultiviewPerViewAttributesPropertiesNVX*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_viewport_swizzle
        case VK_STRUCTURE_TYPE_PIPELINE_VIEWPORT_SWIZZLE_STATE_CREATE_INFO_NV:
        {
            deepcopy_VkPipelineViewportSwizzleStateCreateInfoNV(pool, reinterpret_cast<const VkPipelineViewportSwizzleStateCreateInfoNV*>(structExtension), reinterpret_cast<VkPipelineViewportSwizzleStateCreateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_discard_rectangles
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_DISCARD_RECTANGLE_PROPERTIES_EXT:
        {
            deepcopy_VkPhysicalDeviceDiscardRectanglePropertiesEXT(pool, reinterpret_cast<const VkPhysicalDeviceDiscardRectanglePropertiesEXT*>(structExtension), reinterpret_cast<VkPhysicalDeviceDiscardRectanglePropertiesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_DISCARD_RECTANGLE_STATE_CREATE_INFO_EXT:
        {
            deepcopy_VkPipelineDiscardRectangleStateCreateInfoEXT(pool, reinterpret_cast<const VkPipelineDiscardRectangleStateCreateInfoEXT*>(structExtension), reinterpret_cast<VkPipelineDiscardRectangleStateCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_conservative_rasterization
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_CONSERVATIVE_RASTERIZATION_PROPERTIES_EXT:
        {
            deepcopy_VkPhysicalDeviceConservativeRasterizationPropertiesEXT(pool, reinterpret_cast<const VkPhysicalDeviceConservativeRasterizationPropertiesEXT*>(structExtension), reinterpret_cast<VkPhysicalDeviceConservativeRasterizationPropertiesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_RASTERIZATION_CONSERVATIVE_STATE_CREATE_INFO_EXT:
        {
            deepcopy_VkPipelineRasterizationConservativeStateCreateInfoEXT(pool, reinterpret_cast<const VkPipelineRasterizationConservativeStateCreateInfoEXT*>(structExtension), reinterpret_cast<VkPipelineRasterizationConservativeStateCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_debug_utils
        case VK_STRUCTURE_TYPE_DEBUG_UTILS_MESSENGER_CREATE_INFO_EXT:
        {
            deepcopy_VkDebugUtilsMessengerCreateInfoEXT(pool, reinterpret_cast<const VkDebugUtilsMessengerCreateInfoEXT*>(structExtension), reinterpret_cast<VkDebugUtilsMessengerCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_ANDROID_external_memory_android_hardware_buffer
        case VK_STRUCTURE_TYPE_ANDROID_HARDWARE_BUFFER_USAGE_ANDROID:
        {
            deepcopy_VkAndroidHardwareBufferUsageANDROID(pool, reinterpret_cast<const VkAndroidHardwareBufferUsageANDROID*>(structExtension), reinterpret_cast<VkAndroidHardwareBufferUsageANDROID*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_ANDROID_HARDWARE_BUFFER_FORMAT_PROPERTIES_ANDROID:
        {
            deepcopy_VkAndroidHardwareBufferFormatPropertiesANDROID(pool, reinterpret_cast<const VkAndroidHardwareBufferFormatPropertiesANDROID*>(structExtension), reinterpret_cast<VkAndroidHardwareBufferFormatPropertiesANDROID*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_IMPORT_ANDROID_HARDWARE_BUFFER_INFO_ANDROID:
        {
            deepcopy_VkImportAndroidHardwareBufferInfoANDROID(pool, reinterpret_cast<const VkImportAndroidHardwareBufferInfoANDROID*>(structExtension), reinterpret_cast<VkImportAndroidHardwareBufferInfoANDROID*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_EXTERNAL_FORMAT_ANDROID:
        {
            deepcopy_VkExternalFormatANDROID(pool, reinterpret_cast<const VkExternalFormatANDROID*>(structExtension), reinterpret_cast<VkExternalFormatANDROID*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_sampler_filter_minmax
        case VK_STRUCTURE_TYPE_SAMPLER_REDUCTION_MODE_CREATE_INFO_EXT:
        {
            deepcopy_VkSamplerReductionModeCreateInfoEXT(pool, reinterpret_cast<const VkSamplerReductionModeCreateInfoEXT*>(structExtension), reinterpret_cast<VkSamplerReductionModeCreateInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SAMPLER_FILTER_MINMAX_PROPERTIES_EXT:
        {
            deepcopy_VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT(pool, reinterpret_cast<const VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT*>(structExtension), reinterpret_cast<VkPhysicalDeviceSamplerFilterMinmaxPropertiesEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_sample_locations
        case VK_STRUCTURE_TYPE_SAMPLE_LOCATIONS_INFO_EXT:
        {
            deepcopy_VkSampleLocationsInfoEXT(pool, reinterpret_cast<const VkSampleLocationsInfoEXT*>(structExtension), reinterpret_cast<VkSampleLocationsInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_RENDER_PASS_SAMPLE_LOCATIONS_BEGIN_INFO_EXT:
        {
            deepcopy_VkRenderPassSampleLocationsBeginInfoEXT(pool, reinterpret_cast<const VkRenderPassSampleLocationsBeginInfoEXT*>(structExtension), reinterpret_cast<VkRenderPassSampleLocationsBeginInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_SAMPLE_LOCATIONS_STATE_CREATE_INFO_EXT:
        {
            deepcopy_VkPipelineSampleLocationsStateCreateInfoEXT(pool, reinterpret_cast<const VkPipelineSampleLocationsStateCreateInfoEXT*>(structExtension), reinterpret_cast<VkPipelineSampleLocationsStateCreateInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SAMPLE_LOCATIONS_PROPERTIES_EXT:
        {
            deepcopy_VkPhysicalDeviceSampleLocationsPropertiesEXT(pool, reinterpret_cast<const VkPhysicalDeviceSampleLocationsPropertiesEXT*>(structExtension), reinterpret_cast<VkPhysicalDeviceSampleLocationsPropertiesEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_blend_operation_advanced
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_BLEND_OPERATION_ADVANCED_FEATURES_EXT:
        {
            deepcopy_VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT(pool, reinterpret_cast<const VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT*>(structExtension), reinterpret_cast<VkPhysicalDeviceBlendOperationAdvancedFeaturesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_BLEND_OPERATION_ADVANCED_PROPERTIES_EXT:
        {
            deepcopy_VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT(pool, reinterpret_cast<const VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT*>(structExtension), reinterpret_cast<VkPhysicalDeviceBlendOperationAdvancedPropertiesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_COLOR_BLEND_ADVANCED_STATE_CREATE_INFO_EXT:
        {
            deepcopy_VkPipelineColorBlendAdvancedStateCreateInfoEXT(pool, reinterpret_cast<const VkPipelineColorBlendAdvancedStateCreateInfoEXT*>(structExtension), reinterpret_cast<VkPipelineColorBlendAdvancedStateCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_fragment_coverage_to_color
        case VK_STRUCTURE_TYPE_PIPELINE_COVERAGE_TO_COLOR_STATE_CREATE_INFO_NV:
        {
            deepcopy_VkPipelineCoverageToColorStateCreateInfoNV(pool, reinterpret_cast<const VkPipelineCoverageToColorStateCreateInfoNV*>(structExtension), reinterpret_cast<VkPipelineCoverageToColorStateCreateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_framebuffer_mixed_samples
        case VK_STRUCTURE_TYPE_PIPELINE_COVERAGE_MODULATION_STATE_CREATE_INFO_NV:
        {
            deepcopy_VkPipelineCoverageModulationStateCreateInfoNV(pool, reinterpret_cast<const VkPipelineCoverageModulationStateCreateInfoNV*>(structExtension), reinterpret_cast<VkPipelineCoverageModulationStateCreateInfoNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_validation_cache
        case VK_STRUCTURE_TYPE_SHADER_MODULE_VALIDATION_CACHE_CREATE_INFO_EXT:
        {
            deepcopy_VkShaderModuleValidationCacheCreateInfoEXT(pool, reinterpret_cast<const VkShaderModuleValidationCacheCreateInfoEXT*>(structExtension), reinterpret_cast<VkShaderModuleValidationCacheCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_descriptor_indexing
        case VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_BINDING_FLAGS_CREATE_INFO_EXT:
        {
            deepcopy_VkDescriptorSetLayoutBindingFlagsCreateInfoEXT(pool, reinterpret_cast<const VkDescriptorSetLayoutBindingFlagsCreateInfoEXT*>(structExtension), reinterpret_cast<VkDescriptorSetLayoutBindingFlagsCreateInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_DESCRIPTOR_INDEXING_FEATURES_EXT:
        {
            deepcopy_VkPhysicalDeviceDescriptorIndexingFeaturesEXT(pool, reinterpret_cast<const VkPhysicalDeviceDescriptorIndexingFeaturesEXT*>(structExtension), reinterpret_cast<VkPhysicalDeviceDescriptorIndexingFeaturesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_DESCRIPTOR_INDEXING_PROPERTIES_EXT:
        {
            deepcopy_VkPhysicalDeviceDescriptorIndexingPropertiesEXT(pool, reinterpret_cast<const VkPhysicalDeviceDescriptorIndexingPropertiesEXT*>(structExtension), reinterpret_cast<VkPhysicalDeviceDescriptorIndexingPropertiesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DESCRIPTOR_SET_VARIABLE_DESCRIPTOR_COUNT_ALLOCATE_INFO_EXT:
        {
            deepcopy_VkDescriptorSetVariableDescriptorCountAllocateInfoEXT(pool, reinterpret_cast<const VkDescriptorSetVariableDescriptorCountAllocateInfoEXT*>(structExtension), reinterpret_cast<VkDescriptorSetVariableDescriptorCountAllocateInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_DESCRIPTOR_SET_VARIABLE_DESCRIPTOR_COUNT_LAYOUT_SUPPORT_EXT:
        {
            deepcopy_VkDescriptorSetVariableDescriptorCountLayoutSupportEXT(pool, reinterpret_cast<const VkDescriptorSetVariableDescriptorCountLayoutSupportEXT*>(structExtension), reinterpret_cast<VkDescriptorSetVariableDescriptorCountLayoutSupportEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_global_priority
        case VK_STRUCTURE_TYPE_DEVICE_QUEUE_GLOBAL_PRIORITY_CREATE_INFO_EXT:
        {
            deepcopy_VkDeviceQueueGlobalPriorityCreateInfoEXT(pool, reinterpret_cast<const VkDeviceQueueGlobalPriorityCreateInfoEXT*>(structExtension), reinterpret_cast<VkDeviceQueueGlobalPriorityCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_external_memory_host
        case VK_STRUCTURE_TYPE_IMPORT_MEMORY_HOST_POINTER_INFO_EXT:
        {
            deepcopy_VkImportMemoryHostPointerInfoEXT(pool, reinterpret_cast<const VkImportMemoryHostPointerInfoEXT*>(structExtension), reinterpret_cast<VkImportMemoryHostPointerInfoEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_EXTERNAL_MEMORY_HOST_PROPERTIES_EXT:
        {
            deepcopy_VkPhysicalDeviceExternalMemoryHostPropertiesEXT(pool, reinterpret_cast<const VkPhysicalDeviceExternalMemoryHostPropertiesEXT*>(structExtension), reinterpret_cast<VkPhysicalDeviceExternalMemoryHostPropertiesEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_AMD_shader_core_properties
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SHADER_CORE_PROPERTIES_AMD:
        {
            deepcopy_VkPhysicalDeviceShaderCorePropertiesAMD(pool, reinterpret_cast<const VkPhysicalDeviceShaderCorePropertiesAMD*>(structExtension), reinterpret_cast<VkPhysicalDeviceShaderCorePropertiesAMD*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_EXT_vertex_attribute_divisor
        case VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_VERTEX_ATTRIBUTE_DIVISOR_PROPERTIES_EXT:
        {
            deepcopy_VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT(pool, reinterpret_cast<const VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT*>(structExtension), reinterpret_cast<VkPhysicalDeviceVertexAttributeDivisorPropertiesEXT*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_PIPELINE_VERTEX_INPUT_DIVISOR_STATE_CREATE_INFO_EXT:
        {
            deepcopy_VkPipelineVertexInputDivisorStateCreateInfoEXT(pool, reinterpret_cast<const VkPipelineVertexInputDivisorStateCreateInfoEXT*>(structExtension), reinterpret_cast<VkPipelineVertexInputDivisorStateCreateInfoEXT*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_NV_device_diagnostic_checkpoints
        case VK_STRUCTURE_TYPE_QUEUE_FAMILY_CHECKPOINT_PROPERTIES_NV:
        {
            deepcopy_VkQueueFamilyCheckpointPropertiesNV(pool, reinterpret_cast<const VkQueueFamilyCheckpointPropertiesNV*>(structExtension), reinterpret_cast<VkQueueFamilyCheckpointPropertiesNV*>(structExtension_out));
            break;
        }
#endif
#ifdef VK_GOOGLE_color_buffer
        case VK_STRUCTURE_TYPE_IMPORT_COLOR_BUFFER_GOOGLE:
        {
            deepcopy_VkImportColorBufferGOOGLE(pool, reinterpret_cast<const VkImportColorBufferGOOGLE*>(structExtension), reinterpret_cast<VkImportColorBufferGOOGLE*>(structExtension_out));
            break;
        }
        case VK_STRUCTURE_TYPE_IMPORT_PHYSICAL_ADDRESS_GOOGLE:
        {
            deepcopy_VkImportPhysicalAddressGOOGLE(pool, reinterpret_cast<const VkImportPhysicalAddressGOOGLE*>(structExtension), reinterpret_cast<VkImportPhysicalAddressGOOGLE*>(structExtension_out));
            break;
        }
#endif
        default:
        {
            return;
        }
    }
}


} // namespace goldfish_vk

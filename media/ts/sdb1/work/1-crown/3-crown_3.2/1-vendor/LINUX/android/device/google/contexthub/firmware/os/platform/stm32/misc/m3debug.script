#
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

set flash-start  08000000
set flash-size   00080000
set flash-block  00000400
set flash-buffer 20000200

function flash-setup
	attach
	reset-stop
        stop
        stop

        #prepare for erase
	download misc/platform/stm32f4xx/flash_script/tool.bin 20000100
        wr sp 200000fc
	wr psr 01000000
	wr 200000fc be00be00
	wr lr 200000fd

        #do erase
        wr pc 20000100
        go
end


function flash-erase
	#nothing since setup did the erase
end

function flash-write
	wr r0 $1
        wr r1 $flash-buffer
	wr pc 20000104
	wr lr 200000fd
	go
end


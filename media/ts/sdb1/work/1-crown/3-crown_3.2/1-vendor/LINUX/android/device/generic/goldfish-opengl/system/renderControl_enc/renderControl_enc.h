// Generated Code - DO NOT EDIT !!
// generated by 'emugen'

#ifndef GUARD_renderControl_encoder_context_t
#define GUARD_renderControl_encoder_context_t

#include "IOStream.h"
#include "ChecksumCalculator.h"
#include "renderControl_client_context.h"


#include <stdint.h>
#include <EGL/egl.h>
#include "glUtils.h"

struct renderControl_encoder_context_t : public renderControl_client_context_t {

	IOStream *m_stream;
	ChecksumCalculator *m_checksumCalculator;

	renderControl_encoder_context_t(IOStream *stream, ChecksumCalculator *checksumCalculator);
	virtual uint64_t lockAndWriteDma(void*, uint32_t) { return 0; }
};

#endif  // GUARD_renderControl_encoder_context_t

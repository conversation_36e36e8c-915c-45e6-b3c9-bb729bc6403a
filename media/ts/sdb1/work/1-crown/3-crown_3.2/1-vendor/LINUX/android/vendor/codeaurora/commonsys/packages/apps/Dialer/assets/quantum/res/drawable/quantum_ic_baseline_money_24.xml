<!--
  ~ Copyright (c) 2021, The Linux Foundation. All rights reserved.
  ~
  ~ Redistribution and use in source and binary forms, with or without
  ~ modification, are permitted provided that the following conditions are
  ~ met:
  ~     * Redistributions of source code must retain the above copyright
  ~       notice, this list of conditions and the following disclaimer.
  ~     * Redistributions in binary form must reproduce the above
  ~       copyright notice, this list of conditions and the following
  ~       disclaimer in the documentation and/or other materials provided
  ~       with the distribution.
  ~     * Neither the name of The Linux Foundation nor the names of its
  ~       contributors may be used to endorse or promote products derived
  ~       from this software without specific prior written permission.
  ~
  ~ THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
  ~ WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  ~ MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT
  ~ ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
  ~ BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
  ~ CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
  ~ SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
  ~ BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
  ~ WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
  ~ OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
  ~ IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  -->
<vector android:height="24dp" android:tint="#989898"
    android:viewportHeight="24" android:viewportWidth="24"
    android:width="24dp" xmlns:android="http://schemas.android.com/apk/res/android">
    <path android:fillColor="@android:color/white" android:pathData="M5,8h2v8L5,16zM12,8L9,8c-0.55,0 -1,0.45 -1,1v6c0,0.55 0.45,1 1,1h3c0.55,0 1,-0.45 1,-1L13,9c0,-0.55 -0.45,-1 -1,-1zM11,14h-1v-4h1v4zM18,8h-3c-0.55,0 -1,0.45 -1,1v6c0,0.55 0.45,1 1,1h3c0.55,0 1,-0.45 1,-1L19,9c0,-0.55 -0.45,-1 -1,-1zM17,14h-1v-4h1v4z"/>
    <path android:fillColor="@android:color/white" android:pathData="M2,4v16h20L22,4L2,4zM4,18L4,6h16v12L4,18z"/>
</vector>

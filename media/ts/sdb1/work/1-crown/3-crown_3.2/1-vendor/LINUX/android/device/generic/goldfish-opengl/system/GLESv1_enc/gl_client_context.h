// Generated Code - DO NOT EDIT !!
// generated by 'emugen'
#ifndef __gl_client_context_t_h
#define __gl_client_context_t_h

#include "gl_client_proc.h"

#include "gl_types.h"


struct gl_client_context_t {

	glAlphaFunc_client_proc_t glAlphaFunc;
	glClearColor_client_proc_t glClearColor;
	glClearDepthf_client_proc_t glClearDepthf;
	glClipPlanef_client_proc_t glClipPlanef;
	glColor4f_client_proc_t glColor4f;
	glDepthRangef_client_proc_t glDepthRangef;
	glFogf_client_proc_t glFogf;
	glFogfv_client_proc_t glFogfv;
	glFrustumf_client_proc_t glFrustumf;
	glGetClipPlanef_client_proc_t glGetClipPlanef;
	glGetFloatv_client_proc_t glGetFloatv;
	glGetLightfv_client_proc_t glGetLightfv;
	glGetMaterialfv_client_proc_t glGetMaterialfv;
	glGetTexEnvfv_client_proc_t glGetTexEnvfv;
	glGetTexParameterfv_client_proc_t glGetTexParameterfv;
	glLightModelf_client_proc_t glLightModelf;
	glLightModelfv_client_proc_t glLightModelfv;
	glLightf_client_proc_t glLightf;
	glLightfv_client_proc_t glLightfv;
	glLineWidth_client_proc_t glLineWidth;
	glLoadMatrixf_client_proc_t glLoadMatrixf;
	glMaterialf_client_proc_t glMaterialf;
	glMaterialfv_client_proc_t glMaterialfv;
	glMultMatrixf_client_proc_t glMultMatrixf;
	glMultiTexCoord4f_client_proc_t glMultiTexCoord4f;
	glNormal3f_client_proc_t glNormal3f;
	glOrthof_client_proc_t glOrthof;
	glPointParameterf_client_proc_t glPointParameterf;
	glPointParameterfv_client_proc_t glPointParameterfv;
	glPointSize_client_proc_t glPointSize;
	glPolygonOffset_client_proc_t glPolygonOffset;
	glRotatef_client_proc_t glRotatef;
	glScalef_client_proc_t glScalef;
	glTexEnvf_client_proc_t glTexEnvf;
	glTexEnvfv_client_proc_t glTexEnvfv;
	glTexParameterf_client_proc_t glTexParameterf;
	glTexParameterfv_client_proc_t glTexParameterfv;
	glTranslatef_client_proc_t glTranslatef;
	glActiveTexture_client_proc_t glActiveTexture;
	glAlphaFuncx_client_proc_t glAlphaFuncx;
	glBindBuffer_client_proc_t glBindBuffer;
	glBindTexture_client_proc_t glBindTexture;
	glBlendFunc_client_proc_t glBlendFunc;
	glBufferData_client_proc_t glBufferData;
	glBufferSubData_client_proc_t glBufferSubData;
	glClear_client_proc_t glClear;
	glClearColorx_client_proc_t glClearColorx;
	glClearDepthx_client_proc_t glClearDepthx;
	glClearStencil_client_proc_t glClearStencil;
	glClientActiveTexture_client_proc_t glClientActiveTexture;
	glColor4ub_client_proc_t glColor4ub;
	glColor4x_client_proc_t glColor4x;
	glColorMask_client_proc_t glColorMask;
	glColorPointer_client_proc_t glColorPointer;
	glCompressedTexImage2D_client_proc_t glCompressedTexImage2D;
	glCompressedTexSubImage2D_client_proc_t glCompressedTexSubImage2D;
	glCopyTexImage2D_client_proc_t glCopyTexImage2D;
	glCopyTexSubImage2D_client_proc_t glCopyTexSubImage2D;
	glCullFace_client_proc_t glCullFace;
	glDeleteBuffers_client_proc_t glDeleteBuffers;
	glDeleteTextures_client_proc_t glDeleteTextures;
	glDepthFunc_client_proc_t glDepthFunc;
	glDepthMask_client_proc_t glDepthMask;
	glDepthRangex_client_proc_t glDepthRangex;
	glDisable_client_proc_t glDisable;
	glDisableClientState_client_proc_t glDisableClientState;
	glDrawArrays_client_proc_t glDrawArrays;
	glDrawElements_client_proc_t glDrawElements;
	glEnable_client_proc_t glEnable;
	glEnableClientState_client_proc_t glEnableClientState;
	glFinish_client_proc_t glFinish;
	glFlush_client_proc_t glFlush;
	glFogx_client_proc_t glFogx;
	glFogxv_client_proc_t glFogxv;
	glFrontFace_client_proc_t glFrontFace;
	glFrustumx_client_proc_t glFrustumx;
	glGetBooleanv_client_proc_t glGetBooleanv;
	glGetBufferParameteriv_client_proc_t glGetBufferParameteriv;
	glClipPlanex_client_proc_t glClipPlanex;
	glGenBuffers_client_proc_t glGenBuffers;
	glGenTextures_client_proc_t glGenTextures;
	glGetError_client_proc_t glGetError;
	glGetFixedv_client_proc_t glGetFixedv;
	glGetIntegerv_client_proc_t glGetIntegerv;
	glGetLightxv_client_proc_t glGetLightxv;
	glGetMaterialxv_client_proc_t glGetMaterialxv;
	glGetPointerv_client_proc_t glGetPointerv;
	glGetString_client_proc_t glGetString;
	glGetTexEnviv_client_proc_t glGetTexEnviv;
	glGetTexEnvxv_client_proc_t glGetTexEnvxv;
	glGetTexParameteriv_client_proc_t glGetTexParameteriv;
	glGetTexParameterxv_client_proc_t glGetTexParameterxv;
	glHint_client_proc_t glHint;
	glIsBuffer_client_proc_t glIsBuffer;
	glIsEnabled_client_proc_t glIsEnabled;
	glIsTexture_client_proc_t glIsTexture;
	glLightModelx_client_proc_t glLightModelx;
	glLightModelxv_client_proc_t glLightModelxv;
	glLightx_client_proc_t glLightx;
	glLightxv_client_proc_t glLightxv;
	glLineWidthx_client_proc_t glLineWidthx;
	glLoadIdentity_client_proc_t glLoadIdentity;
	glLoadMatrixx_client_proc_t glLoadMatrixx;
	glLogicOp_client_proc_t glLogicOp;
	glMaterialx_client_proc_t glMaterialx;
	glMaterialxv_client_proc_t glMaterialxv;
	glMatrixMode_client_proc_t glMatrixMode;
	glMultMatrixx_client_proc_t glMultMatrixx;
	glMultiTexCoord4x_client_proc_t glMultiTexCoord4x;
	glNormal3x_client_proc_t glNormal3x;
	glNormalPointer_client_proc_t glNormalPointer;
	glOrthox_client_proc_t glOrthox;
	glPixelStorei_client_proc_t glPixelStorei;
	glPointParameterx_client_proc_t glPointParameterx;
	glPointParameterxv_client_proc_t glPointParameterxv;
	glPointSizex_client_proc_t glPointSizex;
	glPolygonOffsetx_client_proc_t glPolygonOffsetx;
	glPopMatrix_client_proc_t glPopMatrix;
	glPushMatrix_client_proc_t glPushMatrix;
	glReadPixels_client_proc_t glReadPixels;
	glRotatex_client_proc_t glRotatex;
	glSampleCoverage_client_proc_t glSampleCoverage;
	glSampleCoveragex_client_proc_t glSampleCoveragex;
	glScalex_client_proc_t glScalex;
	glScissor_client_proc_t glScissor;
	glShadeModel_client_proc_t glShadeModel;
	glStencilFunc_client_proc_t glStencilFunc;
	glStencilMask_client_proc_t glStencilMask;
	glStencilOp_client_proc_t glStencilOp;
	glTexCoordPointer_client_proc_t glTexCoordPointer;
	glTexEnvi_client_proc_t glTexEnvi;
	glTexEnvx_client_proc_t glTexEnvx;
	glTexEnviv_client_proc_t glTexEnviv;
	glTexEnvxv_client_proc_t glTexEnvxv;
	glTexImage2D_client_proc_t glTexImage2D;
	glTexParameteri_client_proc_t glTexParameteri;
	glTexParameterx_client_proc_t glTexParameterx;
	glTexParameteriv_client_proc_t glTexParameteriv;
	glTexParameterxv_client_proc_t glTexParameterxv;
	glTexSubImage2D_client_proc_t glTexSubImage2D;
	glTranslatex_client_proc_t glTranslatex;
	glVertexPointer_client_proc_t glVertexPointer;
	glViewport_client_proc_t glViewport;
	glPointSizePointerOES_client_proc_t glPointSizePointerOES;
	glVertexPointerOffset_client_proc_t glVertexPointerOffset;
	glColorPointerOffset_client_proc_t glColorPointerOffset;
	glNormalPointerOffset_client_proc_t glNormalPointerOffset;
	glPointSizePointerOffset_client_proc_t glPointSizePointerOffset;
	glTexCoordPointerOffset_client_proc_t glTexCoordPointerOffset;
	glWeightPointerOffset_client_proc_t glWeightPointerOffset;
	glMatrixIndexPointerOffset_client_proc_t glMatrixIndexPointerOffset;
	glVertexPointerData_client_proc_t glVertexPointerData;
	glColorPointerData_client_proc_t glColorPointerData;
	glNormalPointerData_client_proc_t glNormalPointerData;
	glTexCoordPointerData_client_proc_t glTexCoordPointerData;
	glPointSizePointerData_client_proc_t glPointSizePointerData;
	glWeightPointerData_client_proc_t glWeightPointerData;
	glMatrixIndexPointerData_client_proc_t glMatrixIndexPointerData;
	glDrawElementsOffset_client_proc_t glDrawElementsOffset;
	glDrawElementsData_client_proc_t glDrawElementsData;
	glGetCompressedTextureFormats_client_proc_t glGetCompressedTextureFormats;
	glFinishRoundTrip_client_proc_t glFinishRoundTrip;
	glBlendEquationSeparateOES_client_proc_t glBlendEquationSeparateOES;
	glBlendFuncSeparateOES_client_proc_t glBlendFuncSeparateOES;
	glBlendEquationOES_client_proc_t glBlendEquationOES;
	glDrawTexsOES_client_proc_t glDrawTexsOES;
	glDrawTexiOES_client_proc_t glDrawTexiOES;
	glDrawTexxOES_client_proc_t glDrawTexxOES;
	glDrawTexsvOES_client_proc_t glDrawTexsvOES;
	glDrawTexivOES_client_proc_t glDrawTexivOES;
	glDrawTexxvOES_client_proc_t glDrawTexxvOES;
	glDrawTexfOES_client_proc_t glDrawTexfOES;
	glDrawTexfvOES_client_proc_t glDrawTexfvOES;
	glEGLImageTargetTexture2DOES_client_proc_t glEGLImageTargetTexture2DOES;
	glEGLImageTargetRenderbufferStorageOES_client_proc_t glEGLImageTargetRenderbufferStorageOES;
	glAlphaFuncxOES_client_proc_t glAlphaFuncxOES;
	glClearColorxOES_client_proc_t glClearColorxOES;
	glClearDepthxOES_client_proc_t glClearDepthxOES;
	glClipPlanexOES_client_proc_t glClipPlanexOES;
	glClipPlanexIMG_client_proc_t glClipPlanexIMG;
	glColor4xOES_client_proc_t glColor4xOES;
	glDepthRangexOES_client_proc_t glDepthRangexOES;
	glFogxOES_client_proc_t glFogxOES;
	glFogxvOES_client_proc_t glFogxvOES;
	glFrustumxOES_client_proc_t glFrustumxOES;
	glGetClipPlanexOES_client_proc_t glGetClipPlanexOES;
	glGetClipPlanex_client_proc_t glGetClipPlanex;
	glGetFixedvOES_client_proc_t glGetFixedvOES;
	glGetLightxvOES_client_proc_t glGetLightxvOES;
	glGetMaterialxvOES_client_proc_t glGetMaterialxvOES;
	glGetTexEnvxvOES_client_proc_t glGetTexEnvxvOES;
	glGetTexParameterxvOES_client_proc_t glGetTexParameterxvOES;
	glLightModelxOES_client_proc_t glLightModelxOES;
	glLightModelxvOES_client_proc_t glLightModelxvOES;
	glLightxOES_client_proc_t glLightxOES;
	glLightxvOES_client_proc_t glLightxvOES;
	glLineWidthxOES_client_proc_t glLineWidthxOES;
	glLoadMatrixxOES_client_proc_t glLoadMatrixxOES;
	glMaterialxOES_client_proc_t glMaterialxOES;
	glMaterialxvOES_client_proc_t glMaterialxvOES;
	glMultMatrixxOES_client_proc_t glMultMatrixxOES;
	glMultiTexCoord4xOES_client_proc_t glMultiTexCoord4xOES;
	glNormal3xOES_client_proc_t glNormal3xOES;
	glOrthoxOES_client_proc_t glOrthoxOES;
	glPointParameterxOES_client_proc_t glPointParameterxOES;
	glPointParameterxvOES_client_proc_t glPointParameterxvOES;
	glPointSizexOES_client_proc_t glPointSizexOES;
	glPolygonOffsetxOES_client_proc_t glPolygonOffsetxOES;
	glRotatexOES_client_proc_t glRotatexOES;
	glSampleCoveragexOES_client_proc_t glSampleCoveragexOES;
	glScalexOES_client_proc_t glScalexOES;
	glTexEnvxOES_client_proc_t glTexEnvxOES;
	glTexEnvxvOES_client_proc_t glTexEnvxvOES;
	glTexParameterxOES_client_proc_t glTexParameterxOES;
	glTexParameterxvOES_client_proc_t glTexParameterxvOES;
	glTranslatexOES_client_proc_t glTranslatexOES;
	glIsRenderbufferOES_client_proc_t glIsRenderbufferOES;
	glBindRenderbufferOES_client_proc_t glBindRenderbufferOES;
	glDeleteRenderbuffersOES_client_proc_t glDeleteRenderbuffersOES;
	glGenRenderbuffersOES_client_proc_t glGenRenderbuffersOES;
	glRenderbufferStorageOES_client_proc_t glRenderbufferStorageOES;
	glGetRenderbufferParameterivOES_client_proc_t glGetRenderbufferParameterivOES;
	glIsFramebufferOES_client_proc_t glIsFramebufferOES;
	glBindFramebufferOES_client_proc_t glBindFramebufferOES;
	glDeleteFramebuffersOES_client_proc_t glDeleteFramebuffersOES;
	glGenFramebuffersOES_client_proc_t glGenFramebuffersOES;
	glCheckFramebufferStatusOES_client_proc_t glCheckFramebufferStatusOES;
	glFramebufferRenderbufferOES_client_proc_t glFramebufferRenderbufferOES;
	glFramebufferTexture2DOES_client_proc_t glFramebufferTexture2DOES;
	glGetFramebufferAttachmentParameterivOES_client_proc_t glGetFramebufferAttachmentParameterivOES;
	glGenerateMipmapOES_client_proc_t glGenerateMipmapOES;
	glMapBufferOES_client_proc_t glMapBufferOES;
	glUnmapBufferOES_client_proc_t glUnmapBufferOES;
	glGetBufferPointervOES_client_proc_t glGetBufferPointervOES;
	glCurrentPaletteMatrixOES_client_proc_t glCurrentPaletteMatrixOES;
	glLoadPaletteFromModelViewMatrixOES_client_proc_t glLoadPaletteFromModelViewMatrixOES;
	glMatrixIndexPointerOES_client_proc_t glMatrixIndexPointerOES;
	glWeightPointerOES_client_proc_t glWeightPointerOES;
	glQueryMatrixxOES_client_proc_t glQueryMatrixxOES;
	glDepthRangefOES_client_proc_t glDepthRangefOES;
	glFrustumfOES_client_proc_t glFrustumfOES;
	glOrthofOES_client_proc_t glOrthofOES;
	glClipPlanefOES_client_proc_t glClipPlanefOES;
	glClipPlanefIMG_client_proc_t glClipPlanefIMG;
	glGetClipPlanefOES_client_proc_t glGetClipPlanefOES;
	glClearDepthfOES_client_proc_t glClearDepthfOES;
	glTexGenfOES_client_proc_t glTexGenfOES;
	glTexGenfvOES_client_proc_t glTexGenfvOES;
	glTexGeniOES_client_proc_t glTexGeniOES;
	glTexGenivOES_client_proc_t glTexGenivOES;
	glTexGenxOES_client_proc_t glTexGenxOES;
	glTexGenxvOES_client_proc_t glTexGenxvOES;
	glGetTexGenfvOES_client_proc_t glGetTexGenfvOES;
	glGetTexGenivOES_client_proc_t glGetTexGenivOES;
	glGetTexGenxvOES_client_proc_t glGetTexGenxvOES;
	glBindVertexArrayOES_client_proc_t glBindVertexArrayOES;
	glDeleteVertexArraysOES_client_proc_t glDeleteVertexArraysOES;
	glGenVertexArraysOES_client_proc_t glGenVertexArraysOES;
	glIsVertexArrayOES_client_proc_t glIsVertexArrayOES;
	glDiscardFramebufferEXT_client_proc_t glDiscardFramebufferEXT;
	glMultiDrawArraysEXT_client_proc_t glMultiDrawArraysEXT;
	glMultiDrawElementsEXT_client_proc_t glMultiDrawElementsEXT;
	glMultiDrawArraysSUN_client_proc_t glMultiDrawArraysSUN;
	glMultiDrawElementsSUN_client_proc_t glMultiDrawElementsSUN;
	glRenderbufferStorageMultisampleIMG_client_proc_t glRenderbufferStorageMultisampleIMG;
	glFramebufferTexture2DMultisampleIMG_client_proc_t glFramebufferTexture2DMultisampleIMG;
	glDeleteFencesNV_client_proc_t glDeleteFencesNV;
	glGenFencesNV_client_proc_t glGenFencesNV;
	glIsFenceNV_client_proc_t glIsFenceNV;
	glTestFenceNV_client_proc_t glTestFenceNV;
	glGetFenceivNV_client_proc_t glGetFenceivNV;
	glFinishFenceNV_client_proc_t glFinishFenceNV;
	glSetFenceNV_client_proc_t glSetFenceNV;
	glGetDriverControlsQCOM_client_proc_t glGetDriverControlsQCOM;
	glGetDriverControlStringQCOM_client_proc_t glGetDriverControlStringQCOM;
	glEnableDriverControlQCOM_client_proc_t glEnableDriverControlQCOM;
	glDisableDriverControlQCOM_client_proc_t glDisableDriverControlQCOM;
	glExtGetTexturesQCOM_client_proc_t glExtGetTexturesQCOM;
	glExtGetBuffersQCOM_client_proc_t glExtGetBuffersQCOM;
	glExtGetRenderbuffersQCOM_client_proc_t glExtGetRenderbuffersQCOM;
	glExtGetFramebuffersQCOM_client_proc_t glExtGetFramebuffersQCOM;
	glExtGetTexLevelParameterivQCOM_client_proc_t glExtGetTexLevelParameterivQCOM;
	glExtTexObjectStateOverrideiQCOM_client_proc_t glExtTexObjectStateOverrideiQCOM;
	glExtGetTexSubImageQCOM_client_proc_t glExtGetTexSubImageQCOM;
	glExtGetBufferPointervQCOM_client_proc_t glExtGetBufferPointervQCOM;
	glExtGetShadersQCOM_client_proc_t glExtGetShadersQCOM;
	glExtGetProgramsQCOM_client_proc_t glExtGetProgramsQCOM;
	glExtIsProgramBinaryQCOM_client_proc_t glExtIsProgramBinaryQCOM;
	glExtGetProgramBinarySourceQCOM_client_proc_t glExtGetProgramBinarySourceQCOM;
	glStartTilingQCOM_client_proc_t glStartTilingQCOM;
	glEndTilingQCOM_client_proc_t glEndTilingQCOM;
	glGetGraphicsResetStatusEXT_client_proc_t glGetGraphicsResetStatusEXT;
	glReadnPixelsEXT_client_proc_t glReadnPixelsEXT;
	virtual ~gl_client_context_t() {}

	typedef gl_client_context_t *CONTEXT_ACCESSOR_TYPE(void);
	static void setContextAccessor(CONTEXT_ACCESSOR_TYPE *f);
	int initDispatchByName( void *(*getProc)(const char *name, void *userData), void *userData);
	virtual void setError(unsigned int  error){ (void)error; };
	virtual unsigned int getError(){ return 0; };
};

#endif

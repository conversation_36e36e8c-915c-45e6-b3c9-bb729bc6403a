/*
 * Copyright (C) 2009 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.contacts.common.util;

public class Constants {

  /**
   * Log tag for performance measurement. To enable: adb shell setprop log.tag.ContactsPerf VERBOSE
   */
  public static final String PERFORMANCE_TAG = "ContactsPerf";

  // Used for lookup URI that contains an encoded JSON string.
  public static final String LOOKUP_URI_ENCODED = "encoded";
}

//
// Copyright (C) 2017 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

cc_binary_host {
    name: "vnc_server",
    srcs: [
        "blackboard.cpp",
        "frame_buffer_watcher.cpp",
        "jpeg_compressor.cpp",
        "main.cpp",
        "simulated_hw_composer.cpp",
        "virtual_inputs.cpp",
        "vnc_client_connection.cpp",
        "vnc_server.cpp",
    ],
    shared_libs: [
        "libcuttlefish_fs",
        "libcuttlefish_utils",
        "cuttlefish_tcp_socket",
        "libbase",
        "liblog",
    ],
    static_libs: [
        "libcuttlefish_host_config",
        "libcuttlefish_screen_connector",
        "libcuttlefish_wayland_server",
        "libffi",
        "libjsoncpp",
        "libjpeg",
        "libgflags",
        "libwayland_server",
        "libwayland_extension_server_protocols",
    ],
    defaults: ["cuttlefish_host_only"],
}

#
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

include $(BUILD_SYSTEM)/aux_toolchain.mk

ifeq ($(AUX_BUILD_NOT_COMPATIBLE),)

include $(NANO_BUILD)/config_internal.mk
include $(BUILD_AUX_STATIC_LIBRARY)

endif # AUX_BUILD_NOT_COMPATIBLE

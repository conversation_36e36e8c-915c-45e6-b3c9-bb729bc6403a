HTS221 environmental sensor device driver for Google nanohub.

- Supported features:

A. Report Humidity data
B. Different data rates: 1/7/12.5 Hz
C. I2C protocol

This driver does not support the DRDy interrupt.
Instead, a timer callback is used to read the data.


- Platform/variant porting:

The driver requires that following macros are defined in the variant.h
file of the specific variant:

  HTS221_I2C_BUS_ID    /* specify I2C Bus ID */
  HTS221_I2C_SPEED     /* specify I2C Bus speed in hz */
  HTS221_I2C_ADDR      /* specify device I2C address */

Example:

  /*
   * Define platform/variant dependent HTS221 device macros
   */

  /* I2C defs to be used when device is plugged on U4 slot */
  #define HTS221_I2C_BUS_ID      0
  #define HTS221_I2C_SPEED       400000
  #define HTS221_I2C_ADDR        0x5F

If these macros are not defined in the current variant the driver forces a compilation
error.

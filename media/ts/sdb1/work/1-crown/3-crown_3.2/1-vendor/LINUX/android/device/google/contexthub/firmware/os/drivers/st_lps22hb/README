LPS22HB environmental sensor device driver for Google nanohub.

- Supported features:

A. Report Pressure and Temperature data
B. Different data rates: 1/10/25/50/75 Hz
C. I2C protocol

This driver does not support the DRDy interrupt.
Instead, a timer callback is used to read the data.

- Platform/variant porting:

The driver requires that following macros are defined in the variant.h
file of the specific variant:

  LPS22HB_I2C_BUS_ID    /* specify I2C Bus ID */
  LPS22HB_I2C_SPEED     /* specify I2C Bus speed in hz */
  LPS22HB_I2C_ADDR      /* specify device I2C address */
  LPS22HB_DBG_ENABLED   /* Enable debug messages */

Example:

  /*
   * Define platform/variant dependent LPS22HB device macros
   */

  #define LPS22HB_I2C_BUS_ID      0
  #define LPS22HB_I2C_SPEED       400000
  #define LPS22HB_I2C_ADDR        0x5C

If these macros are not defined in the current variant the driver forces a compilation
error.

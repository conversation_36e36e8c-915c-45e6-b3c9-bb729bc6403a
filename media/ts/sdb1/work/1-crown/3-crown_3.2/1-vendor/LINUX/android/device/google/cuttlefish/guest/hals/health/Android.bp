/*
 * Copyright (C) 2018 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
cc_library_shared {
    name: "android.hardware.health@2.1-impl-cuttlefish",
    stem: "android.hardware.health@2.0-impl-2.1-cuttlefish",
    proprietary: true,
    recovery_available: true,

    relative_install_path: "hw",

    srcs: [
        "health.cpp",
    ],

    cflags: [
        "-Wall",
        "-Werror",
    ],

    static_libs: [
        "android.hardware.health@1.0-convert",
        "libbatterymonitor",
        "libhealthloop",
        "libhealth2impl",
    ],

    shared_libs: [
        "libbase",
        "libcutils",
        "libhidlbase",
        "libutils",
        "android.hardware.health@2.0",
        "android.hardware.health@2.1",
    ],

    defaults: ["enabled_on_p_and_later"],
}

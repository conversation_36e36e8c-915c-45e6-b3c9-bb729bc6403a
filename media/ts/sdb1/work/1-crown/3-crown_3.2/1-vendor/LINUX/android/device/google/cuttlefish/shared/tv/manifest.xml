<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Copyright 2017, The Android Open Source Project.
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<manifest version="1.0" type="device" target-level="5">
     <!-- FIXME: Implement tv.cec HAL
     <hal format="hidl">
        <name>android.hardware.tv.cec</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IHdmiCec</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
    <!-- FIXME: Implement tv.input HAL
    <hal format="hidl">
        <name>android.hardware.tv.input</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>ITvInput</name>
            <instance>default</instance>
        </interface>
    </hal>
    -->
</manifest>

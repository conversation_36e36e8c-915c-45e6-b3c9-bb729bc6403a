<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/visible_panel"
    android:layout_width="480dp"
    android:layout_height="wrap_content"
    android:orientation="horizontal" >

    <LinearLayout
        android:id="@+id/slider_group"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical" />

    <ImageView
        android:id="@+id/expand_button_divider"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_gravity="top"
        android:layout_marginBottom="16dp"
        android:layout_marginTop="16dp"
        android:scaleType="fitXY"
        android:src="?attr/dividerVertical" />

    <ImageView
        android:id="@+id/expand_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top"
        android:background="?attr/selectableItemBackground"
        android:padding="16dp"
        android:src="@drawable/ic_sysbar_quicksettings" />

</LinearLayout>

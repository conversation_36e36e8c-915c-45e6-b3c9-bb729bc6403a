type setup_wifi, domain;
type setup_wifi_exec, exec_type, vendor_file_type, file_type;

init_daemon_domain(setup_wifi)

allow setup_wifi self:capability { net_admin net_raw sys_module };
allow setup_wifi self:udp_socket { create ioctl };
allow setup_wifi self:netlink_route_socket { bind create nlmsg_write read write };

allow setup_wifi kernel:system module_request;

type cuttlefish_wifi_mac_address, property_type;

get_prop(setup_wifi, cuttlefish_wifi_mac_address)

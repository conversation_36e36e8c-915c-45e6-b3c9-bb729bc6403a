# goldfish
/dev/block/mtdblock0         u:object_r:system_block_device:s0
/dev/block/mtdblock1         u:object_r:userdata_block_device:s0
/dev/block/mtdblock2         u:object_r:cache_block_device:s0

# ranchu
/dev/block/vda               u:object_r:system_block_device:s0
/dev/block/vdb               u:object_r:cache_block_device:s0
/dev/block/vdc               u:object_r:userdata_block_device:s0
/dev/block/vdd               u:object_r:metadata_block_device:s0
/dev/block/vde               u:object_r:system_block_device:s0
/dev/block/zram0             u:object_r:swap_block_device:s0

/dev/goldfish_pipe           u:object_r:qemu_device:s0
/dev/goldfish_sync           u:object_r:qemu_device:s0
/dev/goldfish_address_space  u:object_r:qemu_device:s0
/dev/qemu_.*                 u:object_r:qemu_device:s0
/dev/dri/card0               u:object_r:gpu_device:s0
/dev/dri/controlD64          u:object_r:gpu_device:s0
/dev/dri/renderD128          u:object_r:gpu_device:s0
/dev/ttyGF[0-9]*             u:object_r:serial_device:s0
/dev/ttyS2                   u:object_r:console_device:s0
/vendor/bin/init\.ranchu-core\.sh u:object_r:goldfish_setup_exec:s0
/vendor/bin/init\.ranchu-net\.sh u:object_r:goldfish_setup_exec:s0
/vendor/bin/init\.wifi\.sh   u:object_r:goldfish_setup_exec:s0
/vendor/bin/qemu-props       u:object_r:qemu_props_exec:s0
/vendor/bin/mac80211_create_radios u:object_r:mac80211_create_radios_exec:s0
/vendor/bin/createns         u:object_r:createns_exec:s0
/vendor/bin/execns           u:object_r:execns_exec:s0
/vendor/bin/ip               u:object_r:goldfish_ip_exec:s0
/vendor/bin/hw/libgoldfish-rild               u:object_r:rild_exec:s0
/vendor/bin/iw               u:object_r:goldfish_iw_exec:s0
/vendor/bin/dhcpclient       u:object_r:dhcpclient_exec:s0
/vendor/bin/hostapd_nohidl   u:object_r:hostapd_nohidl_exec:s0
/vendor/bin/netmgr           u:object_r:netmgr_exec:s0
/vendor/bin/wifi_forwarder   u:object_r:wifi_forwarder_exec:s0

/vendor/bin/hw/android\.hardware\.bluetooth@1\.1-service\.sim  u:object_r:hal_bluetooth_sim_exec:s0
/vendor/bin/hw/android\.hardware\.drm@[0-9]+\.[0-9]+-service\.widevine          u:object_r:hal_drm_widevine_exec:s0
/vendor/bin/hw/android\.hardware\.drm@[0-9]+\.[0-9]+-service\.clearkey          u:object_r:hal_drm_clearkey_exec:s0
/vendor/bin/hw/android\.hardware\.gatekeeper@1\.0-service.software    u:object_r:hal_gatekeeper_default_exec:s0
/vendor/bin/hw/android\.hardware\.thermal@2\.0-service.mock           u:object_r:hal_thermal_default_exec:s0
/vendor/bin/hw/android\.hardware\.authsecret@1\.0-service  u:object_r:hal_authsecret_default_exec:s0
/vendor/bin/hw/android\.hardware\.input\.classifier@1\.0-service.default  u:object_r:hal_input_classifier_default_exec:s0
/vendor/bin/hw/android\.hardware\.power\.stats@1\.0-service\.mock  u:object_r:hal_power_stats_default_exec:s0
/vendor/bin/hw/android\.hardware\.gnss@2\.0-service\.ranchu        u:object_r:hal_gnss_default_exec:s0
/vendor/bin/hw/android\.hardware\.neuralnetworks@1\.3-service-sample-.*   u:object_r:hal_neuralnetworks_sample_exec:s0
/vendor/bin/hw/android\.hardware\.audio\.service.ranchu   u:object_r:hal_audio_default_exec:s0

/vendor/lib(64)?/hw/vulkan\.ranchu\.so   u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libEGL_emulation\.so          u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libGLESv1_CM_emulation\.so    u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libGLESv2_emulation\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libOpenglCodecCommon\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libOpenglSystemCommon\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/lib_renderControl_enc\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libGLESv1_enc\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libGLESv2_enc\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libvulkan_enc\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libandroidemu\.so       u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/libdrm.so  u:object_r:same_process_hal_file:s0
/vendor/lib(64)?/hw/android\.hardware\.graphics\.mapper@3\.0-impl-ranchu\.so   u:object_r:same_process_hal_file:s0

# data
/data/vendor/mediadrm(/.*)?            u:object_r:mediadrm_vendor_data_file:s0
/data/vendor/var/run(/.*)?             u:object_r:varrun_file:s0

# not yet AOSP HALs
/vendor/bin/hw/android\.hardware\.camera\.provider@2\.6-service-google u:object_r:hal_camera_default_exec:s0
/vendor/bin/hw/android\.hardware\.rebootescrow-service\.default        u:object_r:hal_rebootescrow_default_exec:s0
/vendor/bin/hw/android\.hardware\.contexthub@1\.1-service\.mock        u:object_r:hal_contexthub_default_exec:s0

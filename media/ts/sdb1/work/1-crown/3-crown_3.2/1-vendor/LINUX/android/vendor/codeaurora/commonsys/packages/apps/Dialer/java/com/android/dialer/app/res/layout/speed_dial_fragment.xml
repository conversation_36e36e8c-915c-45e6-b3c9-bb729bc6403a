<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2012 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<FrameLayout
  xmlns:android="http://schemas.android.com/apk/res/android"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:clipChildren="false">

  <FrameLayout
    android:id="@+id/contact_tile_frame"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_alignParentLeft="true"
    android:layout_alignParentTop="true"
    android:paddingStart="@dimen/favorites_row_start_padding"
    android:paddingEnd="@dimen/favorites_row_end_padding">
    <com.android.dialer.app.list.PhoneFavoriteListView
      android:id="@+id/contact_tile_list"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:paddingTop="@dimen/favorites_row_top_padding"
      android:paddingBottom="@dimen/floating_action_button_list_bottom_padding"
      android:clipToPadding="false"
      android:divider="@null"
      android:fadingEdge="none"
      android:nestedScrollingEnabled="true"
      android:numColumns="@integer/contact_tile_column_count_in_favorites"/>
  </FrameLayout>

  <com.android.dialer.widget.EmptyContentView
    android:id="@+id/empty_list_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:visibility="gone"/>

</FrameLayout>

<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
  <string name="call_custom">Call %s</string>
  <string name="call_home">Call home</string>
  <string name="call_mobile">Call mobile</string>
  <string name="call_work">Call work</string>
  <string name="call_fax_work">Call work fax</string>
  <string name="call_fax_home">Call home fax</string>
  <string name="call_pager">Call pager</string>
  <string name="call_other">Call</string>
  <string name="call_callback">Call callback</string>
  <string name="call_car">Call car</string>
  <string name="call_company_main">Call company main</string>
  <string name="call_isdn">Call ISDN</string>
  <string name="call_main">Call main</string>
  <string name="call_other_fax">Call fax</string>
  <string name="call_radio">Call radio</string>
  <string name="call_telex">Call telex</string>
  <string name="call_tty_tdd">Call TTY/TDD</string>
  <string name="call_work_mobile">Call work mobile</string>
  <string name="call_work_pager">Call work pager</string>
  <string name="call_assistant">Call %s</string>
  <string name="call_mms">Call MMS</string>
  <string name="sms_custom">Text %s</string>
  <string name="sms_home">Text home</string>
  <string name="sms_mobile">Text mobile</string>
  <string name="sms_work">Text work</string>
  <string name="sms_fax_work">Text work fax</string>
  <string name="sms_fax_home">Text home fax</string>
  <string name="sms_pager">Text pager</string>
  <string name="sms_other">Text</string>
  <string name="sms_callback">Text callback</string>
  <string name="sms_car">Text car</string>
  <string name="sms_company_main">Text company main</string>
  <string name="sms_isdn">Text ISDN</string>
  <string name="sms_main">Text main</string>
  <string name="sms_other_fax">Text fax</string>
  <string name="sms_radio">Text radio</string>
  <string name="sms_telex">Text telex</string>
  <string name="sms_tty_tdd">Text TTY/TDD</string>
  <string name="sms_work_mobile">Text work mobile</string>
  <string name="sms_work_pager">Text work pager</string>
  <string name="sms_assistant">Text %s</string>
  <string name="sms_mms">Text MMS</string>
  <string name="clearFrequentsConfirmation_title">Clear frequently contacted?</string>
  <string name="clearFrequentsConfirmation">You\'ll clear the frequently contacted list in the Contacts and Phone apps, and force email apps to learn your addressing preferences from scratch.</string>
  <string name="clearFrequentsProgress_title">Clearing frequently contacted…</string>
  <string name="status_available">Available</string>
  <string name="status_away">Away</string>
  <string name="status_busy">Busy</string>
  <string name="contactsList">Contacts</string>
  <string name="contact_suggestions">Top suggested</string>
  <string name="local_invisible_directory">Other</string>
  <string name="directory_search_label">Directory</string>
  <string name="directory_search_label_work">Work directory</string>
  <string name="local_search_label">All contacts</string>
  <string name="local_suggestions_search_label">Suggestions</string>
  <string msgid="9154761216179882405" name="user_profile_contacts_list_header">Me</string>
  <string name="missing_name">(No name)</string>
  <string msgid="2795575601596468581" name="description_view_contact_detail">View contact</string>
  <string name="list_filter_phones">All contacts with phone numbers</string>
  <string name="list_filter_phones_work">Work profile contacts</string>
  <string name="view_updates_from_group">View updates</string>
  <string name="account_phone">Device only, unsynced</string>
  <string name="nameLabelsGroup">Name</string>
  <string name="nicknameLabelsGroup">Nickname</string>
  <string name="full_name">Name</string>
  <string name="name_given">First name</string>
  <string name="name_family">Surname</string>
  <string name="name_prefix">Name prefix</string>
  <string name="name_middle">Middle name</string>
  <string name="name_suffix">Name suffix</string>
  <string name="name_phonetic">Phonetic name</string>
  <string name="name_phonetic_given">Phonetic first name</string>
  <string name="name_phonetic_middle">Phonetic middle name</string>
  <string name="name_phonetic_family">Phonetic surname</string>
  <string name="phoneLabelsGroup">Phone</string>
  <string name="emailLabelsGroup">Email</string>
  <string name="postalLabelsGroup">Address</string>
  <string name="imLabelsGroup">IM</string>
  <string name="organizationLabelsGroup">Organisation</string>
  <string name="relationLabelsGroup">Relationship</string>
  <string name="eventLabelsGroup">Special date</string>
  <string name="sms">Text message</string>
  <string name="postal_address">Address</string>
  <string name="ghostData_company">Company</string>
  <string name="ghostData_title">Title</string>
  <string name="label_notes">Notes</string>
  <string name="label_sip_address">SIP</string>
  <string name="websiteLabelsGroup">Website</string>
  <string name="groupsLabel">Groups</string>
  <string name="email_home">Email home</string>
  <string name="email_mobile">Email mobile</string>
  <string name="email_work">Email work</string>
  <string name="email_other">Email</string>
  <string name="email_custom">Email %s</string>
  <string name="email">Email</string>
  <string name="postal_street">Street</string>
  <string name="postal_city">City</string>
  <string name="postal_region">County</string>
  <string name="postal_postcode">Postcode</string>
  <string name="postal_country">Country</string>
  <string name="map_home">View home address</string>
  <string name="map_work">View work address</string>
  <string name="map_other">View address</string>
  <string name="map_custom">View %s address</string>
  <string name="chat_aim">Chat using AIM</string>
  <string name="chat_msn">Chat using Windows Live</string>
  <string name="chat_yahoo">Chat using Yahoo</string>
  <string name="chat_skype">Chat using Skype</string>
  <string name="chat_qq">Chat using QQ</string>
  <string name="chat_gtalk">Chat using Google Talk</string>
  <string name="chat_icq">Chat using ICQ</string>
  <string name="chat_jabber">Chat using Jabber</string>
  <string name="chat">Chat</string>
  <string name="list_filter_all_accounts">All contacts</string>
  <string name="listAllContactsInAccount">Contacts in %s</string>
  <string name="listCustomView">Contacts in customised view</string>
  <string name="listSingleContact">Single contact</string>
  <string name="display_options_sort_list_by">Sort by</string>
  <string name="display_options_sort_by_given_name">First name</string>
  <string name="display_options_sort_by_family_name">Surname</string>
  <string name="display_options_view_names_as">Name format</string>
  <string name="display_options_view_given_name_first">First name first</string>
  <string name="display_options_view_family_name_first">Surname first</string>
  <string name="menu_clear_frequents">Clear frequents</string>
  <string name="action_menu_back_from_search">stop searching</string>
  <string name="description_clear_search">Clear search</string>
  <string name="select_account_dialog_title">Account</string>
  <string name="set_default_account">Always use this for calls</string>
  <string name="select_phone_account_for_calls">Choose SIM for this call</string>
  <string name="select_phone_account_for_calls_remember">Remember this choice</string>
  <string name="call_with_a_note">Call with a note</string>
  <string name="call_subject_hint">Type a note to send with call ...</string>
  <string name="send_and_call_button">SEND &amp; CALL</string>
  <string name="call_subject_limit">%1$s / %2$s</string>
  <string name="old_call_subject_type_and_number">%1$s %2$s</string>
  <string name="tab_title">%1$s tab.</string>
  <plurals name="tab_title_with_unread_items">
    <item quantity="one"> %1$s tab. %2$d unread item. </item>
    <item quantity="other"> %1$s tab. %2$d unread items. </item>
  </plurals>
  <string name="description_search_video_call">Video call</string>
  <string name="description_search_call_and_share">Share and call</string>
</resources>

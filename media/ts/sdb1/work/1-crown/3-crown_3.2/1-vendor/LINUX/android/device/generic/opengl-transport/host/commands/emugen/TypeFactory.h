/*
* Copyright (C) 2011 The Android Open Source Project
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
* http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/
#ifndef __TYPE__FACTORY__H__
#define __TYPE__FACTORY__H__

#include <string>
#include "VarType.h"

class TypeFactory {
public:
    static TypeFactory *instance() {
        if (m_instance == NULL) {
            m_instance = new TypeFactory;
        }
        return m_instance;
    }
    const VarType * getVarTypeByName(const std::string &type);
    int  initFromFile(const std::string &filename);
private:
    static TypeFactory *m_instance;
    void initBaseTypes();
    TypeFactory() {}
};
#endif

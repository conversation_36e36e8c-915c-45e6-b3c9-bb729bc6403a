/*
 * Copyright 2019, The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include "interface_state.h"

#include <stddef.h>

class Bridge;

class BridgeBuilder {
public:
    // Construct a bridge builder that will add any interface that comes up to
    // |bridge| if the interface name begins with |interfacePrefix|.
    BridgeBuilder(Bridge& bridge, const char* interfacePrefix);

    void onInterfaceState(unsigned int index,
                          const char* name,
                          InterfaceState state);

private:
    Bridge& mBridge;
    const char* mInterfacePrefix;
    size_t mPrefixLength;
};

/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

SECTIONS
{
	/* things that live in the main app image */
	.text : {
		KEEP (*(.vectors) ) ;
		*(.text) *(.text.*) ;
		*(.rodata) *(.rodata.*) ;
		. = ALIGN(8);
		__internal_app_start = ABSOLUTE(.);
		KEEP (*(.internal_app_init) ) ;
		__internal_app_end = ABSOLUTE(.);
		. = ALIGN(4);
		__text_end = ABSOLUTE(.);
	} > code = 0xff

	.stack (NOLOAD) : {
		. = ALIGN(4);
		__stack_bottom = ABSOLUTE(.);
		KEEP ( *(.stack) );
		KEEP ( *(.stack.*) );
		. = ALIGN(4);
		__stack_top = ABSOLUTE(.);
	 } > ram

	.data : {
		. = ALIGN(4);
		__data_start = ABSOLUTE(.);
		*(.data);
		*(.data.*);
		. = ALIGN(4);
		__data_end = ABSOLUTE(.);
	 } > ram AT > code

	.bss : {
		. = ALIGN(4);
		__bss_start = ABSOLUTE(.);
		*(.bss) *(.bss.*) *(COMMON);
		. = ALIGN(4);
		__bss_end = ABSOLUTE(.);
	} > ram

	.neverinit : {
		. = ALIGN(4);
		*(.neverinit) *(.neverinit.*) ;
		. = ALIGN(4);
		__heap_start = ABSOLUTE(.);
	} > ram
}

_BL = ORIGIN(bl);
__app_start = __data_data + __data_end - __data_start;
__heap_end = ORIGIN(ram) + LENGTH(ram);
ENTRY(ResetISR)

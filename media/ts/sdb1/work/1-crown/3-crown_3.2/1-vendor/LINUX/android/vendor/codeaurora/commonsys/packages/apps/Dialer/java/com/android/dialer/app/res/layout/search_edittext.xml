<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2017 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<view xmlns:android="http://schemas.android.com/apk/res/android"
  android:id="@+id/search_view_container"
  class="com.android.dialer.app.widget.SearchEditTextLayout"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:layout_marginTop="@dimen/search_top_margin"
  android:layout_marginBottom="@dimen/search_bottom_margin"
  android:layout_marginLeft="@dimen/search_margin_horizontal"
  android:layout_marginRight="@dimen/search_margin_horizontal"
  android:background="@drawable/rounded_corner"
  android:elevation="@dimen/search_box_elevation"
  android:theme="@style/DialtactsSearchBarThemeOverlay"
  android:orientation="horizontal">

  <RelativeLayout
    android:id="@+id/search_box_collapsed"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/selectableItemBackground"
    android:gravity="center_vertical">

    <ImageView
      android:id="@+id/search_magnifying_glass"
      android:layout_width="@dimen/search_box_icon_size"
      android:layout_height="@dimen/search_box_icon_size"
      android:layout_marginStart="8dp"
      android:layout_centerVertical="true"
      android:importantForAccessibility="no"
      android:scaleType="center"
      android:src="@drawable/quantum_ic_search_vd_theme_24"
      android:tint="?colorIcon"/>

    <TextView
      android:id="@+id/search_box_start_search"
      android:layout_width="wrap_content"
      android:layout_height="match_parent"
      android:layout_toEndOf="@+id/search_magnifying_glass"
      android:layout_toStartOf="@+id/voice_search_button"
      android:layout_marginStart="8dp"
      android:fontFamily="@string/search_font_family"
      android:gravity="center_vertical"
      android:hint="@string/dialer_hint_find_contact"
      android:textSize="@dimen/search_collapsed_text_size"/>

    <ImageView
      android:id="@+id/voice_search_button"
      android:layout_width="@dimen/search_box_icon_size"
      android:layout_height="match_parent"
      android:layout_toStartOf="@+id/dialtacts_options_menu_button"
      android:background="?android:attr/selectableItemBackgroundBorderless"
      android:clickable="true"
      android:contentDescription="@string/description_start_voice_search"
      android:scaleType="center"
      android:src="@drawable/ic_mic_grey600"
      android:tint="?colorIcon"/>

    <ImageButton
      android:id="@+id/dialtacts_options_menu_button"
      android:layout_width="@dimen/search_box_icon_size"
      android:layout_height="match_parent"
      android:layout_alignParentEnd="true"
      android:background="?android:attr/selectableItemBackgroundBorderless"
      android:contentDescription="@string/action_menu_overflow_description"
      android:scaleType="center"
      android:src="@drawable/quantum_ic_more_vert_white_24"
      android:tint="?colorIcon"/>
  </RelativeLayout>

  <include layout="@layout/search_bar_expanded"/>
</view>

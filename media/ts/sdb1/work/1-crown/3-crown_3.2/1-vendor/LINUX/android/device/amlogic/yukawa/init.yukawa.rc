import init.yukawa.usb.rc

on early-init
    # mount debugfs
    mount debugfs /sys/kernel/debug /sys/kernel/debug mode=755

on init
    # Support legacy paths
    symlink /sdcard /mnt/sdcard
    symlink /sdcard /storage/sdcard0
    write /proc/sys/vm/page-cluster 0

on fs
    mount_all /vendor/etc/fstab.yukawa
    swapon_all /vendor/etc/fstab.yukawa

on post-fs
# fake some battery state
    setprop status.battery.state Slow
    setprop status.battery.level 5
    setprop status.battery.level_raw  50
    setprop status.battery.level_scale 9
    setprop ro.hardware.hwcomposer drm_meson

on zygote-start
    mkdir /data/vendor/wifi 0770 wifi wifi
    mkdir /data/vendor/wifi/wpa 0770 wifi wifi
    mkdir /data/vendor/wifi/wpa/sockets 0770 wifi wifi

service wpa_supplicant /system/vendor/bin/hw/wpa_supplicant \
     -g@android:wpa_wlan0
     interface android.hardware.wifi.supplicant@1.0::ISupplicant default
     interface android.hardware.wifi.supplicant@1.1::ISupplicant default
     socket wpa_wlan0 dgram 660 wifi wifi
     class main
     disabled
     oneshot

service bugreport /system/bin/dumpstate -d -p -z
    class main
    disabled
    oneshot

<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="80dp"
    android:layout_marginBottom="8dp"
    android:layout_marginTop="8dp"
    android:gravity="left|center_vertical"
    android:orientation="horizontal" >

    <ImageView
        android:id="@+id/stream_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:padding="16dp" />

    <SeekBar
        android:id="@+id/seekbar"
        style="?android:attr/seekBarStyle"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginRight="16dp"
        android:layout_weight="1"
        android:background="@android:color/transparent"
        android:padding="16dp"
        android:progressDrawable="@android:drawable/progress_volume"
        android:thumb="@null" />

</LinearLayout>

<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<resources>
    <!-- Flag indicating that the media framework should support playing of sounds on volume
         key usage.  This adds noticeable additional overhead to volume key processing, so
         is disableable for products for which it is irrelevant. -->
    <bool name="config_useVolumeKeySounds">false</bool>

    <!-- Disable AUDIO_BECOMING_NOISY notifications. -->
    <bool name="config_sendAudioBecomingNoisy">false</bool>

    <!-- This device is data-only. -->
    <bool name="config_voice_capable">false</bool>

    <!-- This device does not allow sms service. -->
    <bool name="config_sms_capable">false</bool>

    <!-- Control the default UI mode type to use when there is no other type override
         happening.  One of the following values (See Configuration.java):
             1  UI_MODE_TYPE_NORMAL
             4  UI_MODE_TYPE_TELEVISION
             5  UI_MODE_TYPE_APPLIANCE
         Any other values will have surprising consequences. -->
    <integer name="config_defaultUiModeType">4</integer>

    <!--  Control whether to lock UI mode to what is selected from config_defaultUiModeType.
          Once UI mode is locked, applications cannot change it anymore. -->
    <bool name="config_lockUiMode">true</bool>

    <!-- default device has recents property -->
    <bool name="config_hasRecents">false</bool>

    <!-- Control the behavior when the user long presses the home button.
            0 - Nothing
            1 - Launch all apps intent
            2 - Launch assist intent
         This needs to match the constants in
         policy/src/com/android/internal/policy/impl/PhoneWindowManager.java
    -->
    <integer name="config_longPressOnHomeBehavior">1</integer>

    <!-- Override configuration check for dpad so that we always appear to have one -->
    <bool name="config_hasPermanentDpad">true</bool>

    <bool name="config_defaultInTouchMode">false</bool>

    <!-- Launcher customization requires AppWidgetService, but otherwise
         home screen widgets are not supported on TV -->
    <bool name="config_enableAppWidgetService">true</bool>

    <!-- Whether to keep background restricted profiles running after exiting. If set to false,
         restricted profiles may be put into stopped state as soon as the switch back to primary
         happens.
         Can be overridden with android.provider.Settings.Global.KEEP_PROFILE_IN_BACKGROUND. -->
    <bool name="config_keepRestrictedProfilesInBackground">false</bool>

    <!-- Enable doze mode -->
    <bool name="config_enableAutoPowerModes">true</bool>
    <bool name="config_autoPowerModeUseMotionSensor">false</bool>

    <!-- True if the device supports split screen as a form of multi-window. -->
    <bool name="config_supportsSplitScreenMultiWindow">false</bool>

    <!-- Whether the device supports quick settings and its associated APIs -->
    <bool name="config_quickSettingsSupported">false</bool>

    <!-- True if the device supports system decorations on secondary displays. -->
    <bool name="config_supportsSystemDecorsOnSecondaryDisplays">false</bool>

    <!-- Enable assistant to show in front of the dream/screensaver. -->
    <bool name="config_assistantOnTopOfDream">true</bool>

    <!-- Maximum size, specified in pixels, to restrain the display space width to. Height and
         density will be scaled accordingly to maintain aspect ratio. A value of 0 indicates no
         constraint will be enforced.
         We limit the UI graphics width to 1920 because higher resolution is unnecessary and causes
         too much overhead on the GPU for Android TV devices. -->
    <integer name="config_maxUiWidth">1920</integer>
</resources>

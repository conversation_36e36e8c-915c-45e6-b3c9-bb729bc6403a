#AP6398S_NVRAM_V1.1_20170926
# BCM4359 WLBGA iPA, iLNA board for bringup -AP6359SA_V1.0NVRAM
NVRAMRev=$Rev: 528206 $
cckdigfilttype=5
#cckdigfilttype=4 (default)
#valid ofdm filter types are 0 and 1
ofdmfilttype_2gbe=127
ofdmfilttype_5gbe=127
sromrev=11
boardrev=0x1301
boardtype=0x0812
# JIRA:SW4349-945 MANDATORY! Update makefile in case you touch bfl
#boardflags=0x10081201
boardflags=0x00480201
boardflags2=0x40801000
boardflags3=0x48700106
#boardnum=57410 
macaddr=00:90:4c:27:80:01
ccode=0
regrev=0
antswitch=0
pdgain5g=0
pdgain2g=0
lowpowerrange2g=0
lowpowerrange5g=0
tworangetssi2g=0
tworangetssi5g=0
# Low Power Range start value: 0dBm
olpc_thresh2g=0
olpc_thresh5g=0
AvVmid_c0=2,130,2,130,2,130,2,130,2,130
AvVmid_c1=2,130,2,130,2,130,2,130,2,130
# JIRA:SW4349-945 MANDATORY! Update makefile in case you touch femctl
femctrl=14
vendid=0x14e4
devid=0x43ef
manfid=0x2d0
#prodid=0x052e
nocrc=1
btc_mode=1
#btc_params82=0x1a0
otpimagesize=502
xtalfreq=37400
rxgains2gelnagaina0=3
rxgains2gtrisoa0=7
rxgains2gtrelnabypa0=1
rxgains5gelnagaina0=3
rxgains5gtrisoa0=6
rxgains5gtrelnabypa0=1
rxgains5gmelnagaina0=3
rxgains5gmtrisoa0=6
rxgains5gmtrelnabypa0=1
rxgains5ghelnagaina0=3
rxgains5ghtrisoa0=6
rxgains5ghtrelnabypa0=1
rxgains2gelnagaina1=3
rxgains2gtrisoa1=7
rxgains2gtrelnabypa1=1
rxgains5gelnagaina1=3
rxgains5gtrisoa1=6
rxgains5gtrelnabypa1=1
rxgains5gmelnagaina1=3
rxgains5gmtrisoa1=6
rxgains5gmtrelnabypa1=1
rxgains5ghelnagaina1=3
rxgains5ghtrisoa1=6
rxgains5ghtrelnabypa1=1
rxchain=3
txchain=3
aa2g=3
aa5g=3
agbg0=2
agbg1=2
aga0=2
aga1=2
tssipos2g=1
extpagain2g=2
tssipos5g=1
extpagain5g=2
tempthresh=255
tempoffset=255
rawtempsense=0x1ff
fdss_interp_en=1
#fdss_level_2g=3,3
fdss_level_5g=4,4
#pa2gccka0=-186,8076,-976
#pa2gccka1=-217,7061,-881
#pa2gccka2=-67,9864,-1253
#pa2gccka3=-115,9164,-1225
#pa2ga0=-196,6950,-832
#pa2ga1=-204,6710,-809
#pa2ga2=-220,4557,-593
#pa2ga3=-218,4596,-601
pa2ga0=-193,7335,-862
pa2ga1=-202,6968,-828
pa2ga2=-220,4685,-607
pa2ga3=-218,4724,-615
#pa5ga0=-191,6865,-844,-169,7525,-907,-168,7768,-938,-192,7073,-871
#pa5ga1=-182,7580,-919,-188,7614,-931,-219,6536,-818,-202,7220,-895
#pa5ga2=-220,4437,-628,-183,5005,-678,-229,4048,-551,-223,4448,-611
#pa5ga3=-263,3914,-566,-224,4649,-640,-230,4385,-596,-154,6488,-866
pa5ga0=-205,6664,-820,-201,6801,-835,-199,6767,-831,-178,7266,-873
pa5ga1=-200,7025,-858,-193,7170,-871,-186,7290,-879,-187,7227,-873
pa5ga2=-220,4616,-647,-183,5184,-694,-229,4227,-571,-223,4627,-631
pa5ga3=-263,4170,-599,-224,4905,-668,-230,4641,-625,-154,6744,-885
#pa5gbw4080a0=-201,6883,-859,-198,7088,-881,-202,6968,-870,-210,6522,-820
#pa5gbw4080a1=-217,6626,-832,-201,7517,-932,-201,7251,-896,-184,7500,-917
#pa5gbw4080a2=-272,3585,-525,-193,5404,-740,-229,4201,-572,-230,4036,-550
#pa5gbw4080a3=-278,3361,-486,-230,4794,-662,-268,3605,-508,-276,3337,-478
maxp2ga0=74
maxp2ga1=74
maxp5ga0=70,70,70,70
maxp5ga1=70,70,71,70
subband5gver=0x4
paparambwver=3
pdoffset2g40mvalid=0
cckpwroffset0=0x3
cckpwroffset1=0x3
pdoffset2g40ma0=0x2
pdoffset2g40ma1=0x3
pdoffset40ma0=0x0022
pdoffset80ma0=0xceff
pdoffset40ma1=0x0123
pdoffset80ma1=0xdfff
cckbw202gpo=0
cckbw20ul2gpo=0
mcsbw202gpo=0x44444444
mcsbw402gpo=0x44444444
dot11agofdmhrbw202gpo=0x2222
ofdmlrbw202gpo=0x0000
mcsbw205glpo=0x44444444
mcsbw405glpo=0x44444444
mcsbw805glpo=0xCCCCCCCC
mcsbw1605glpo=0
mcsbw205gmpo=0x44444444
mcsbw405gmpo=0x44444444
mcsbw805gmpo=0xCCCCCCCC
mcsbw1605gmpo=0
mcsbw205ghpo=0x44444444
mcsbw405ghpo=0x44444444
mcsbw805ghpo=0xCCCCCCCC
mcsbw1605ghpo=0
mcslr5glpo=0x0000
mcslr5gmpo=0x0000
mcslr5ghpo=0x0000
sb20in40hrpo=0x0
sb20in80and160hr5glpo=0x0
sb40and80hr5glpo=0x0
sb20in80and160hr5gmpo=0x0
sb40and80hr5gmpo=0x0
sb20in80and160hr5ghpo=0x0
sb40and80hr5ghpo=0x0
sb20in40lrpo=0x0
sb20in80and160lr5glpo=0x0
sb40and80lr5glpo=0x0
sb20in80and160lr5gmpo=0x0
sb40and80lr5gmpo=0x0
sb20in80and160lr5ghpo=0x0
sb40and80lr5ghpo=0x0
dot11agduphrpo=0x0
dot11agduplrpo=0x0
phycal_tempdelta=255
temps_period=15
temps_hysteresis=15
ltecxmux=0
ltecxpadnum=0x0504
ltecxfnsel=0x44
ltecxgcigpio=0x04
#OOB params
#device_wake_opt=1
#host_wake_opt=0
swctrlmap_2g=0x00000808,0x00001010,0x00001010,0x021010,0x3ff
swctrlmapext_2g=0x00000000,0x00000000,0x00000000,0x000000,0x003
swctrlmap_5g=0x00004040,0x00000000,0x00000000,0x000000,0x3e5								
swctrlmapext_5g=0x00000000,0x00000101,0x00000101,0x000000,0x003
fem_table_init_val=0x00001010,0x00000000
rssi_delta_5gl_c0=3,3,2,2,5,5
rssi_delta_5gml_c0=0,2,0,2,3,5
rssi_delta_5gmu_c0=0,2,0,2,3,5
rssi_delta_5gh_c0=2,5,2,5,5,8
rssi_delta_5gl_c1=1,1,2,2,3,3
rssi_delta_5gml_c1=-1,1,0,2,1,3
rssi_delta_5gmu_c1=-1,1,0,2,1,3
rssi_delta_5gh_c1=0,3,2,5,3,6
rssi_delta_2g_c0=4,5,4,5
rssi_delta_2g_c1=2,3,2,3
#muxenab=1
#avs_enab=1

# ###########  BTC Dynctl profile params  ############
# flags:bit0 - dynctl enabled, bit1 dynamic desense, bit2 dynamic mode
btcdyn_flags=0x0
#btcdyn_dflt_dsns_level=0
#btcdyn_low_dsns_level=0
#btcdyn_mid_dsns_level=7
#btcdyn_high_dsns_level=2
#btcdyn_default_btc_mode=5
#btcdyn_btrssi_hyster=2
# --- number of rows in the array vars below ---
#btcdyn_msw_rows=3
#btcdyn_dsns_rows=2
# --- mode switch data rows (max is 4) ---
#btcdyn_msw_row0=1,8,0,-50,-100
#btcdyn_msw_row1=1,4,0,-55,-100
#btcdyn_msw_row2=1,0,0,-70,-100
#btcdyn_msw_row3=1,-4,0,-70,-100
# --- desense switching data rows (max is 4) ---
#btcdyn_dsns_row0=5,8,0,-40,-40
#btcdyn_dsns_row0=5,4,0,-60,-60
#btcdyn_dsns_row1=5,0,0,0,-75
powoffs2gtna0=1,3,3,1,0,0,1,2,2,2,1,1,0,0
powoffs2gtna1=-1,1,1,1,0,0,1,2,3,2,2,0,0,0
#new Jan 4th
#eps_shift0=-1,-6,-1,-5
#eps_shift1=-4,-6,-1,-2
#eps_shift2=-1,9,-2,-6
muxenab=0x10

#bandedge
fdss_level_2g=4,4
fdss_level_5g=5,5
fdss_interp_en=1

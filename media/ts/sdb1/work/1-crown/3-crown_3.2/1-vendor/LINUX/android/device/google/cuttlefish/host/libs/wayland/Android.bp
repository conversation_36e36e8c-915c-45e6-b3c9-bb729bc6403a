//
// Copyright (C) 2019 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

cc_library_host_static {
    name: "libcuttlefish_wayland_server",
    srcs: [
        "wayland_compositor.cpp",
        "wayland_dmabuf.cpp",
        "wayland_seat.cpp",
        "wayland_shell.cpp",
        "wayland_server.cpp",
        "wayland_subcompositor.cpp",
        "wayland_surface.cpp",
    ],
    shared_libs: [
        "libbase",
        "libcuttlefish_fs",
        "liblog",
    ],
    static_libs: [
        "libdrm",
        "libffi",
        "libwayland_server",
        "libwayland_extension_server_protocols",
    ],
    defaults: ["cuttlefish_host_only"],
}

